# AlertComm System - Login Credentials

## 🔐 Test User Accounts

### Administrator Access
```
Username: admin
Password: password123
Role: System Administrator
Access: Full system access, user management, settings
```

### Command Staff
```
Username: commander1
Password: password123
Role: Chief Commander
Access: Event management, resource allocation, command functions
```

### Medical Personnel
```
Username: medic1
Password: password123
Role: Paramedic
Location: Station 1
Access: Medical events, patient care, transport

Username: medic2
Password: password123
Role: Paramedic  
Location: Station 2
Access: Medical events, patient care, transport

Username: emt1
Password: password123
Role: EMT
Location: Station 1
Access: Basic medical events, transport assistance

Username: nurse1
Password: password123
Role: Registered Nurse
Location: Hospital Center
Access: Hospital events, patient care
```

### Fire Department
```
Username: firefighter1
Password: password123
Role: Firefighter
Location: Station 1
Access: Fire events, rescue operations, hazmat
```

### Support Staff
```
Username: driver1
Password: password123
Role: Driver/EMT
Location: Station 3
Access: Transport, basic medical support

Username: dispatcher1
Password: password123
Role: Emergency Dispatcher
Location: Headquarters
Access: Event creation, communication, coordination

Username: supervisor1
Password: password123
Role: Field Supervisor
Location: Station 2
Access: Field operations, staff coordination
```

## 🏢 System Information

### Database
```
Database Name: alertcomm1
Default User: postgres
Tables: 27 total
Test Data: Fully populated
```

### Company Details
```
Company Name: AlertComm Emergency Services
Company Code: ALERT001
Slug: alertcomm
```

### Server Endpoints
```
Backend API: http://localhost:3000
Frontend App: http://localhost:3001
Socket.io: http://localhost:3000 (WebSocket)
```

## 🎯 Role Permissions

### Admin
- ✅ Full system access
- ✅ User management
- ✅ System settings
- ✅ All modules
- ✅ Database management

### Commander
- ✅ Event management
- ✅ Resource allocation
- ✅ Staff assignments
- ✅ Command functions
- ✅ Reporting

### Staff (Medic/EMT/Firefighter)
- ✅ Assigned events
- ✅ Task management
- ✅ Location updates
- ✅ Chat communication
- ✅ Status updates

### Dispatcher
- ✅ Event creation
- ✅ Communication
- ✅ Resource tracking
- ✅ Coordination
- ✅ Notifications

### Supervisor
- ✅ Field operations
- ✅ Staff coordination
- ✅ Local resource management
- ✅ Incident oversight

## 📱 Quick Login Guide

1. **Open** `http://localhost:3001`
2. **Enter** username and password from above
3. **Click** Login
4. **Explore** the dashboard based on your role

## 🔄 Password Reset

All test accounts use simple passwords for development. In production:
- Use strong passwords
- Enable password reset functionality
- Implement proper security measures

## 🚨 Emergency Test Scenarios

### Test as Commander (commander1)
- Create new emergency events
- Assign resources and personnel
- Monitor real-time responses
- Use command dashboard

### Test as Medic (medic1)
- Respond to medical emergencies
- Update patient status
- Use chat communication
- Track location updates

### Test as Dispatcher (dispatcher1)
- Create and dispatch events
- Coordinate multiple units
- Monitor system-wide status
- Manage communications

---

**⚠️ Important:** These are test credentials for development only. Change all passwords before production deployment!
