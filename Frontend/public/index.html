<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Web site created using create-react-app" />
    <title>AlertComm</title>
    <!-- Load Google Maps API statically -->
    <script>
      window.initMap = function() {
        console.log('Google Maps API loaded via frontend index.html');
        window.mapsLoaded = true; // Flag for components
      };
    </script>
    <!-- Load new Google Maps Places API with PlaceAutocompleteElement -->
    <script type="importmap">
      {
        "imports": {
          "@googlemaps/places": "https://unpkg.com/@googlemaps/places@1.0.0/dist/index.min.js"
        }
      }
    </script>
    <script type="module">
      import { PlaceAutocompleteElement } from "@googlemaps/places";
      customElements.define("gmp-place-autocomplete-element", PlaceAutocompleteElement);
    </script>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>