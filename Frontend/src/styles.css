/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
    color: #333;
    line-height: var(--line-height-normal);
    font-size: var(--font-size-base);
    min-height: 100vh;
}

/* Color Variables */
:root {
    /* Colors */
    --primary: #667eea; /* Modern purple-blue */
    --secondary: #764ba2; /* Deep purple */
    --accent: #e0e7ff; /* Light blue accent */
    --danger: #dc3545; /* Red for alerts */
    --success: #28a745; /* Green for success */
    --gray-dark: #2d3748; /* Dark gray for text */
    --gray-light: #e2e8f0; /* Light gray for backgrounds */
    --white: #ffffff;
    --glass-bg: rgba(255, 255, 255, 0.95);
    --glass-border: rgba(255, 255, 255, 0.2);
    
    /* Font Size Variables - These will be dynamically updated by JavaScript */
    --font-size-scale: 1; /* Default scale, will be updated by user preference */
    --font-size-xs: calc(0.75rem * var(--font-size-scale));
    --font-size-sm: calc(0.875rem * var(--font-size-scale));
    --font-size-base: calc(1rem * var(--font-size-scale));
    --font-size-lg: calc(1.125rem * var(--font-size-scale));
    --font-size-xl: calc(1.25rem * var(--font-size-scale));
    --font-size-2xl: calc(1.5rem * var(--font-size-scale));
    --font-size-3xl: calc(1.875rem * var(--font-size-scale));
    --font-size-4xl: calc(2.25rem * var(--font-size-scale));
    
    /* Line Height Variables */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;
}

/* Modern Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Modern Card Styles */
.modern-card {
    animation: fadeInUp 0.6s ease-out;
    transition: all 0.3s ease;
}

.modern-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 30px 60px rgba(0,0,0,0.2) !important;
}

.dashboard-header {
    animation: fadeInUp 0.8s ease-out;
}

.status-section {
    animation: fadeInUp 1s ease-out;
}

.loading-card {
    animation: pulse 2s infinite;
}

/* Navigation Bar */
nav {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    padding: 1rem 2rem;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

nav a, nav button {
    color: var(--primary);
    text-decoration: none;
    font-weight: 600;
    margin-right: 1.5rem;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 12px;
}

nav a:hover, nav button:hover {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

nav button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1.5rem;
}

/* Responsive Containers */
@media (max-width: 768px) {
    .container {
        margin: 1rem auto;
        padding: 0 1rem;
    }
}

@media (max-width: 480px) {
    .container {
        margin: 0.5rem auto;
        padding: 0 0.75rem;
    }
}

.delete-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
  
  .delete-modal {
    background: #fff;
    padding: 2rem;
    border-radius: 8px;
    max-width: 800px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
  }


  .toggle-label {
    display: inline-block;
    position: relative;
    width: 40px;
    height: 20px;
  }
  
  .toggle-input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .toggle-track {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    border-radius: 20px;
    transition: background-color 0.2s;
  }
  
  .toggle-input:checked + .toggle-track {
    background-color: #2563eb;
  }
  
  .toggle-track:before {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    background-color: white;
    border-radius: 50%;
    top: 2px;
    left: 2px;
    transition: transform 0.2s;
  }
  
  .toggle-input:checked + .toggle-track:before {
    transform: translateX(20px);
  }

/* Cards */
.card {
    background: var(--white);
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 2rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    
    overflow-x: auto;
}

.user-management-card{
    background: var(--white);
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 2rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    overflow-x: auto;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.card h2 {
    color: var(--primary);
    /* margin-bottom: 1rem; */
    margin-bottom: 0rem;
    font-size: 1.3rem;
}

/* Buttons */
.btn-primary, .btn-secondary, .btn-danger {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease, transform 0.2s ease;
}

.btn-primary {
    background: var(--primary);
    color: var(--white);
}

.btn-primary:hover {
    background: #153057;
    transform: scale(1.05);
}

.btn-secondary {
    background: var(--secondary);
    color: var(--white);
}

.btn-secondary:hover {
    background: #008d97;
    transform: scale(1.05);
}

.btn-danger {
    background: var(--danger);
    color: var(--white);
}

.btn-danger:hover {
    background: #c82333;
    transform: scale(1.05);
}

/* Form Inputs */
.form-input {
    width: 100%;
    padding: 0.75rem;
    margin: 0.5rem 0;
    border: 1px solid var(--gray-light);
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-input:focus {
    border-color: var(--secondary);
    outline: none;
    box-shadow: 0 0 5px rgba(0, 168, 181, 0.3);
}

/* Links */
a {
    color: var(--secondary);
    text-decoration: none;
    font-weight: 500;
}

a:hover {
    text-decoration: underline;
    color: #008d97;
}

/* Lists */
ul {
    list-style: none;
    padding-left: 0;
}

li {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--gray-light);
}

/* Headings */
h1 {
    color: var(--primary);
    font-size: 2.5rem;
    margin-bottom: 2rem;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    nav {
        padding: 1rem;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
    }

    nav a, nav button {
        margin-right: 1rem;
        font-size: 0.9rem;
    }

    .container {
        padding: 0 1rem;
    }

    .card {
        padding: 1rem;
    }

    h1 {
        font-size: 2rem;
    }

    .form-container {
        width: 90%;
        max-width: 400px;
      }

      .map-popup-container{
        flex-direction: column;
      }

      .map-popup-table{
        width: 100% !important;
        max-width: 100% !important;
        height: 50% !important;
      }

      .map-popup-table h3{
        margin-bottom: 0px !important;
      }

      .event-wrapper-margin{
        margin: 0px !important;
      }

      .full-screen-btn{
        margin-top: 0px !important;
        display: none;
      }

}

/* @media (max-width: 768px) {
  .map-popup-container{
        flex-direction: column;
      }
} */

.event-wrapper-margin{
  margin: 1rem 0px ;
}

/* Logo Styling */
.logo {
    display: flex;
    align-items: center;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--white);
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
}

.logo span {
    color: var(--secondary); /* Teal accent on "1" */
}

.logo-icon {
    width: 30px;
    height: 30px;
    margin-right: 0.5rem;
    background: var(--secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--white);
}
/* Hero Section */
.hero {
    text-align: center;
    padding: 4rem 0;
    background: linear-gradient(135deg, var(--accent) 0%, var(--white) 100%);
    border-radius: 12px;
    margin-bottom: 2rem;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--gray-dark);
    margin: 1rem 0 2rem;
}

.hero-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

/* Features Section */
.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Call-to-Action Section */
.cta {
    text-align: center;
    padding: 2rem;
    background: var(--gray-light);
    border-radius: 12px;
}

.cta h2 {
    color: var(--primary);
    margin-bottom: 1rem;
}

.cta p {
    color: var(--gray-dark);
    margin-bottom: 1.5rem;
}
/* Map Popup */
.map-popup-container {
    display: flex;
    height: 100vh;
    background: var(--gray-light);
}

.map-popup-table {
    width: 370px;
    padding: 1.5rem;
    background: var(--white);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

.map-popup-table h3 {
    color: var(--primary);
    margin-bottom: 1rem;
}

.map-popup-table ul {
    list-style: none;
}

.map-popup-table li {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--gray-light);
    display: flex;
    justify-content: space-between;
}

.map-popup-table li span {
    color: var(--gray-dark);
}

.map-popup-map {
    flex: 1;
    height: 100%;
}
/* Drag-and-Drop */
.draggable-responder {
    cursor: move;
    padding: 0.5rem;
    background: var(--white);
    border: 1px solid var(--gray-light);
    border-radius: 4px;
    transition: background 0.2s ease;
}

.draggable-responder.dragging {
    opacity: 0.5;
    background: var(--accent);
}

.dropzone {
    transition: background 0.2s ease;
}

.dropzone.dragover {
    background: rgba(0, 168, 181, 0.3); /* Teal overlay */
}
.draggable-responder {
    cursor: move;
    transition: background-color 0.3s;
}

.green-card {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
}

.yellow-card {
    background-color: #fff3cd;
    border: 1px solid #ffeeba;
}

.dropzone:hover {
    background-color: #e0f7fa;
}

/* -------Sign Up Code Start------- */

.signup-container {
    min-height: 100vh;
    background-color: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 2rem 1rem;
}

.form-container {
    background-color: #ffffff;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    width: 100%;
    max-width: 850px;
    margin: 0 auto;
}

.signup-title {
    color: var(--primary);
    font-size: 1.6rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-align: center;
}

.signup-subtitle {
    color: #6b7280;
    text-align: center;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.signup-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Form styles for single layer layout */
.signup-form {
    background-color: #ffffff;
    padding: 1rem 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.field-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.form-field {
    margin-bottom: 0.5rem;
}

.input-field {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background-color: #fff;
    font-size: 1rem;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    height: 44px;
}

.input-field:focus {
    border-color: var(--secondary);
    box-shadow: 0 0 0 3px rgba(0, 168, 181, 0.2);
    outline: none;
}

.input-field::placeholder {
    color: #9ca3af;
}

.input-field:disabled {
    background-color: #f3f4f6;
    cursor: not-allowed;
}

.phone-input-wrapper {
    display: flex;
    width: 100%;
    margin-top: 0.25rem;
}

.country-code-select {
    width: 30%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px 0 0 6px;
    background-color: #f9fafb;
    font-size: 1rem;
    height: 44px;
}

.phone-input {
    width: 70%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-left: none;
    border-radius: 0 6px 6px 0;
    font-size: 1rem;
    height: 44px;
}

.required {
    color: #ef4444;
}

/* Verification Page Styles */
.verification-container {
    max-width: 500px;
    margin: 0 auto;
    padding: 2rem;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.verification-header {
    text-align: center;
    margin-bottom: 2rem;
}

.verification-header h2 {
    color: var(--primary);
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.verification-subtitle {
    color: #6b7280;
    font-size: 1rem;
}

.verification-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.verification-code-container {
    margin-bottom: 1rem;
}

.verification-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.verification-input {
    width: 100%;
    padding: 1rem;
    font-size: 1.2rem;
    letter-spacing: 2px;
    text-align: center;
    height: 50px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.verification-input:focus {
    border-color: var(--secondary);
    box-shadow: 0 0 0 3px rgba(0, 168, 181, 0.2);
}

.verification-info {
    text-align: center;
    margin: 1rem 0;
}

.resend-link {
    color: var(--secondary);
    cursor: pointer;
    font-weight: 500;
    text-decoration: underline;
}

.verification-timer {
    color: #6b7280;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.verification-buttons {
    display: flex;
    justify-content: center;
    margin-top: 1rem;
}

.verification-button {
    width: 100%;
    max-width: 300px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    font-size: 1rem;
}

.loading-indicator {
    text-align: center;
    padding: 1rem;
    background-color: #f3f4f6;
    border-radius: 6px;
    margin-bottom: 1rem;
}

.button-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    margin-top: 1rem;
}

.submit-button {
    width: 100%;
    max-width: 300px;
    background-color: var(--primary);
    color: white;
    font-weight: 500;
    padding: 0.75rem 1.25rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.95rem;
    text-align: center;
    height: 44px;
    line-height: 1;
}

.submit-button:hover {
    background-color: #153057;
    transform: translateY(-1px);
}

.submit-button:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
    transform: none;
}

.login-link {
    color: var(--primary);
    font-size: 0.95rem;
    text-decoration: none;
    margin-top: 1rem;
}

.login-link:hover {
    text-decoration: underline;
}

.error-message {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.success-message {
    color: #22c55e;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}
label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
}

/* Responsive styles for mobile devices */
@media (max-width: 768px) {
    .signup-container {
        padding: 1rem;
    }
    
    .form-container {
        padding: 1.5rem;
        border-radius: 8px;
    }
    
    .signup-title {
        font-size: 1.5rem;
    }
    
    .signup-subtitle {
        font-size: 0.9rem;
    }
    
    .form-section {
        padding: 1rem;
    }
    
    .section-title {
        font-size: 1.1rem;
    }
    
    .field-group {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .input-field {
        padding: 0.7rem;
        font-size: 0.95rem;
    }
    
    .phone-input-wrapper {
        flex-direction: row;
    }
    
    .country-code-select {
        width: 35%;
    }
    
    .phone-input {
        width: 65%;
    }
    
    .submit-button {
        padding: 0.75rem;
        font-size: 0.95rem;
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    .form-container {
        padding: 1rem;
    }
    
    .signup-title {
        font-size: 1.3rem;
    }
    
    .signup-subtitle {
        font-size: 0.85rem;
        margin-bottom: 1.5rem;
    }
    
    .form-section {
        padding: 0.75rem;
    }
    
    .section-title {
        font-size: 1rem;
        margin-bottom: 0.75rem;
    }
    
    .field-group {
        gap: 0.5rem;
        margin-bottom: 0.5rem;
    }
    
    .form-field {
        margin-bottom: 0.5rem;
    }
    
    .input-field {
        padding: 0.6rem;
        font-size: 0.9rem;
    }
    
    .phone-input-wrapper {
        flex-direction: column;
    }
    
    .country-code-select {
        width: 100%;
        border-radius: 6px;
        margin-bottom: 0.5rem;
    }
    
    .phone-input {
        width: 100%;
        border-left: 1px solid #d1d5db;
        border-radius: 6px;
    }
}



  @media (min-width: 768px) {
    .iframe-wrapper {
      width: 80vw !important;
      height: 80vh !important;
    }
    /* .gm-fullscreen-control{
      display: none;
    } */
  }

  @media (max-width: 768px) {
    .close-btn-md {
      display: flex !important;
      border: 1px solid red;
    }
  }
.badge {
  display: inline-block;
  padding: 0.35em 0.65em;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.375rem; /* pill shape */
}

.badge-primary {
  background-color: #0d6efd;
}

.badge-secondary {
  background-color: #6c757d;
}

.badge-success {
  background-color: #198754;
}

.badge-danger {
  background-color: #dc3545;
}

.badge-warning {
  background-color: #ffc107;
  color: #212529;
}

/* ============================================
   RESPONSIVE DESIGN SYSTEM
   ============================================ */

/* Mobile First Approach */
/* Extra Small devices (phones, 320px and up) */
@media (max-width: 575.98px) {
  /* Typography */
  h1 { font-size: 1.5rem; }
  h2 { font-size: 1.3rem; }
  h3 { font-size: 1.2rem; }
  h4 { font-size: 1.1rem; }
  
  /* Forms */
  .form-input, .form-select {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px 8px;
  }
  
  /* Buttons */
  .btn, .btn-primary, .btn-secondary {
    width: 100%;
    margin-bottom: 8px;
    padding: 12px 16px;
    font-size: 16px;
  }
  
  /* Cards */
  .card {
    margin-bottom: 16px;
    padding: 16px;
    border-radius: 8px;
  }
  
  /* Tables */
  table {
    font-size: 14px;
  }
  
  table th, table td {
    padding: 8px 4px;
  }
  
  /* Navigation */
  nav {
    padding: 0.75rem 1rem;
  }
  
  /* Modal */
  .delete-modal {
    margin: 16px;
    padding: 16px;
    max-width: calc(100vw - 32px);
  }
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
  .container {
    max-width: 540px;
  }
  
  /* Grid System */
  .row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -8px;
  }
  
  .col-sm-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .col-sm-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .col-sm-3 { flex: 0 0 25%; max-width: 25%; }
  .col-sm-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
  .col-sm-12 { flex: 0 0 100%; max-width: 100%; }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 991.98px) {
  .container {
    max-width: 720px;
  }
  
  /* Button Groups */
  .btn-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .btn-group .btn {
    flex: 1;
    min-width: 120px;
  }
  
  /* Grid System */
  .col-md-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .col-md-3 { flex: 0 0 25%; max-width: 25%; }
  .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-md-6 { flex: 0 0 50%; max-width: 50%; }
  .col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
  .col-md-12 { flex: 0 0 100%; max-width: 100%; }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) and (max-width: 1199.98px) {
  .container {
    max-width: 960px;
  }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

/* Dashboard Specific Responsive */
@media (max-width: 768px) {
  /* StatCard Grid */
  .stat-cards {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
  }

  /* Event Info */
  .event-info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
  }

  /* Chat Interface */
  .chat-container {
    height: 250px;
  }

  /* Map Container */
  .map-container {
    height: 300px;
  }

  /* Event Location Mobile Responsive */
  .location-fields-row {
    grid-template-columns: 1fr !important;
  }

  /* Map Picker Button - Icon only on mobile */
  .map-picker-btn .map-text {
    display: none;
  }

  .map-picker-btn {
    padding: 12px !important;
    min-width: 48px;
  }

  /* Form Layout Mobile Responsive */
  .scale-urgency-grid {
    grid-template-columns: 1fr !important;
  }

  /* Event Type & Configuration */
  .event-type-grid {
    grid-template-columns: 1fr !important;
  }

  /* User Assignment */
  .user-assignment-grid {
    grid-template-columns: 1fr !important;
  }

  /* Report-to Locations */
  .report-locations-grid {
    grid-template-columns: 1fr !important;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  /* StatCard Grid */
  .stat-cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  /* Event Info */
  .event-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }
}

@media (min-width: 1025px) {
  /* StatCard Grid */
  .stat-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }
}

/* Form Layout Responsive */
@media (max-width: 768px) {
  .form-row {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .form-group {
    width: 100%;
  }
  
  /* Template Cards */
  .template-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  /* Location Management */
  .location-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .location-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
}

@media (min-width: 769px) {
  .form-row {
    display: flex;
    gap: 20px;
    align-items: end;
  }
  
  .form-group {
    flex: 1;
  }
  
  /* Template Cards */
  .template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }
  
  .location-buttons {
    display: flex;
    gap: 12px;
  }
}

/* Utility Classes */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }

@media (max-width: 575.98px) {
  .d-none-xs { display: none !important; }
  .d-block-xs { display: block !important; }
}

@media (min-width: 576px) and (max-width: 767.98px) {
  .d-none-sm { display: none !important; }
  .d-block-sm { display: block !important; }
}

@media (min-width: 768px) and (max-width: 991.98px) {
  .d-none-md { display: none !important; }
  .d-block-md { display: block !important; }
}

@media (min-width: 992px) {
  .d-none-lg { display: none !important; }
  .d-block-lg { display: block !important; }
}

/* Text Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

@media (max-width: 768px) {
  .text-center-mobile { text-align: center; }
  .text-left-mobile { text-align: left; }
}

/* Spacing Utilities */
.m-0 { margin: 0; }
.p-0 { padding: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }

/* Print Styles */
@media print {
  nav, .btn, .form-input { display: none; }
  .card { border: 1px solid #000; margin-bottom: 1rem; }
  body { font-size: 12pt; }
}

/* Dashboard Styles */
.dashboard-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  background: var(--white);
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.dashboard-header h1 {
  color: var(--primary);
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.event-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.event-info p {
  margin: 0.5rem 0;
  color: var(--text-dark);
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;
  }

  .status-cards {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .event-info {
    grid-template-columns: 1fr;
  }
}

.badge-info {
  background-color: #0dcaf0;
  color: #212529;
}

.badge-light {
  background-color: #f8f9fa;
  color: #212529;
}

.badge-dark {
  background-color: #212529;
}
  
