// src/EventList.js
import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import io from "socket.io-client";
import Footer from "./Footer";
import config from './config'; 
const { baseUrl } = config;

const socket = io(`${baseUrl}`);

function EventList({ token, mapsLoaded, endpoint, title, filter }) {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    console.log(`Fetching events for ${endpoint}`);
    fetch(`${baseUrl}${endpoint}`, {
      headers: { Authorization: `Bearer ${token}` },
    })
      .then((res) => res.json())
      .then((data) => {
        let eventList = Array.isArray(data) ? data : data.created_events || [];
        if (filter === "resolved") {
          eventList = eventList.filter((e) => e.status === "resolved");
        } else if (filter === "created_events") {
          eventList = data.created_events || [];
        }
        console.log(`Events fetched for ${endpoint}:`, eventList);
        setEvents(eventList);
        setLoading(false);
      })
      .catch((err) => console.error("Error fetching events:", err));

    socket.on("new-event", (newEvent) => {
      console.log("New event received via Socket.IO:", newEvent);
      setEvents((prev) => [
        newEvent,
        ...prev.filter((e) => e.id !== newEvent.id),
      ]);
    });

    socket.on("event-closed", (data) => {
      console.log("Event closed:", data);
      setEvents((prev) =>
        prev.map((e) => (e.id === data.id ? { ...e, status: "resolved" } : e))
      );
    });

    return () => {
      console.log("Component unmounting");
      socket.off("new-event");
      socket.off("event-closed");
    };
  }, [token, endpoint, filter]);

  return (
    <div
      style={{ display: "flex", flexDirection: "column", minHeight: "100vh" }}
    >
      <div style={{ flex: "1" }}>
        {loading ? (
          <div>Loading events...</div>
        ) : (
          <div>
            <h1>{title}</h1>
            {events.length > 0 ? (
              <ul>
                {events.map((event) => (
                  <li key={event.id}>
                    <Link to={`/dashboard/${event.id}`}>
                      Event #{event.id}: {event.title} - {event.status}
                    </Link>
                  </li>
                ))}
              </ul>
            ) : (
              <p>No events found.</p>
            )}
          </div>
        )}
      </div>
      <Footer token={token} />
    </div>
  );
}

export default EventList;
