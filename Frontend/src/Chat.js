// src/Chat.js
import React, { useState, useEffect } from "react";
import io from "socket.io-client";
import Footer from "./Footer";
import config from './config'; 
const { baseUrl } = config;

const socket = io(`${baseUrl}`, {
  transports: ["websocket"],
  reconnection: true,
  reconnectionAttempts: 5,
});

function Chat({ token }) {
  const [messages, setMessages] = useState([]);
  const [message, setMessage] = useState("");
  const userId = JSON.parse(atob(token.split(".")[1])).id;
  const username = JSON.parse(atob(token.split(".")[1])).username || "User";

  useEffect(() => {
    socket.on("chat-message", (msg) => {
      console.log("Chat message received:", msg);
      setMessages((prev) => [...prev, msg]);
    });

    return () => {
      socket.off("chat-message");
    };
  }, []);

  const sendMessage = () => {
    if (message.trim()) {
      socket.emit("chat-message", { userId, username, text: message });
      setMessage("");
    }
  };

  return (
    <div
      style={{ display: "flex", flexDirection: "column", minHeight: "100vh" }}
    >
      <div style={{ flex: "1" }}>
        <div style={{ marginBottom: "20px" }}>
          <div
            style={{
              height: "200px",
              overflowY: "scroll",
              border: "1px solid #ccc",
              padding: "10px",
            }}
          >
            {messages.map((msg, index) => (
              <div key={index} style={{ margin: "5px 0" }}>
                <strong>{msg.username}</strong> (
                {new Date(msg.timestamp).toLocaleTimeString()}): {msg.text}
              </div>
            ))}
          </div>
          <input
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={(e) => e.key === "Enter" && sendMessage()}
            style={{ width: "70%", margin: "10px 0" }}
          />
          <button onClick={sendMessage} style={{ padding: "5px 10px" }}>
            Send
          </button>
        </div>
      </div>
      <Footer token={token} />
    </div>
  );
}

export default Chat;
