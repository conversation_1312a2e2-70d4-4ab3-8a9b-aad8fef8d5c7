import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, InfoWindow } from "@react-google-maps/api";
import io from "socket.io-client";

import config from './config'; 
const { baseUrl } = config;

const socket = io(`${baseUrl}`, {
  transports: ["websocket"],
  reconnection: true,
  reconnectionAttempts: 5,
});

function Map({ token }) {
  const [events, setEvents] = useState([]);
  const [responders, setResponders] = useState([]);
  const [selectedMarker, setSelectedMarker] = useState(null);
  const mapRef = useRef(null);
  const mapStyles = { height: "400px", width: "100%" };

  useEffect(() => {
    fetch(`${baseUrl}/all-events`, {
      headers: { Authorization: `Bearer ${token}` },
    })
      .then((res) => res.json())
      .then((data) => {
        const unresolvedEvents = data.filter((e) => e.status !== "resolved");
        Promise.all(
          unresolvedEvents.map((event) =>
            geocodeLocation(event.location).then((coords) => ({
              ...event,
              coords,
            }))
          )
        ).then((geocodedEvents) => {
          setEvents(geocodedEvents);
          fitMapBounds(geocodedEvents, responders);
        });
      })
      .catch((error) => console.error("Fetch events error:", error));

    fetch(`${baseUrl}/responders`, {
      headers: { Authorization: `Bearer ${token}` },
    })
      .then((res) => res.json())
      .then((data) => {
        setResponders(data);
        fitMapBounds(events, data);
      })
      .catch((error) => console.error("Fetch responders error:", error));

    socket.on("new-event", (event) => {
      geocodeLocation(event.location).then((coords) => {
        setEvents((prev) => {
          const updated = [
            ...prev.filter((e) => e.id !== event.id),
            { ...event, coords },
          ].filter((e) => e.status !== "resolved");
          fitMapBounds(updated, responders);
          return updated;
        });
      });
    });

    socket.on("event-closed", (data) => {
      console.log("Event closed received:", data);
      setEvents((prev) => prev.filter((e) => e.id !== data.id));
    });

    socket.on("responder-update", (responder) => {
      console.log("Responder update received:", responder);
      setResponders((prev) => {
        const updated = prev.filter((r) => r.id !== responder.id);
        const newResponders = [...updated, responder];
        fitMapBounds(events, newResponders);
        return newResponders;
      });
    });

    socket.on("task-response", (task) => {
      console.log("Task response received:", task);
      setResponders((prev) =>
        prev.map((r) =>
          r.id === task.assigned_to ? { ...r, status: task.status } : r
        )
      );
    });

    return () => {
      console.log("Map unmounting");
      socket.off("new-event");
      socket.off("event-closed");
      socket.off("responder-update");
      socket.off("task-response");
    };
  }, [token]);

  const geocodeLocation = (location) => {
    return new Promise((resolve) => {
      if (!location || location === "Unknown") {
        resolve({ lat: 43.8, lng: -91.2 }); // Default: La Crosse, WI
        return;
      }
      const geocoder = new window.google.maps.Geocoder();
      geocoder.geocode({ address: location }, (results, status) => {
        if (status === "OK" && results[0]) {
          resolve({
            lat: results[0].geometry.location.lat(),
            lng: results[0].geometry.location.lng(),
          });
        } else {
          console.log(`Geocode failed for ${location}: ${status}`);
          resolve({ lat: 43.8, lng: -91.2 }); // Fallback
        }
      });
    });
  };

  const fitMapBounds = (eventsList, respondersList) => {
    if (!mapRef.current) return;
    const bounds = new window.google.maps.LatLngBounds();
    eventsList.forEach((event) => {
      if (event.coords) bounds.extend(event.coords);
    });
    respondersList.forEach((responder) => {
      if (responder.latitude && responder.longitude) {
        bounds.extend({ lat: responder.latitude, lng: responder.longitude });
      }
    });
    if (!bounds.isEmpty()) {
      mapRef.current.fitBounds(bounds);
      const zoom = mapRef.current.getZoom();
      if (zoom > 10) mapRef.current.setZoom(10); // Cap max zoom
      console.log(
        "Map bounds set to include events and responders, zoom:",
        mapRef.current.getZoom()
      );
    } else {
      mapRef.current.setCenter({ lat: 43.8, lng: -91.2 });
      mapRef.current.setZoom(6); // Wider default zoom
    }
  };

  const getResponderIcon = (status) => {
    switch (status) {
      case "available":
        return "http://maps.google.com/mapfiles/ms/icons/green-dot.png";
      case "acknowledged":
        return "http://maps.google.com/mapfiles/ms/icons/yellow-dot.png";
      case "enroute":
        return "http://maps.google.com/mapfiles/ms/icons/blue-dot.png";
      case "delayed":
        return "http://maps.google.com/mapfiles/ms/icons/orange-dot.png";
      case "busy":
        return "http://maps.google.com/mapfiles/ms/icons/purple-dot.png";
      default:
        return "http://maps.google.com/mapfiles/ms/icons/blue-dot.png";
    }
  };

  const handleMapLoad = (map) => {
    mapRef.current = map;
    fitMapBounds(events, responders);
  };

  const center = { lat: 43.8, lng: -91.2 }; // Default center

  return (
    <div style={{ marginBottom: "20px" }}>
      <GoogleMap
        mapContainerStyle={mapStyles}
        center={center}
        zoom={6} // Wider default zoom
        onLoad={handleMapLoad}
      >
        {events.map(
          (event) =>
            event.coords && (
              <Marker
                key={event.id}
                position={event.coords}
                label={event.title}
                icon={{
                  url: "http://maps.google.com/mapfiles/ms/icons/red-dot.png",
                }}
                onClick={() =>
                  setSelectedMarker({ type: "event", data: event })
                }
              />
            )
        )}
        {responders.map(
          (responder) =>
            responder.latitude &&
            responder.longitude && (
              <Marker
                key={responder.id}
                position={{ lat: responder.latitude, lng: responder.longitude }}
                label={responder.username}
                icon={{ url: getResponderIcon(responder.status) }}
                onClick={() =>
                  setSelectedMarker({ type: "responder", data: responder })
                }
              />
            )
        )}
        {selectedMarker && (
          <InfoWindow
            position={
              selectedMarker.type === "event"
                ? selectedMarker.data.coords
                : {
                    lat: selectedMarker.data.latitude,
                    lng: selectedMarker.data.longitude,
                  }
            }
            onCloseClick={() => setSelectedMarker(null)}
          >
            <div>
              {selectedMarker.type === "event" ? (
                <>
                  <h3>{selectedMarker.data.title}</h3>
                  <p>Scale: {selectedMarker.data.scale}</p>
                  <p>Urgency: {selectedMarker.data.urgency}</p>
                  <p>Location: {selectedMarker.data.location}</p>
                  <p>Status: {selectedMarker.data.status}</p>
                </>
              ) : (
                <>
                  <h3>{selectedMarker.data.username}</h3>
                  <p>Status: {selectedMarker.data.status}</p>
                </>
              )}
            </div>
          </InfoWindow>
        )}
      </GoogleMap>
      <div style={{ marginTop: "10px", fontSize: "12px" }}>
        <strong>Legend:</strong>
        <br />
        <span style={{ color: "red" }}>■</span> Event
        <br />
        <span style={{ color: "green" }}>■</span> Available
        <br />
        <span style={{ color: "yellow" }}>■</span> Acknowledged
        <br />
        <span style={{ color: "blue" }}>■</span> Enroute
        <br />
        <span style={{ color: "orange" }}>■</span> Delayed
        <br />
        <span style={{ color: "purple" }}>■</span> Busy
      </div>
    </div>
  );
}

export default Map;
