// src/MapPopup.js
import React, { useState, useEffect, useRef } from "react";
import {
  GoogleMap,
  Marker,
  Circle,
  useLoadScript,
} from "@react-google-maps/api";
import io from "socket.io-client";
import "./styles.css";
import config from "./config";
const { baseUrl } = config;

const socket = io(`${baseUrl}`, {
  transports: ["websocket"],
  reconnection: true,
  reconnectionAttempts: 5,
});

const mapContainerStyle = {
  width: "100%",
  height: "100%",
};

const circleOptions = {
  strokeColor: "#FF0000",
  strokeOpacity: 0.8,
  strokeWeight: 2,
  fillColor: "#FF0000",
  fillOpacity: 0.2,
};

const reportToCircleOptions = {
  strokeColor: "#00A8B5",
  strokeOpacity: 0.8,
  strokeWeight: 2,
  fillColor: "#00A8B5",
  fillOpacity: 0.2,
};

const LIBRARIES = ["places"];

function MapPopup({ token, eventId }) {
  const [eventInfo, setEventInfo] = useState(null);
  const [tasks, setTasks] = useState([]);
  const [responders, setResponders] = useState([]);
  const [notifiedResponders, setNotifiedResponders] = useState([]);
  const [eventLatLng, setEventLatLng] = useState(null);
  const [taskLocations, setTaskLocations] = useState({});
  const [reportToLocations, setReportToLocations] = useState({});
  const [responderPositions, setResponderPositions] = useState({});
  const [eventRadius, setEventRadius] = useState(804.67); // 0.5 miles default
  const [reportToRadius, setReportToRadius] = useState(804.67);
  const [geocodeErrors, setGeocodeErrors] = useState([]);
  const [currentTime, setCurrentTime] = useState(Date.now());
  const [displayOptions, setDisplayOptions] = useState({
    username: true,
    fullName: false,
    jobRole: true,
    all: false,
  });
  const mapRef = useRef(null);
  const [showMapModal, setShowMapModal] = useState("");

  const { isLoaded } = useLoadScript({
    googleMapsApiKey: "AIzaSyBigR0OaTpsLIIFuUJphgtMgWB7PMDDm7k",
    libraries: LIBRARIES,
  });

  const formatLocation = (location) => {
    if (!location) return "Unknown";
    if (typeof location === "string") return location;
    const { commonName, address, city, state, zip } = location;
    const namePart = commonName || "";
    const addressPart = address || "";
    const cityPart = city || "";
    const statePart = state || "";
    const zipPart = zip || "";
    return (
      `${namePart}${namePart && addressPart ? ", " : ""}${addressPart}${
        addressPart || namePart ? ", " : ""
      }${cityPart}${cityPart && statePart ? ", " : ""}${statePart} ${zipPart}`
        .trim()
        .replace(/,\s*,/g, ",")
        .replace(/,\s*$/, "") || "Unknown"
    );
  };

  const geocode = (location, setter, id = null) => {
    if (!isLoaded) return Promise.resolve(null);
    const geocoder = new window.google.maps.Geocoder();
  
    return new Promise((resolve) => {
      geocoder.geocode({ address: location }, (results, status) => {
        if (status === "OK") {
          const { lat, lng } = results[0].geometry.location;
          const coords = { lat: lat(), lng: lng() };
          
          if (id) {
            setter((prev) => ({ ...prev, [id]: coords }));
          } else {
            setter(coords);
          }
          resolve(coords);
        } else {
       
          setGeocodeErrors((prev) => [
            ...prev,
            `Failed to geocode ${location}: ${status}`,
          ]);
          if (!id) setter(null);
          resolve(null);
        }
      });
    });
  };

  const fetchData = async () => {
    try {
      const response = await fetch(`${baseUrl}/active-events/${eventId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (!response.ok) throw new Error("Failed to fetch event");
      const data = await response.json();

      setEventInfo(data);
      setTasks(data.tasks || []);

      const assignedIds = data.assignIds || [];
      const notifyAll = data.notifyAll && assignedIds.length === 0;

      // Build URL with query params only if needed
        let respondersUrl = `${baseUrl}/responders`;

        if (assignedIds.length > 0 && !notifyAll) {
          const params = new URLSearchParams();
          assignedIds.forEach((id) => params.append("ids[]", id));
          respondersUrl += `?${params.toString()}`;
        }

      const respondersRes = await fetch(`${respondersUrl}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const respondersData = await respondersRes.json();
     
      setResponders(respondersData);
      setNotifiedResponders(
        notifyAll
          ? respondersData
          : respondersData.filter((r) => assignedIds.includes(r.id))
      );

      const initialPositions = {};
      respondersData.forEach((r) => {
        if (r.latitude && r.longitude) {
          initialPositions[r.id] = {
            lat: r.latitude,
            lng: r.longitude,
            last_updated: r.last_updated || new Date().toISOString(),
          };
        }
      });
    
      setResponderPositions(initialPositions);

      if (isLoaded) {
        const eventLocation = formatLocation(data.location);
     
        await geocode(eventLocation, setEventLatLng);

        const reportToPromises =
          data.included_report_to_locations?.map((loc) => {
            const locString = `${loc.commonName || ""}, ${loc.address || ""}, ${
              loc.city || ""
            }, ${loc.state || ""} ${loc.zip || ""}`
              .trim()
              .replace(/,\s*,/g, ",")
              .replace(/,\s*$/, "");
            return geocode(locString, setReportToLocations, locString);
          }) || [];

        const taskPromises =
          data.tasks?.map((task) => {
            if (task.report_to_location) {
              return geocode(
                task.report_to_location,
                setTaskLocations,
                task.id
              );
            }
            return Promise.resolve(null);
          }) || [];

        await Promise.all([...reportToPromises, ...taskPromises]);
      }
    } catch (err) {
      console.error("Error fetching map data:", err);
    }
  };

  useEffect(() => {
    if (token && eventId && isLoaded) fetchData();
    socket.on("connect", () => socket.emit("join", `event-${eventId}`));
    socket.on("task-assigned", ({ task }) => {
     
      setTasks((prev) => [...prev.filter((t) => t.id !== task.id), task]);
      if (task.report_to_location && isLoaded)
        geocode(task.report_to_location, setTaskLocations, task.id);
    });
    socket.on("task-response", (task) => {
    
      setTasks((prev) => {
        const newTasks = prev.map((t) => (t.id === task.id ? task : t));
     
        return newTasks;
      });
    });
    socket.on("responder-update", (responder) => {
      
      setResponders((prev) => {
        const newResponders = prev.map((r) =>
          r.id === responder.id ? { ...r, ...responder } : r
        );
        
        return newResponders;
      });
      if (responder.latitude && responder.longitude) {
        const newLastUpdated =
          responder.last_updated || new Date().toISOString();
        setResponderPositions((prev) => {
          const newPositions = {
            ...prev,
            [responder.id]: {
              lat: responder.latitude,
              lng: responder.longitude,
              last_updated: newLastUpdated,
            },
          };
         
          return newPositions;
        });
      }
    });

    const timer = setInterval(() => setCurrentTime(Date.now()), 5000);

    return () => {
      socket.off("connect");
      socket.off("task-assigned");
      socket.off("task-response");
      socket.off("responder-update");
      clearInterval(timer);
      socket.emit("leave", `event-${eventId}`);
    };
  }, [token, eventId, isLoaded]);

  const calculateETA = (responder, taskId) => {
    if (!responder?.latitude || !responder?.longitude || !taskLocations[taskId])
      return "TBD";
    const R = 6371; // Earth radius in km
    const dLat =
      ((taskLocations[taskId].lat - responder.latitude) * Math.PI) / 180;
    const dLon =
      ((taskLocations[taskId].lng - responder.longitude) * Math.PI) / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos((responder.latitude * Math.PI) / 180) *
        Math.cos((taskLocations[taskId].lat * Math.PI) / 180) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c; // Distance in km
    const speed = 50; // km/h
    return Math.round((distance / speed) * 60) + " min";
  };

  const getTimeSinceUpdate = (lastUpdated) => {
    const last = lastUpdated ? new Date(lastUpdated) : new Date(currentTime);
    const timeSince = Math.floor((currentTime - last) / 1000); // Seconds
    
    return timeSince;
  };

  const getCardColor = (lastUpdated) => {
    const seconds = getTimeSinceUpdate(lastUpdated);
    return seconds < 70 ? "green-card" : "yellow-card";
  };

  const getPinIcon = (lastUpdated) => {
    const seconds = getTimeSinceUpdate(lastUpdated);
    return seconds < 70
      ? "http://maps.google.com/mapfiles/ms/icons/green-dot.png"
      : "http://maps.google.com/mapfiles/ms/icons/yellow-dot.png";
  };

  const adjustRadius = (zoom) => {
    const minRadius = 100.58; // 1/16 mile in meters
    const maxRadius = 804.67; // 0.5 mile in meters
    const maxZoom = 20;
    const minZoom = 10;
    const slope = (maxRadius - minRadius) / (minZoom - maxZoom);
    const radius = maxRadius + slope * (zoom - minZoom);
    return Math.max(minRadius, Math.min(maxRadius, radius));
  };

  const handleZoomChanged = () => {
    if (mapRef.current) {
      const newZoom = mapRef.current.getZoom();
      const newRadius = adjustRadius(newZoom);
      setEventRadius(newRadius);
      setReportToRadius(newRadius);
 
    }
  };

  const onMapLoad = (map) => {
    mapRef.current = map;
    handleZoomChanged();
  };

  const handleDragStart = (e, responderId) => {
    e.dataTransfer.setData("text/plain", responderId);
  };

  const handleDrop = async (e, newReportToLoc) => {
    e.preventDefault();
    const responderId = e.dataTransfer.getData("text/plain");
    const task = tasks.find((t) => t.assigned_to === parseInt(responderId));
    if (!task) return;

    try {
      const response = await fetch(`${baseUrl}/tasks`, {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          event_id: eventId,
          assigned_to: task.assigned_to,
          status: task.status,
          report_to_location: newReportToLoc,
        }),
      });
      if (!response.ok) throw new Error("Failed to update task");
      const updatedTask = await response.json();
      setTasks((prev) =>
        prev.map((t) => (t.id === updatedTask.id ? updatedTask : t))
      );
      if (newReportToLoc && isLoaded)
        geocode(newReportToLoc, setTaskLocations, updatedTask.id);
      socket
        .to(`user-${task.assigned_to}`)
        .emit("task-assigned", { task: updatedTask, event: eventInfo });
    } catch (err) {
      console.error("Error updating task location:", err);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.currentTarget.style.backgroundColor = "#e0f7fa";
  };

  const handleDragLeave = (e) => {
    e.currentTarget.style.backgroundColor = "";
  };

  const handleDisplayOptionChange = (option) => {
    setDisplayOptions((prev) => ({
      ...prev,
      [option]: !prev[option],
      all:
        option === "all"
          ? !prev.all
          : prev.all && option !== "all"
          ? false
          : prev.all,
    }));
  };

  // const getResponderDisplayText = (responder) => {
  //   const parts = [];
  //   if (
  //     displayOptions.all ||
  //     (displayOptions.username &&
  //       displayOptions.fullName &&
  //       displayOptions.jobRole)
  //   ) {
  //     parts.push(responder.username || `Responder ${responder.id}`);
  //     parts.push(
  //       `${responder.first_name || ""} ${responder.last_name || ""}`.trim()
  //     );
  //     parts.push(responder.job_role || "");
  //   } else {
  //     if (displayOptions.username)
  //       parts.push(responder.username || `Responder ${responder.id}`);
  //     if (displayOptions.fullName)
  //       parts.push(
  //         `${responder.first_name || ""} ${responder.last_name || ""}`.trim()
  //       );
  //     if (displayOptions.jobRole)
  //        parts.push(`(${responder.job_role})` || "");
  //   }
  //   return parts.filter(Boolean).join(" - ") || "Unknown";
  // };

  const getResponderDisplayText = (responder) => {
    const parts = [];

    if (displayOptions.all) {
      // Show all fields in the format: full name (job_role)
      const fullName = `${responder.first_name || ""} ${
        responder.last_name || ""
      }`.trim();
      const jobRole = responder.job_role || "No Role";
      parts.push(`${fullName} (${jobRole})`);
    } else if (displayOptions.fullName && displayOptions.jobRole) {
      // Show full name and job role in the format: full name (job_role)
      const fullName = `${responder.first_name || ""} ${
        responder.last_name || ""
      }`.trim();
      const jobRole = responder.job_role || "No Role";
      parts.push(`${fullName} (${jobRole})`);
    } else if (displayOptions.username && displayOptions.jobRole) {
      // Show username and job role in the format: username (job_role)
      const username = responder.username || `Responder ${responder.id}`;
      const jobRole = responder.job_role || "No Role";
      parts.push(`${username} (${jobRole})`);
    } else {
      // Show specific fields based on displayOptions
      if (displayOptions.username)
        parts.push(responder.username || `Responder ${responder.id}`);
      if (displayOptions.fullName)
        parts.push(
          `${responder.first_name || ""} ${responder.last_name || ""}`.trim()
        );
      if (displayOptions.jobRole)
        parts.push(`(${responder.job_role || "No Role"})`);
    }

    return parts.filter(Boolean).join(" - ") || "Unknown";
  };
  //   const getResponderDisplayText = (responder) => {
  //     const parts = [];

  //     if (
  //           displayOptions.all ||
  //           (displayOptions.username &&
  //             displayOptions.fullName &&
  //             displayOptions.jobRole)
  //         ) {
  //           parts.push(responder.username || `Responder ${responder.id}`);
  //           parts.push(
  //             `${responder.first_name || ""} ${responder.last_name || ""}`.trim()
  //           );
  //           parts.push(responder.job_role || "");
  //         }
  //     // Format username (job_role)
  //     if (displayOptions.username) {
  //         const username = responder.username || `Responder ${responder.id}`;
  //         const jobRole = responder.job_role || "";
  //         parts.push(jobRole ? `${username} (${jobRole})` : username);
  //     }

  //     // Add full name if enabled
  //     if (displayOptions.fullName) {
  //         const fullName = `${responder.first_name || ""} ${responder.last_name || ""}`.trim();
  //         if (fullName) parts.push(fullName);
  //     }

  //     // Add job role separately if enabled and not already included
  //     if (displayOptions.jobRole && !displayOptions.username) {
  //         parts.push(responder.job_role || "");
  //     }

  //     return parts.filter(Boolean).join(" - ") || "Unknown";
  // };

  if (!isLoaded) return <div>Loading map...</div>;

  const sections = {
    Notified: notifiedResponders.filter(
      (r) => !tasks.some((t) => t.assigned_to === r.id)
    ),
    Acknowledged: tasks.filter(
      (t) => t.status?.toLowerCase() === "acknowledged"
    ),
    Enroute: tasks.filter((t) => t.status?.toLowerCase() === "enroute"),
    Declined: tasks.filter((t) => t.status?.toLowerCase() === "declined"),
    Unassigned: notifiedResponders.filter(
      (r) => !tasks.some((t) => t.assigned_to === r.id) && !r.latitude
    ),
  };

  const reportToGroups = {};
  tasks.forEach((task) => {
    const loc = task.report_to_location || "Unassigned";
    if (!reportToGroups[loc]) reportToGroups[loc] = [];
    reportToGroups[loc].push(task);
  });

  return (
    <div
      style={{ display: "flex", flexDirection: "column", minHeight: "100vh" }}
    >
      <div style={{ flex: "1" }}>
        <div className="map-popup-container">
          <div
            className="map-popup-table"
            style={{
              maxWidth: "400px",
              overflowY: "auto",
              display: "flex",
              flexDirection: "column", // Make the container a column flexbox
              height: "100%", // Ensure the container takes full height
            }}
          >
            <h3
              style={{
                background: "var(--secondary)",
                color: "var(--white)",
                padding: "0.5rem",
                borderRadius: "6px",
              }}
            >
              Responders
            </h3>
            {geocodeErrors.length > 0 && (
              <div style={{ color: "var(--danger)", marginBottom: "1rem" }}>
                {geocodeErrors.map((err, idx) => (
                  <p key={idx}>{err}</p>
                ))}
              </div>
            )}

            <div className="event-wrapper-margin">
              <table style={{ width: "100%", borderSpacing: "10px" }}>
                <tbody>
                  <tr>
                    <td style={{ whiteSpace: "nowrap" }}>
                      <label>Event Radius (mi):</label>
                    </td>
                    <td>
                      <select
                        style={{
                          width: "100%",
                          padding: "0.5rem",
                          borderRadius: "4px",
                          border: "1px solid #ccc",
                          backgroundColor: "#f9f9f9",
                        }}
                        value={eventRadius / 1609.34}
                        onChange={(e) =>
                          setEventRadius(e.target.value * 1609.34)
                        }
                      >
                        <option value="0.5">0.5</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="5">5</option>
                      </select>
                    </td>
                  </tr>
                  <tr>
                    <td style={{ whiteSpace: "nowrap" }}>
                      <label>Report-To Radius (mi):</label>
                    </td>
                    <td>
                      <select
                        style={{
                          width: "100%",
                          padding: "0.5rem",
                          borderRadius: "4px",
                          border: "1px solid #ccc",
                          backgroundColor: "#f9f9f9",
                        }}
                        value={reportToRadius / 1609.34}
                        onChange={(e) =>
                          setReportToRadius(e.target.value * 1609.34)
                        }
                      >
                        <option value="0.5">0.5</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="5">5</option>
                      </select>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div
              style={{
                margin: "1rem 0",
                display: "flex",
                flexWrap: "wrap",
                gap: "10px",
              }}
            >
              {["username", "fullName", "jobRole", "all"].map((option) => (
                <label
                  key={option}
                  style={{ display: "flex", alignItems: "center", gap: "5px" }}
                >
                  <input
                    type="checkbox"
                    checked={displayOptions[option]}
                    onChange={() => handleDisplayOptionChange(option)}
                  />
                  {option === "username"
                    ? "Username"
                    : option === "fullName"
                    ? "Full Name"
                    : option === "jobRole"
                    ? "Job Role"
                    : "All"}
                </label>
              ))}
            </div>
            {Object.entries(reportToGroups).map(([loc, tasksInLoc]) => (
              <div
                key={loc}
                style={{
                  marginBottom: "1rem",
                  padding: "0.5rem",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                }}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={(e) => handleDrop(e, loc === "Unassigned" ? null : loc)}
              >
                <h4>{loc}</h4>
                <ul style={{ listStyle: "none", padding: 0 }}>
                  {tasksInLoc.map((task) => {
                    const responder = responders.find(
                      (r) => r.id === task.assigned_to
                    );
                    if (!responder) return null;
                    const eta = calculateETA(responder, task.id);
                    const lastUpdated =
                      responderPositions[responder.id]?.last_updated;
                    const cardClass = getCardColor(lastUpdated);
                    const timeSince = getTimeSinceUpdate(lastUpdated);

                    return (
                      <li
                        key={task.id}
                        className={`draggable-responder ${cardClass}`}
                        draggable
                        onDragStart={(e) => handleDragStart(e, responder.id)}
                        style={{
                          padding: "5px",
                          margin: "0.25rem 0px",
                          borderRadius: "4px",
                        }}
                        title={`Last update: ${timeSince}s ago`}
                      >
                        <span>{getResponderDisplayText(responder)}</span>
                        <span>
                          {" "}
                          - {task.status}{" "}
                          {eta !== "TBD" &&
                          task.status.toLowerCase() === "enroute"
                            ? `(${eta})`
                            : ""}
                        </span>
                      </li>
                    );
                  })}
                </ul>
              </div>
            ))}
            <div
              style={{
                marginBottom: "1rem",
                padding: "0.5rem",
                border: "1px solid #ccc",
                borderRadius: "4px",
              }}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={(e) => handleDrop(e, null)}
            >
              <h4>Unassigned</h4>
              <ul style={{ listStyle: "none", padding: 0 }}>
                {sections.Unassigned.map((responder) => (
                  <li
                    key={responder.id}
                    className="draggable-responder yellow-card"
                    draggable
                    onDragStart={(e) => handleDragStart(e, responder.id)}
                    style={{
                      padding: "0.5rem",
                      margin: "0.25rem 0",
                      borderRadius: "4px",
                    }}
                  >
                    <span>{getResponderDisplayText(responder)}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Add View Full Screen Button */}
            <div  className="full-screen-btn" style={{ textAlign: "center", marginTop: "1rem" }}>
              <button
                style={{
                  padding: "0.5rem 1rem",
                  backgroundColor: "var(--secondary)",
                  color: "var(--white)",
                  border: "none",
                  borderRadius: "4px",
                  cursor: "pointer",
                  fontSize: "1rem",
                  width: "100%",
                }}
                onClick={() =>
                  window.open(
                    `/map-popup/${eventId}?token=${token}`,
                    "_blank",
                    "noopener,noreferrer"
                  )
                }
              >
                View Full Screen
              </button>
            </div>
          </div>
          <div className="map-popup-map">
            {isLoaded ? (
              <GoogleMap
                mapContainerStyle={mapContainerStyle}
                center={eventLatLng || { lat: 43.8, lng: -91.2 }}
                zoom={10}
                onLoad={onMapLoad}
                onZoomChanged={handleZoomChanged}
                options={{
                  fullscreenControl: false, // Disable the full-screen button
                }}
              >
                {/* {console.log("Popup map rendering with center:", eventLatLng)} */}
                {eventLatLng && (
                  <>
                    <Marker
                      position={eventLatLng}
                      title={eventInfo?.title || "Event Location"}
                      icon={{
                        url: "http://maps.google.com/mapfiles/ms/icons/red-dot.png",
                      }}
                      zIndex={1000}
                      onLoad={() =>
                        console.log("Event marker loaded at:", eventLatLng)
                      }
                    />
                    <Circle
                      center={eventLatLng}
                      radius={eventRadius}
                      options={circleOptions}
                    />
                  </>
                )}
                {Object.entries(reportToLocations).map(([loc, coords]) => (
                  <React.Fragment key={loc}>
                    <Marker
                      position={coords}
                      title={loc}
                      icon={{
                        url: "http://maps.google.com/mapfiles/ms/icons/green-dot.png",
                      }}
                      zIndex={500}
                      onLoad={() =>
                        console.log(
                          `Report-to marker loaded for ${loc} at:`,
                          coords
                        )
                      }
                    />
                    <Circle
                      center={coords}
                      radius={reportToRadius}
                      options={reportToCircleOptions}
                    />
                  </React.Fragment>
                ))}
                {Object.entries(responderPositions).map(([id, pos]) => {
                  const responder = responders.find(
                    (r) => r.id === parseInt(id)
                  );
                  return responder ? (
                    <Marker
                      key={id}
                      position={{ lat: pos.lat, lng: pos.lng }}
                      title={`${
                        responder.username || `Responder ${id}`
                      } (Last update: ${getTimeSinceUpdate(
                        pos.last_updated
                      )}s ago)`}
                      icon={{ url: getPinIcon(pos.last_updated) }}
                      zIndex={100}
                      onLoad={() =>
                        console.log(
                          `Responder marker loaded for ${
                            responder.username || id
                          } at:`,
                          pos
                        )
                      }
                    />
                  ) : null;
                })}
              </GoogleMap>
            ) : (
              <div>Loading map...</div>
            )}
          </div>
        </div>
      </div>
      {/* Removed <Footer token={token} /> */}
    </div>
  );
}

export default MapPopup;