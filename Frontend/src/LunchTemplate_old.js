import React from "react";
import { <PERSON>complete, GoogleMap, Marker } from "@react-google-maps/api";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Select from "react-select"; // Assuming you use react-select for modern select functionalities.

function LaunchTemplate({
  formData,
  setFormData,
  responders,
  locations,
  previousLocations,
  theme,
  mapsLoaded,
  launchEvent,
  onLoadEventAutocomplete,
  onPlaceChanged,
  setShowMapPicker,
  showMapPicker,
  mapCenter,
  selectedPosition,
  handleMapClick,
  selectedModule,
  mode,
  setMode,
  formConfig,
  error,
  success,
}) {
  const roles = ["staff", "lead", "commander", "viewer"];
  const [role, setRole] = React.useState("");
  // const [formData, setFormData] = React.useState({ assignedIds: [] });
  const [filteredResponders, setFilteredResponders] =
    React.useState(responders);

   

    React.useEffect(() => {
      // Filter responders based on the selected role
      if (role) {
        const filtered = responders.filter(
          (responder) => responder.role === role
        );
        setFilteredResponders(filtered);
    
        // Automatically select responders with the selected role
        const selectedIds = filtered.map((responder) => responder.id);
        setFormData((prev) => ({ ...prev, assignedIds: selectedIds }));
      } else {
        setFilteredResponders(responders); // Show all responders if no role selected
        setFormData((prev) => ({ ...prev, assignedIds: [] })); // Clear selection
      }
    }, [role, responders]);
    
   




  const handleResponderChange = (selectedOptions) => {
    const selectedIds = selectedOptions ? selectedOptions.map((option) => option.value) : [];
  
   
    setFormData((prev) => ({ ...prev, assignedIds: selectedIds }));
  };
  

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    const currentInput = e.target;
    setFormData((prev) => {
      if (name.startsWith("custom_")) {
        return {
          ...prev,
          customFields: { ...prev.customFields, [name]: value },
        };
      } else if (type === "checkbox") {
        return { ...prev, [name]: checked };
      } else if (name === "assignedIds") {
        return {
          ...prev,
          assignedIds: value
            .split(",")
            .map((id) => parseInt(id.trim()))
            .filter((id) => !isNaN(id)),
        };
      } else if (name.includes(".")) {
        const [parent, child] = name.split(".");
        if (parent === "location") {
          return { ...prev, location: { ...prev.location, [child]: value } };
        }
      }
      return { ...prev, [name]: value };
    });

    // Restore focus after state update
    requestAnimationFrame(() => {
      if (currentInput) currentInput.focus();
    });
  };

  const handleLocationChange = (index, field, value) => {
    setFormData((prev) => {
      const newLocs = [...prev.included_report_to_locations];
      newLocs[index] = {
        ...newLocs[index],
        [field]: field === "staffNeeded" ? parseInt(value) || 2 : value,
      };
      return { ...prev, included_report_to_locations: newLocs };
    });
  };

  const addLocation = () => {
    setFormData((prev) => ({
      ...prev,
      included_report_to_locations: [
        ...prev.included_report_to_locations,
        {
          commonName: "",
          address: "",
          city: "",
          state: "",
          zip: "",
          primary: false,
          staffNeeded: 2,
          resources: [{ name: "", responderCount: 2, requiredRoles: [] }],
        },
      ],
    }));
  };

  const deleteLocation = (index) => {
    setFormData((prev) => ({
      ...prev,
      included_report_to_locations: prev.included_report_to_locations.filter(
        (_, i) => i !== index
      ),
    }));
  };

  const clearLocation = (index) => {
    setFormData((prev) => {
      const newLocs = [...prev.included_report_to_locations];
      newLocs[index] = {
        commonName: "",
        address: "",
        city: "",
        state: "",
        zip: "",
        primary: false,
        staffNeeded: 2,
        resources: [{ name: "", responderCount: 2, requiredRoles: [] }],
      };
      return { ...prev, included_report_to_locations: newLocs };
    });
  };

  const addResource = (locIndex) => {
    setFormData((prev) => {
      const newLocs = [...prev.included_report_to_locations];
      newLocs[locIndex].resources.push({
        name: "",
        responderCount: 2,
        requiredRoles: [],
      });
      return { ...prev, included_report_to_locations: newLocs };
    });
  };

  const removeResource = (locIndex, resIndex) => {
    setFormData((prev) => {
      const newLocs = [...prev.included_report_to_locations];
      newLocs[locIndex].resources.splice(resIndex, 1);
      return { ...prev, included_report_to_locations: newLocs };
    });
  };

  const handleResourceChange = (locIndex, resIndex, field, value) => {
    setFormData((prev) => {
      const newLocs = [...prev.included_report_to_locations];
      newLocs[locIndex].resources[resIndex][field] =
        field === "responderCount" ? parseInt(value) || 2 : value;
      return { ...prev, included_report_to_locations: newLocs };
    });
  };

  const handleLocationSelect = (index, value) => {
    const selectedLocation = locations.find(
      (loc) => `${loc.commonName} - ${loc.address}` === value
    );
    if (selectedLocation) {
      setFormData((prev) => {
        const newLocs = [...prev.included_report_to_locations];
        newLocs[index] = {
          ...newLocs[index],
          address: selectedLocation.address,
          commonName: selectedLocation.commonName,
        };
        return { ...prev, included_report_to_locations: newLocs };
      });
    } else if (previousLocations.includes(value)) {
      const parts = value.split(/,\s*/).map((part) => part.trim());
      const address = parts[0] || "";
      const city = parts[1] || "";
      const stateZip = parts[2] || "";
      const [state, zip] = stateZip.split(/\s+/).map((part) => part.trim());
      setFormData((prev) => {
        const newLocs = [...prev.included_report_to_locations];
        newLocs[index] = {
          ...newLocs[index],
          address,
          city,
          state: state || "",
          zip: zip || "",
        };
        return { ...prev, included_report_to_locations: newLocs };
      });
    }
  };

  const handleLaunch = async (data, saveAsTemplate = false) => {
    const toastId = toast.loading("Launching event...", {
      position: "top-right",
    });

    try {
      await launchEvent(data, saveAsTemplate);
      toast.update(toastId, {
        render: saveAsTemplate
          ? "Event saved as template and launched successfully!"
          : "Event launched successfully!",
        type: "success",
        isLoading: false,
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
    } catch (err) {
      toast.update(toastId, {
        render: "Failed to launch event. Please try again.",
        type: "error",
        isLoading: false,
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
    }
  };
  // console.log("formConfig:", formConfig);
  // console.log("formData:", formData);

  return (
    <div
  style={{
    padding: "20px",
    fontFamily: "Arial, sans-serif",
    backgroundColor: theme.backgroundColor,
  }}
>
  <ToastContainer
    position="top-right"
    autoClose={3000}
    hideProgressBar={false}
    newestOnTop
    closeOnClick
    rtl={false}
    pauseOnFocusLoss
    draggable
    pauseOnHover
  />
  <h1
    style={{
      color:
        mode === "template" ? theme.primaryColor : theme.secondaryColor,
      marginBottom: "20px",
    }}
  >
    {mode === "template" ? "Launch from Template" : "Full Form Launch"}
  </h1>
  <form
    onSubmit={(e) => {
      e.preventDefault();
      handleLaunch({
        ...formData,
        assignedIds: formData.assignedIds.filter((id) =>
          responders.some((r) => r.id === id)
        ),
        module: selectedModule,
      });
    }}
    style={{
      backgroundColor: "#fff",
      padding: "20px",
      borderRadius: "8px",
      boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
    }}
  >
    {error && <p style={{ color: "#d32f2f" }}>{error}</p>}
    {success && <p style={{ color: "#388e3c" }}>{success}</p>}
    {formConfig.map((field) => (
      <div key={field.name} style={{ marginBottom: "15px" }}>
        <label
          style={{ display: "block", marginBottom: "5px", color: "#333" }}
        >
          {field.label}
          {field.required && <span style={{ color: "red" }}>*</span>}
        </label>
        {field.type === "text" && !field.nested && (
          <input
            type="text"
            name={field.name}
            value={formData[field.name] || ""}
            onChange={handleInputChange}
            style={{
              width: "100%",
              padding: "8px",
              borderRadius: "4px",
              border: "1px solid #ccc",
            }}
          />
        )}
        {field.type === "text" &&
          field.nested &&
          field.name.startsWith("location.") && (
            <input
              type="text"
              name={field.name}
              value={formData.location[field.name.split(".")[1]] || ""}
              onChange={handleInputChange}
              required={field.required}
              style={{
                width: "100%",
                padding: "8px",
                borderRadius: "4px",
                border: "1px solid #ccc",
              }}
            />
          )}
        {field.type === "textarea" && (
          <textarea
            name={field.name}
            value={formData[field.name] || ""}
            onChange={handleInputChange}
            required={field.required}
            style={{
              width: "100%",
              padding: "8px",
              borderRadius: "4px",
              border: "1px solid #ccc",
              minHeight: "80px",
            }}
          />
        )}
        {field.type === "select" && (
          <select
            name={field.name}
            value={formData[field.name] || ""}
            onChange={handleInputChange}
            style={{
              width: "100%",
              padding: "12px 10px",
              borderRadius: "6px",
              border: "1px solid #ccc",
              fontSize: "14px",
              color: "#555",
              backgroundColor: "#f9f9f9",
              marginTop: "5px",
              transition: "border-color 0.2s ease, box-shadow 0.2s ease",
            }}
            onFocus={(e) => (e.target.style.borderColor = theme.primaryColor)}
            onBlur={(e) => (e.target.style.borderColor = "#ccc")}
          >
            <option value="">Select...</option>
            {(field.name === "scale"
              ? ["Small", "Medium", "Large"]
              : field.name === "urgency"
              ? ["Low", "Medium", "High", "Immediate"]
              : field.options || []
            ).map((opt) => (
              <option key={opt} value={opt}>
                {opt}
              </option>
            ))}
          </select>
        )}
        {field.type === "autocomplete" && mapsLoaded && (
          <>
            <Autocomplete
              onLoad={onLoadEventAutocomplete}
              onPlaceChanged={() => onPlaceChanged()}
            >
              <input
                type="text"
                name="location.address"
                value={formData.location.address || ""}
                onChange={handleInputChange}
                required={field.required}
                style={{
                  width: "100%",
                  padding: "8px",
                  borderRadius: "4px",
                  border: "1px solid #ccc",
                }}
              />
            </Autocomplete>
          </>
        )}
        {field.type === "checkbox" && (
          <input
            type="checkbox"
            name={field.name}
            checked={formData[field.name]}
            onChange={handleInputChange}
          />
        )}
      </div>
    ))}
  





        <div style={{ marginBottom: "20px" }}>
          <label
            style={{
              display: "block",
              fontSize: "16px",
              fontWeight: "bold",
              marginBottom: "8px",
              color: "#333",
            }}
          >
            Location:
          </label>
          <select
            name="location"
            value={`${formData.location.commonName} - ${formData.location.address}`}
            onChange={(e) => {
              const selectedLocation = locations.find(
                (loc) => `${loc.commonName} - ${loc.address}` === e.target.value
              );
              if (selectedLocation) {
                setFormData((prev) => ({
                  ...prev,
                  location: {
                    ...prev.location,
                    commonName: selectedLocation.commonName,
                    address: selectedLocation.address,
                  },
                }));
              } else if (previousLocations.includes(e.target.value)) {
                const parts = e.target.value
                  .split(/,\s*/)
                  .map((part) => part.trim());
                const address = parts[0] || "";
                const city = parts[1] || "";
                const stateZip = parts[2] || "";
                const [state, zip] = stateZip
                  .split(/\s+/)
                  .map((part) => part.trim());
                setFormData((prev) => ({
                  ...prev,
                  location: {
                    ...prev.location,
                    address,
                    city,
                    state: state || "",
                    zip: zip || "",
                  },
                }));
              }
            }}
            style={{
              width: "100%",
              padding: "12px 10px",
              borderRadius: "6px",
              border: "1px solid #ccc",
              fontSize: "14px",
              color: "#555",
              backgroundColor: "#f9f9f9",
              marginTop: "5px",
              transition: "border-color 0.2s ease, box-shadow 0.2s ease",
            }}
            onFocus={(e) => (e.target.style.borderColor = "#007BFF")}
            onBlur={(e) => (e.target.style.borderColor = "#ccc")}
          >
            <option value="" style={{ color: "#999" }}>
              Type or Select a location
            </option>
            {locations.map((loc, i) => (
              <option key={i} value={`${loc.commonName} - ${loc.address}`}>
                {loc.commonName} - {loc.address}
              </option>
            ))}
            {previousLocations.map((loc, i) => (
              <option key={`prev-${i}`} value={loc}>
                {loc}
              </option>
            ))}
          </select>
        </div>

        {/* <div style={{ marginBottom: "15px" }}>
          <label>Report-to Locations:</label>
          {formData.included_report_to_locations.map((loc, index) => (
            <div
              key={index}
              style={{
                border: "1px solid #ccc",
                padding: "10px",
                marginBottom: "10px",
                borderRadius: "4px",
              }}
            >
              <div
                style={{ display: "flex", gap: "10px", marginBottom: "10px" }}
              >
                <button
                  type="button"
                  onClick={() => deleteLocation(index)}
                  style={{
                    backgroundColor: "#d32f2f",
                    color: "#fff",
                    padding: "5px 10px",
                    borderRadius: "4px",
                    border: "none",
                  }}
                >
                  Delete
                </button>
                <button
                  type="button"
                  onClick={() => clearLocation(index)}
                  style={{
                    backgroundColor: "#666",
                    color: "#fff",
                    padding: "5px 10px",
                    borderRadius: "4px",
                    border: "none",
                  }}
                >
                  Clear
                </button>
              </div>
              <input
                type="text"
                value={loc.commonName}
                onChange={(e) =>
                  handleLocationChange(index, "commonName", e.target.value)
                }
                placeholder="Common Name"
                style={{
                  width: "100%",
                  padding: "8px",
                  borderRadius: "4px",
                  border: "1px solid #ccc",
                  marginBottom: "5px",
                }}
              />
              <select
                value={`${loc.commonName} - ${loc.address}`}
                onChange={(e) => handleLocationSelect(index, e.target.value)}
                style={{
                  width: "100%",
                  padding: "8px",
                  borderRadius: "4px",
                  border: "1px solid #ccc",
                  marginBottom: "5px",
                }}
              >
                <option value="">Type or Select</option>
                {locations.map((loc, i) => (
                  <option key={i} value={`${loc.commonName} - ${loc.address}`}>
                    {loc.commonName} - {loc.address}
                  </option>
                ))}
                {previousLocations.map((loc, i) => (
                  <option key={`prev-${i}`} value={loc}>
                    {loc}
                  </option>
                ))}
              </select>
              {mapsLoaded && (
                <Autocomplete onPlaceChanged={() => onPlaceChanged(index)}>
                  <input
                    type="text"
                    value={loc.address}
                    onChange={(e) =>
                      handleLocationChange(index, "address", e.target.value)
                    }
                    placeholder="Address"
                    style={{
                      width: "100%",
                      padding: "8px",
                      borderRadius: "4px",
                      border: "1px solid #ccc",
                      marginBottom: "5px",
                    }}
                  />
                </Autocomplete>
              )}
              <button
                type="button"
                onClick={() => setShowMapPicker(index)}
                style={{
                  padding: "8px 16px",
                  backgroundColor: theme.primaryColor,
                  color: "#fff",
                  borderRadius: "4px",
                  border: "none",
                  marginBottom: "5px",
                }}
              >
                Pick on Map
              </button>
              <input
                type="text"
                value={loc.city}
                onChange={(e) =>
                  handleLocationChange(index, "city", e.target.value)
                }
                placeholder="City"
                style={{
                  width: "100%",
                  padding: "8px",
                  borderRadius: "4px",
                  border: "1px solid #ccc",
                  marginBottom: "5px",
                }}
              />
              <input
                type="text"
                value={loc.state}
                onChange={(e) =>
                  handleLocationChange(index, "state", e.target.value)
                }
                placeholder="State"
                style={{
                  width: "100%",
                  padding: "8px",
                  borderRadius: "4px",
                  border: "1px solid #ccc",
                  marginBottom: "5px",
                }}
              />
              <input
                type="text"
                value={loc.zip}
                onChange={(e) =>
                  handleLocationChange(index, "zip", e.target.value)
                }
                placeholder="Zip"
                style={{
                  width: "100%",
                  padding: "8px",
                  borderRadius: "4px",
                  border: "1px solid #ccc",
                  marginBottom: "5px",
                }}
              />
              <label>
                <input
                  type="checkbox"
                  checked={loc.primary}
                  onChange={(e) =>
                    handleLocationChange(index, "primary", e.target.checked)
                  }
                />{" "}
                Primary
              </label>
              <input
                type="number"
                value={loc.staffNeeded}
                onChange={(e) =>
                  handleLocationChange(index, "staffNeeded", e.target.value)
                }
                min="1"
                style={{
                  width: "100%",
                  padding: "8px",
                  borderRadius: "4px",
                  border: "1px solid #ccc",
                  marginBottom: "5px",
                }}
              />
              <div style={{ marginTop: "10px" }}>
                <h4>Resources</h4>
                {loc.resources.map((res, resIndex) => (
                  <div
                    key={resIndex}
                    style={{
                      display: "flex",
                      gap: "10px",
                      marginBottom: "5px",
                    }}
                  >
                    <select
                      value={res.name}
                      onChange={(e) =>
                        handleResourceChange(
                          index,
                          resIndex,
                          "name",
                          e.target.value
                        )
                      }
                      style={{
                        flex: "1",
                        padding: "8px",
                        borderRadius: "4px",
                        border: "1px solid #ccc",
                      }}
                    >
                      <option value="">Select Resource</option>
                      {(theme.resources || []).map((resource) => (
                        <option key={resource} value={resource}>
                          {resource}
                        </option>
                      ))}
                    </select>
                    <input
                      type="number"
                      value={res.responderCount}
                      onChange={(e) =>
                        handleResourceChange(
                          index,
                          resIndex,
                          "responderCount",
                          e.target.value
                        )
                      }
                      min="1"
                      style={{
                        width: "100px",
                        padding: "8px",
                        borderRadius: "4px",
                        border: "1px solid #ccc",
                      }}
                    />
                    <button
                      type="button"
                      onClick={() => removeResource(index, resIndex)}
                      style={{
                        backgroundColor: "#d32f2f",
                        color: "#fff",
                        padding: "5px 10px",
                        borderRadius: "4px",
                        border: "none",
                      }}
                    >
                      Remove
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={() => addResource(index)}
                  style={{
                    padding: "8px 16px",
                    backgroundColor: theme.secondaryColor,
                    color: "#fff",
                    borderRadius: "4px",
                    border: "none",
                    marginTop: "5px",
                  }}
                >
                  Add Resource
                </button>
              </div>
            </div>
          ))}
          <button
            type="button"
            onClick={addLocation}
            style={{
              padding: "8px 16px",
              backgroundColor: theme.secondaryColor,
              color: "#fff",
              borderRadius: "4px",
              border: "none",
              marginTop: "10px",
            }}
          >
            Add Location
          </button>
        </div> */}

        <div style={{ marginBottom: "20px", fontFamily: "Arial, sans-serif" }}>
          <label
            style={{ fontSize: "16px", fontWeight: "bold", color: "#333" }}
          >
            Report-to Locations:
          </label>
          {formData.included_report_to_locations.map((loc, index) => (
            <div
              key={index}
              style={{
                border: "1px solid #ddd",
                padding: "15px",
                marginBottom: "15px",
                borderRadius: "8px",
                boxShadow: "0 2px 5px rgba(0, 0, 0, 0.1)",
                backgroundColor: "#fafafa",
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  marginBottom: "15px",
                }}
              >
                <button
                  type="button"
                  onClick={() => deleteLocation(index)}
                  style={{
                    backgroundColor: "#ff4d4f",
                    color: "#fff",
                    padding: "6px 12px",
                    borderRadius: "6px",
                    border: "none",
                    cursor: "pointer",
                    fontSize: "14px",
                  }}
                >
                  Delete
                </button>
                <button
                  type="button"
                  onClick={() => clearLocation(index)}
                  style={{
                    backgroundColor: "#757575",
                    color: "#fff",
                    padding: "6px 12px",
                    borderRadius: "6px",
                    border: "none",
                    cursor: "pointer",
                    fontSize: "14px",
                  }}
                >
                  Clear
                </button>
              </div>
              <input
                type="text"
                value={loc.commonName}
                onChange={(e) =>
                  handleLocationChange(index, "commonName", e.target.value)
                }
                placeholder="Common Name"
                style={{
                  width: "100%",
                  padding: "10px",
                  borderRadius: "6px",
                  border: "1px solid #ddd",
                  marginBottom: "10px",
                  fontSize: "14px",
                }}
              />
              <select
                value={`${loc.commonName} - ${loc.address}`}
                onChange={(e) => handleLocationSelect(index, e.target.value)}
                style={{
                  width: "100%",
                  padding: "10px",
                  borderRadius: "6px",
                  border: "1px solid #ddd",
                  marginBottom: "10px",
                  fontSize: "14px",
                  backgroundColor: "#fff",
                }}
              >
                <option value="">Type or Select</option>
                {locations.map((loc, i) => (
                  <option key={i} value={`${loc.commonName} - ${loc.address}`}>
                    {loc.commonName} - {loc.address}
                  </option>
                ))}
                {previousLocations.map((loc, i) => (
                  <option key={`prev-${i}`} value={loc}>
                    {loc}
                  </option>
                ))}
              </select>
              {mapsLoaded && (
                <Autocomplete onPlaceChanged={() => onPlaceChanged(index)}>
                  <input
                    type="text"
                    value={loc.address}
                    onChange={(e) =>
                      handleLocationChange(index, "address", e.target.value)
                    }
                    placeholder="Address"
                    style={{
                      width: "100%",
                      padding: "10px",
                      borderRadius: "6px",
                      border: "1px solid #ddd",
                      marginBottom: "10px",
                      fontSize: "14px",
                    }}
                  />
                </Autocomplete>
              )}
              <button
                type="button"
                onClick={() => setShowMapPicker(index)}
                style={{
                  padding: "10px 20px",
                  backgroundColor: "#007bff",
                  color: "#fff",
                  borderRadius: "6px",
                  border: "none",
                  cursor: "pointer",
                  marginBottom: "10px",
                  fontSize: "14px",
                }}
              >
                Pick on Map
              </button>
              <input
                type="text"
                value={loc.city}
                onChange={(e) =>
                  handleLocationChange(index, "city", e.target.value)
                }
                placeholder="City"
                style={{
                  width: "100%",
                  padding: "10px",
                  borderRadius: "6px",
                  border: "1px solid #ddd",
                  marginBottom: "10px",
                  fontSize: "14px",
                }}
              />
              <input
                type="text"
                value={loc.state}
                onChange={(e) =>
                  handleLocationChange(index, "state", e.target.value)
                }
                placeholder="State"
                style={{
                  width: "100%",
                  padding: "10px",
                  borderRadius: "6px",
                  border: "1px solid #ddd",
                  marginBottom: "10px",
                  fontSize: "14px",
                }}
              />
              <input
                type="text"
                value={loc.zip}
                onChange={(e) =>
                  handleLocationChange(index, "zip", e.target.value)
                }
                placeholder="Zip"
                style={{
                  width: "100%",
                  padding: "10px",
                  borderRadius: "6px",
                  border: "1px solid #ddd",
                  marginBottom: "10px",
                  fontSize: "14px",
                }}
              />
              <label
                style={{
                  display: "block",
                  marginBottom: "10px",
                  fontSize: "14px",
                }}
              >
                <input
                  type="checkbox"
                  checked={loc.primary}
                  onChange={(e) =>
                    handleLocationChange(index, "primary", e.target.checked)
                  }
                  style={{ marginRight: "8px" }}
                />
                Primary
              </label>
              <input
                type="number"
                value={loc.staffNeeded}
                onChange={(e) =>
                  handleLocationChange(index, "staffNeeded", e.target.value)
                }
                min="1"
                placeholder="Staff Needed"
                style={{
                  width: "100%",
                  padding: "10px",
                  borderRadius: "6px",
                  border: "1px solid #ddd",
                  marginBottom: "10px",
                  fontSize: "14px",
                }}
              />
              <div style={{ marginTop: "15px" }}>
                <h4
                  style={{
                    fontSize: "16px",
                    fontWeight: "bold",
                    color: "#333",
                  }}
                >
                  Resources
                </h4>
                {loc.resources.map((res, resIndex) => (
                  <div
                    key={resIndex}
                    style={{
                      display: "flex",
                      gap: "10px",
                      alignItems: "center",
                      marginBottom: "10px",
                    }}
                  >
                    <select
                      value={res.name}
                      onChange={(e) =>
                        handleResourceChange(
                          index,
                          resIndex,
                          "name",
                          e.target.value
                        )
                      }
                      style={{
                        flex: "1",
                        padding: "10px",
                        borderRadius: "6px",
                        border: "1px solid #ddd",
                        fontSize: "14px",
                      }}
                    >
                      <option value="">Select Resource</option>
                      {(theme.resources || []).map((resource) => (
                        <option key={resource} value={resource}>
                          {resource}
                        </option>
                      ))}
                    </select>
                    <input
                      type="number"
                      value={res.responderCount}
                      onChange={(e) =>
                        handleResourceChange(
                          index,
                          resIndex,
                          "responderCount",
                          e.target.value
                        )
                      }
                      min="1"
                      placeholder="Count"
                      style={{
                        width: "80px",
                        padding: "10px",
                        borderRadius: "6px",
                        border: "1px solid #ddd",
                        fontSize: "14px",
                      }}
                    />
                    <button
                      type="button"
                      onClick={() => removeResource(index, resIndex)}
                      style={{
                        backgroundColor: "#ff4d4f",
                        color: "#fff",
                        padding: "6px 12px",
                        borderRadius: "6px",
                        border: "none",
                        cursor: "pointer",
                        fontSize: "14px",
                      }}
                    >
                      Remove
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={() => addResource(index)}
                  style={{
                    padding: "10px 20px",
                    backgroundColor: "#6c757d",
                    color: "#fff",
                    borderRadius: "6px",
                    border: "none",
                    cursor: "pointer",
                    fontSize: "14px",
                    marginTop: "10px",
                  }}
                >
                  Add Resource
                </button>
              </div>
            </div>
          ))}
          <button
            type="button"
            onClick={addLocation}
            style={{
              padding: "10px 20px",
              backgroundColor: "#28a745",
              color: "#fff",
              borderRadius: "6px",
              border: "none",
              cursor: "pointer",
              fontSize: "14px",
              marginTop: "20px",
            }}
          >
            Add Location
          </button>
        </div>





        
        {!formData.notifyAllIfUnassigned && (
        <div style={{ marginBottom: "15px", marginTop: "20px" }}>
          {/* Role Selection */}
          <label>
            Role:
            <select
              value={role}
              onChange={(e) => setRole(e.target.value)}
              className="form-input"
              required
            >
              <option value="">Select Role</option>
              {roles.map((roleOption) => (
                <option key={roleOption} value={roleOption}>
                  {roleOption}
                </option>
              ))}
            </select>
          </label>

          {/* Assigned Responders */}
          <label>Assigned Responders:</label>

          {/* <select
            multiple
            name="assignedIds"
            value={formData.assignedIds}
            onChange={(e) => {
              const selectedIds = Array.from(
                e.target.selectedOptions,
                (option) => parseInt(option.value)
              );
              setFormData((prev) => ({ ...prev, assignedIds: selectedIds }));
            }}
            style={{
              width: "100%",
              padding: "8px",
              borderRadius: "4px",
              border: "1px solid #ccc",
              height: "150px",
            }}
          >
            {filteredResponders.map((responder) => {
              const isFiltered = role && responder.role === role;
              const isSelected = formData.assignedIds.includes(responder.id);
              return (
                <option
                  key={responder.id}
                  value={responder.id}
                  style={{
                    backgroundColor: isSelected
                      ? "#d3d3d3"
                      : isFiltered
                      ? "#f0f0f0"
                      : "#fff", // Gray for selected, light gray for filtered, white otherwise
                    color: "#000", // Text color set to black
                    padding: "5px",
                    borderBottom: "1px solid #ddd",
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <span style={{ fontWeight: "bold" }}>
                    {responder.username}
                  </span>
                  <span style={{ fontSize: "12px", color: "#666" }}>
                    ({responder.role})
                  </span>
                </option>
              );
            })}
          </select> */}
<Select
      options={filteredResponders.map((responder) => ({
        value: responder.id,
        label: `${responder.username} (${responder.role})`,
      }))}
      isMulti
      value={formData.notifyAllIfUnassigned
        ? [] 
        : filteredResponders
            .filter((responder) =>
              formData.assignedIds.includes(responder.id)
            )
            .map((responder) => ({
              value: responder.id,
              label: `${responder.username} (${responder.role})`,
            }))}
      onChange={handleResponderChange}
      closeMenuOnSelect={false}
      classNamePrefix="select"
      placeholder="Select responders"
    />
        </div>
        )}

        {/* <div style={{ marginBottom: "15px" }}>
          <label>
            <input
              type="checkbox"
              name="notifyAllIfUnassigned"
              checked={false}
              // checked={formData.notifyAllIfUnassigned}
              onChange={handleInputChange}
            />
            Notify All 
          </label>
        </div> */}

        <div style={{ marginBottom: "15px" }}>
          <label>
            <input
              type="checkbox"
              name="notifyAllIfUnassigned"
              checked={formData.notifyAllIfUnassigned || false}
              onChange={handleInputChange}
            />
            Notify All
          </label>
        </div>

        <div style={{ display: "flex", gap: "10px" }}>
          <button
            type="submit"
            style={{
              padding: "10px 20px",
              backgroundColor: theme.primaryColor,
              color: "#fff",
              borderRadius: "4px",
              border: "none",
            }}
          >
            Launch Event
          </button>
          <button
            type="button"
            onClick={() =>
              handleLaunch(
                {
                  ...formData,
                  assignedIds: formData.assignedIds.filter((id) =>
                    responders.some((r) => r.id === id)
                  ),
                  module: selectedModule,
                },
                true
              )
            }
            style={{
              padding: "10px 20px",
              backgroundColor: theme.secondaryColor,
              color: "#fff",
              borderRadius: "4px",
              border: "none",
            }}
          >
            Save as Template & Launch
          </button>
          <button
            type="button"
            onClick={() => setMode(null)}
            style={{
              padding: "10px 20px",
              backgroundColor: "#666",
              color: "#fff",
              borderRadius: "4px",
              border: "none",
            }}
          >
            Cancel
          </button>
        </div>
      </form>
      {showMapPicker !== null && mapsLoaded && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            backgroundColor: "rgba(0,0,0,0.5)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <div
            style={{
              backgroundColor: "#fff",
              padding: "20px",
              borderRadius: "8px",
              width: "80%",
              maxWidth: "800px",
            }}
          >
            <h3>Pick Location on Map</h3>
            <GoogleMap
              mapContainerStyle={{ width: "100%", height: "400px" }}
              center={mapCenter}
              zoom={10}
              onClick={handleMapClick}
            >
              {selectedPosition && <Marker position={selectedPosition} />}
            </GoogleMap>
            <div
              style={{
                display: "flex",
                gap: "10px",
                marginTop: "10px",
                justifyContent: "center",
              }}
            >
              <button
                onClick={() => setShowMapPicker(null)}
                style={{
                  padding: "10px 20px",
                  backgroundColor: theme.primaryColor,
                  color: "#fff",
                  borderRadius: "4px",
                  border: "none",
                }}
              >
                Confirm
              </button>
              <button
                onClick={() => setShowMapPicker(null)}
                style={{
                  padding: "10px 20px",
                  backgroundColor: "#666",
                  color: "#fff",
                  borderRadius: "4px",
                  border: "none",
                }}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default LaunchTemplate;
