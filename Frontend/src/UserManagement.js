import React, { useState, useEffect, useCallback, useRef } from "react";
import { Autocomplete } from "@react-google-maps/api";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import "./styles.css";

const baseUrl = process.env.REACT_APP_BASE_URL;

function UserManagement({ token, mapsLoaded, role }) {
  const [users, setUsers] = useState([]);
  const [commonLocations, setCommonLocations] = useState([]);
  const [newUser, setNewUser] = useState({
    username: "",
    password: "",
    role: "",
    email: "",
    main_location: "",
    phone: "",
    first_name: "",
    last_name: "",
    job_role: "",
    home_address: "",
    city: "",
    state: "",
    zip: "",
  });
  const [editUser, setEditUser] = useState(null);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [addressAutocomplete, setAddressAutocomplete] = useState(null);
  const [editAddressAutocomplete, setEditAddressAutocomplete] = useState(null);
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const editUsernameInputRef = useRef(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: "asc" });
  const itemsPerPage = 10;

  const roles = ["staff", "lead", "commander", "viewer"];

  useEffect(() => {
    if (role === "commander") {
      fetchUsers();
      fetchCommonLocations();
    }
  }, [role]);

  const fetchUsers = async () => {
    try {
      const response = await fetch(`${baseUrl}/users`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      if (!response.ok) throw new Error("Failed to fetch users");
      const data = await response.json();
      setUsers(data);
    } catch (err) {
      setError(err.message);
      setTimeout(() => setError(null), 3000);
    }
  };

  const fetchCommonLocations = async () => {
    try {
      const response = await fetch(`${baseUrl}/common-locations`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      if (!response.ok) throw new Error("Failed to fetch common locations");
      const data = await response.json();
      setCommonLocations(data);
    } catch (err) {
      setError(err.message);
      setTimeout(() => setError(null), 3000);
    }
  };

  const handleAddUser = async (e) => {
    e.preventDefault();
    console.log("Adding user:", newUser);
    try {
      const response = await fetch(`${baseUrl}/add-user`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newUser),
      });
      const data = await response.json();
      console.log("Add user response:", data);
      if (!response.ok) throw new Error(data.message || "Failed to add user");

      setSuccess(data.message || "User added successfully!");

      if (!data.email_sent) {
        toast.warn("User added successfully", {
          position: "top-right",
          autoClose: 3000,
        });
      }

      setNewUser({
        username: "",
        password: "",
        role: "",
        email: "",
        main_location: "",
        phone: "",
        first_name: "",
        last_name: "",
        job_role: "",
        home_address: "",
        city: "",
        state: "",
        zip: "",
      });
      fetchUsers();
      setShowAddUserModal(false);
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error("Add user error:", err);
      setError(err.message);
      setTimeout(() => setError(null), 3000);
    }
  };

  const handleDeleteUser = async (id) => {
    const confirmed = window.confirm("Are you sure you want to delete this user?");
    if (!confirmed) return;

    console.log("Deleting user ID:", id);
    try {
      const response = await fetch(`${baseUrl}/delete-user/${id}`, {
        method: "DELETE",
        headers: { Authorization: `Bearer ${token}` },
      });
      if (!response.ok) throw new Error("Failed to delete user");
      const data = await response.json();
      
      toast.success(data.message || "User deleted successfully!", {
        position: "top-right",
        autoClose: 3000,
      });

    } catch (err) {
      toast.success(err.message || "User deleted has error!", {
        position: "top-right",
        autoClose: 3000,
      });
    }
  };

  const handleEditUser = async (e) => {
    e.preventDefault();
    try {
      const response = await fetch(`${baseUrl}/edit-user/${editUser.id}`, {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(editUser),
      });
      if (!response.ok) throw new Error("Failed to update user");
      const data = await response.json();
      console.log("Edit user response:", data);

      setSuccess(data.message || "User updated successfully!");

      if (data.email_sent === false) {
        toast.warn("Email notification failed to send", {
          position: "top-right",
          autoClose: 3000,
        });
      }

      setEditUser(null);
      fetchUsers();
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error("Edit user error:", err);
      setError(err.message);
      setTimeout(() => setError(null), 3000);
    }
  };

  const handleEditButtonClick = (user) => {
    setEditUser(user);
    setTimeout(() => {
      if (editUsernameInputRef.current) {
        editUsernameInputRef.current.focus();
      }
    }, 0);
  };

  const onLoadAddressAutocomplete = useCallback((autoC) => {
    console.log("Autocomplete loaded for new user address");
    setAddressAutocomplete(autoC);
  }, []);

  const onLoadEditAddressAutocomplete = useCallback((autoC) => {
    console.log("Autocomplete loaded for edit user address");
    setEditAddressAutocomplete(autoC);
  }, []);

  const onPlaceChanged = useCallback(
    (isEdit = false) => {
      const autoC = isEdit ? editAddressAutocomplete : addressAutocomplete;
      if (!autoC) {
        console.error("Autocomplete not initialized");
        return;
      }
      const place = autoC.getPlace();
      console.log("Place selected:", place);
      if (!place || !place.geometry) {
        console.warn("No valid place selected:", place);
        return;
      }
      const addressComponents = place.address_components || [];
      const getComponent = (type) =>
        addressComponents.find((c) => c.types.includes(type))?.short_name || "";
      const streetNumber = getComponent("street_number");
      const route = getComponent("route");
      const address =
        streetNumber && route
          ? `${streetNumber} ${route}`
          : streetNumber || route || "";
      const city = getComponent("locality");
      const state = getComponent("administrative_area_level_1");
      const zip = getComponent("postal_code");

      if (isEdit) {
        setEditUser((prev) => ({
          ...prev,
          home_address: address,
          city,
          state,
          zip,
        }));
      } else {
        setNewUser((prev) => ({
          ...prev,
          home_address: address,
          city,
          state,
          zip,
        }));
      }
    },
    [addressAutocomplete, editAddressAutocomplete]
  );

  // Search and filter users
  const filteredUsers = users.filter((user) =>
    Object.values(user)
      .join(" ")
      .toLowerCase()
      .includes(searchQuery.toLowerCase())
  );

  // Sorting logic
  const sortedUsers = [...filteredUsers].sort((a, b) => {
    if (!sortConfig.key) return 0;
    const aValue = a[sortConfig.key] || "";
    const bValue = b[sortConfig.key] || "";
    if (sortConfig.direction === "asc") {
      return aValue.toString().localeCompare(bValue.toString());
    }
    return bValue.toString().localeCompare(aValue.toString());
  });

  // Pagination logic
  const totalPages = Math.ceil(sortedUsers.length / itemsPerPage);
  const paginatedUsers = sortedUsers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Handle sort click
  const handleSort = (key) => {
    setSortConfig((prev) => ({
      key,
      direction:
        prev.key === key && prev.direction === "asc" ? "desc" : "asc",
    }));
  };

  // Render sort icon
  const renderSortIcon = (key) => {
    if (sortConfig.key === key) {
      return sortConfig.direction === "asc" ? "↑" : "↓";
    }
    return "↕";
  };

  const handleToggleChange = async (userId, isChecked) => {
    if (!baseUrl) {
      console.error("Base URL is not defined. Check REACT_APP_BASE_URL in .env");
      setError("Configuration error: Base URL is missing");
      setTimeout(() => setError(null), 3000);
      return;
    }

    const user = users.find((u) => u.id === userId);
    if (!user) {
      console.error(`User with ID ${userId} not found`);
      setError("User not found");
      setTimeout(() => setError(null), 3000);
      return;
    }

    const newStatus = isChecked ? 1 : 0;
    console.log(`Updating user ${userId} status to ${isChecked ? 'active' : 'inactive'}`);

    try {
      const response = await fetch(`${baseUrl}/user/change-status`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: user.email,
          status: newStatus,
        }),
      });

      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.message || `Failed to update user status: ${response.status}`);
      }

      setSuccess(data.message || "User status updated successfully!");
      toast.success("User status updated successfully.", {
        position: "top-right",
        autoClose: 3000,
      });
      fetchUsers();
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error("Status change error:", err.message);
      setError(err.message || "Failed to update user status");
      toast.error(err.message || "Failed to update user status", {
        position: "top-right",
        autoClose: 3000,
      });
      setTimeout(() => setError(null), 3000);
    }
  };

  return (
    <div className="container">
      <div className="user-management-card">
        <h1>User Management (Commander Only)</h1>
        {error && <p style={{ color: "var(--danger)" }}>{error}</p>}
        {success && <p style={{ color: "var(--success)" }}>{success}</p>}

        {/* Add New User Button */}
        <button
          className="btn-primary"
          onClick={() => setShowAddUserModal(true)}
          style={{ marginBottom: "5px" }}
        >
          Add New User
        </button>

        {/* Add New User Modal */}
        {showAddUserModal && (
          <div className="delete-modal-overlay">
            <div className="delete-modal">
              <div style={{display: 'flex', justifyContent: 'space-between'}}>
                <h2>Add New User</h2>
                <button
                  type="button" style={{height: '40px', width: '40px', display: 'flex', justifyContent: "center", alignItems: 'center', backgroundColor: '#00a8b5', border: 'none', borderRadius: '10px', cursor: 'pointer' }}
                  onClick={() => setShowAddUserModal(false)}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="20" height="20" viewBox="0 0 50 50">
                    <path fill="#fff" d="M 9.15625 6.3125 L 6.3125 9.15625 L 22.15625 25 L 6.21875 40.96875 L 9.03125 43.78125 L 25 27.84375 L 40.9375 43.78125 L 43.78125 40.9375 L 27.84375 25 L 43.6875 9.15625 L 40.84375 6.3125 L 25 22.15625 Z"></path>
                  </svg>
                </button>
              </div>
              <form onSubmit={handleAddUser}>
                <label>
                  Username:
                  <input
                    value={newUser.username}
                    onChange={(e) =>
                      setNewUser({ ...newUser, username: e.target.value })
                    }
                    placeholder="Username"
                    className="form-input"
                    required
                  />
                </label>
                <label>
                  Password:
                  <input
                    type="password"
                    value={newUser.password}
                    onChange={(e) =>
                      setNewUser({ ...newUser, password: e.target.value })
                    }
                    placeholder="Password"
                    className="form-input"
                    required
                  />
                </label>
                <label>
                  Role:
                  <select
                    value={newUser.role}
                    onChange={(e) =>
                      setNewUser({ ...newUser, role: e.target.value })
                    }
                    className="form-input"
                    required
                  >
                    <option value="">Select Role</option>
                    {roles.map((role) => (
                      <option key={role} value={role}>
                        {role}
                      </option>
                    ))}
                  </select>
                </label>
                <label>
                  Email:
                  <input
                    type="email"
                    value={newUser.email}
                    onChange={(e) =>
                      setNewUser({ ...newUser, email: e.target.value })
                    }
                    placeholder="Email"
                    className="form-input"
                    required
                  />
                </label>
                <label>
                  First Name:
                  <input
                    value={newUser.first_name}
                    onChange={(e) =>
                      setNewUser({ ...newUser, first_name: e.target.value })
                    }
                    placeholder="First Name"
                    className="form-input"
                  />
                </label>
                <label>
                  Last Name:
                  <input
                    value={newUser.last_name}
                    onChange={(e) =>
                      setNewUser({ ...newUser, last_name: e.target.value })
                    }
                    placeholder="Last Name"
                    className="form-input"
                  />
                </label>
                <label>
                  Job Role:
                  <input
                    value={newUser.job_role}
                    onChange={(e) =>
                      setNewUser({ ...newUser, job_role: e.target.value })
                    }
                    placeholder="Job Role (e.g., Firefighter)"
                    className="form-input"
                  />
                </label>
                <label>
                  Main Location/Home Base:
                  <select
                    value={newUser.main_location}
                    onChange={(e) =>
                      setNewUser({ ...newUser, main_location: e.target.value })
                    }
                    className="form-input"
                    required
                  >
                    <option value="">Select a Common Location</option>
                    {commonLocations.map((location) => (
                      <option key={location.id} value={location.name}>
                        {location.name}
                      </option>
                    ))}
                  </select>
                </label>
                <label>
                  Phone Number:
                  <input
                    type="tel"
                    value={newUser.phone}
                    onChange={(e) =>
                      setNewUser({ ...newUser, phone: e.target.value })
                    }
                    placeholder="Phone Number (e.g., ************)"
                    className="form-input"
                  />
                </label>
                <label>
                  Home Address:
                  {mapsLoaded ? (
                    <Autocomplete
                      onLoad={onLoadAddressAutocomplete}
                      onPlaceChanged={() => onPlaceChanged()}
                    >
                      <input
                        value={newUser.home_address}
                        onChange={(e) =>
                          setNewUser({ ...newUser, home_address: e.target.value })
                        }
                        placeholder="Home Address"
                        className="form-input"
                      />
                    </Autocomplete>
                  ) : (
                    <input
                      value={newUser.home_address}
                      onChange={(e) =>
                        setNewUser({ ...newUser, home_address: e.target.value })
                      }
                      placeholder="Home Address (Maps Unavailable)"
                      className="form-input"
                    />
                  )}
                </label>
                <label>
                  City:
                  <input
                    value={newUser.city}
                    onChange={(e) =>
                      setNewUser({ ...newUser, city: e.target.value })
                    }
                    placeholder="City"
                    className="form-input"
                  />
                </label>
                <label>
                  State:
                  <input
                    value={newUser.state}
                    onChange={(e) =>
                      setNewUser({ ...newUser, state: e.target.value })
                    }
                    placeholder="State"
                    className="form-input"
                  />
                </label>
                <label>
                  Zip:
                  <input
                    value={newUser.zip}
                    onChange={(e) =>
                      setNewUser({ ...newUser, zip: e.target.value })
                    }
                    placeholder="Zip Code"
                    className="form-input"
                  />
                </label>
                <div
                  style={{ display: "flex", gap: "1rem", marginTop: "1rem" }}
                >
                  <button type="submit" className="btn-primary">
                    Add User
                  </button>

                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={() => setShowAddUserModal(false)}
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Manage Users Section */}
        <div style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '15px'}}>
          <h2 style={{fontSize: '25px', lineHeight: '25px'}}>Manage Users</h2>
          
          {/* Search Input */}
          <div >
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search users..."
              className="form-input"
              style={{ width: "100%", maxWidth: "300px", margin: '0px'}}
            />
          </div>
        </div>
        
        <div style={{ 
          overflowX: "auto",
          position: "relative",
          borderRadius: "8px",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
         }}>
          <table
            style={{
              width: "100%",
              borderCollapse: "collapse",
              backgroundColor: "#fff",
              borderRadius: "8px",
              overflow: "hidden",
            }}
          >
            <thead>
              <tr style={{ backgroundColor: "#1a73e8", color: "#fff" }}>
                <th
                  style={{ padding: "12px", textAlign: "left", cursor: "pointer" }}
                  onClick={() => handleSort("username")}
                >
                  Username {renderSortIcon("username")}
                </th>
                <th
                  style={{ padding: "12px", textAlign: "left", cursor: "pointer" }}
                  onClick={() => handleSort("role")}
                >
                  Role {renderSortIcon("role")}
                </th>
                <th
                  style={{ padding: "12px", textAlign: "left", cursor: "pointer" }}
                  onClick={() => handleSort("email")}
                >
                  Email {renderSortIcon("email")}
                </th>
                <th style={{ padding: "12px", textAlign: "left" }}>
                  First Name
                </th>
                <th style={{ padding: "12px", textAlign: "left" }}>
                  Last Name
                </th>
                <th style={{ padding: "12px", textAlign: "left" }}>Job Role</th>
                <th style={{ padding: "12px", textAlign: "left" }}>
                  Main Location
                </th>
                <th style={{ padding: "12px", textAlign: "left" }}>Phone</th>
                <th style={{ padding: "12px", textAlign: "left" }}>
                  Home Address
                </th>
                <th style={{ padding: "12px", textAlign: "left" }}>City</th>
                <th style={{ padding: "12px", textAlign: "left" }}>State</th>
                <th style={{ padding: "12px", textAlign: "left" }}>Zip</th>
                <th style={{ 
                  
                  padding: "12px", 
                  textAlign: "right",
                  position: "sticky",
                  right: 0,
                  backgroundColor: "#1a73e8",
                  minWidth: "200px"
                 }}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {paginatedUsers.map((user) => (
                <tr key={user.id} style={{ borderBottom: "1px solid #eee" }}>
                  <td style={{ padding: "12px" }}>{user.username}</td>
                  <td style={{ padding: "12px" }}>{user.role}</td>
                  <td style={{ padding: "12px" }}>{user.email}</td>
                  <td style={{ padding: "12px" }}>
                    {user.first_name || "N/A"}
                  </td>
                  <td style={{ padding: "12px" }}>{user.last_name || "N/A"}</td>
                  <td style={{ padding: "12px" }}>{user.job_role || "N/A"}</td>
                  <td style={{ padding: "12px" }}>{user.main_location}</td>
                  <td style={{ padding: "12px" }}>{user.phone || "N/A"}</td>
                  <td style={{ padding: "12px" }}>
                    {user.home_address || "N/A"}
                  </td>
                  <td style={{ padding: "12px" }}>{user.city || "N/A"}</td>
                  <td style={{ padding: "12px" }}>{user.state || "N/A"}</td>
                  <td style={{ padding: "12px" }}>{user.zip || "N/A"}</td>
                  <td style={{ 
                    padding: "12px", 
                    textAlign: "right",
                    position: "sticky",
                    right: 0,
                    backgroundColor: "#fff",
                    zIndex: 1
                   }}>
                    <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                      <label className="toggle-label">
                        <input
                          type="checkbox"
                          className="toggle-input"
                          checked={Number(user.status) === 1}
                          onChange={(e) => handleToggleChange(user.id, e.target.checked)}
                        />
                        <span className="toggle-track" />
                      </label>
                      <button
                        style={{
                          backgroundColor: "#2563eb",
                          color: "white",
                          padding: "8px 16px",
                          borderRadius: "6px",
                          border: "none",
                          fontSize: "14px",
                          fontWeight: "500",
                          cursor: "pointer",
                          transition: "background-color 0.2s",
                        }}
                        onClick={() => handleEditButtonClick(user)}
                        onMouseOver={(e) => (e.target.style.backgroundColor = "#1d4ed8")}
                        onMouseOut={(e) => (e.target.style.backgroundColor = "#2563eb")}
                      >
                        Edit
                      </button>
                      <button
                        style={{
                          backgroundColor: "#dc2626",
                          color: "white",
                          padding: "8px 16px",
                          borderRadius: "6px",
                          border: "none",
                          fontSize: "14px",
                          fontWeight: "500",
                          cursor: "pointer",
                          transition: "background-color 0.2s",
                        }}
                        onClick={() => handleDeleteUser(user.id)}
                        onMouseOver={(e) => (e.target.style.backgroundColor = "#b91c1c")}
                        onMouseOut={(e) => (e.target.style.backgroundColor = "#dc2626")}
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination Controls */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginTop: "1rem",
          }}
        >
          <button
            className="btn-primary"
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </button>
          <span>
            Page {currentPage} of {totalPages}
          </span>
          <button
            className="btn-primary"
            onClick={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            disabled={currentPage === totalPages}
          >
            Next
          </button>
        </div>
        
        {/* Edit User Modal */}
        {editUser && (
          <div className="delete-modal-overlay">
            <div className="delete-modal">
              <div style={{display: 'flex', justifyContent: 'space-between'}}>
                <h3>Edit User: {editUser.username}</h3>
                <button
                    type="button" style={{height: '40px', width: '40px', display: 'flex', justifyContent: "center", alignItems: 'center', backgroundColor: '#00a8b5', border: 'none', borderRadius: '10px', cursor: 'pointer' }}
                    onClick={() => setEditUser(null)}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="20" height="20" viewBox="0 0 50 50">
                      <path fill="#fff" d="M 9.15625 6.3125 L 6.3125 9.15625 L 22.15625 25 L 6.21875 40.96875 L 9.03125 43.78125 L 25 27.84375 L 40.9375 43.78125 L 43.78125 40.9375 L 27.84375 25 L 43.6875 9.15625 L 40.84375 6.3125 L 25 22.15625 Z"></path>
                    </svg>
                  </button>
              </div>
              <form onSubmit={handleEditUser}>
                <label>
                  Username:
                  <input
                    ref={editUsernameInputRef}
                    value={editUser.username}
                    onChange={(e) =>
                      setEditUser({ ...editUser, username: e.target.value })
                    }
                    placeholder="Username"
                    className="form-input"
                    required
                  />
                </label>
                <label>
                  Password (leave blank to keep unchanged):
                  <input
                    type="password"
                    value={editUser.password || ""}
                    onChange={(e) =>
                      setEditUser({ ...editUser, password: e.target.value })
                    }
                    placeholder="New Password"
                    className="form-input"
                  />
                </label>
                <label>
                  Role:
                  <select
                    value={editUser.role}
                    onChange={(e) =>
                      setEditUser({ ...editUser, role: e.target.value })
                    }
                    className="form-input"
                    required
                  >
                    <option value="">Select Role</option>
                    {roles.map((role) => (
                      <option key={role} value={role}>
                        {role}
                      </option>
                    ))}
                  </select>
                </label>
                <label>
                  Email:
                  <input
                    type="email"
                    value={editUser.email}
                    onChange={(e) =>
                      setEditUser({ ...editUser, email: e.target.value })
                    }
                    placeholder="Email"
                    className="form-input"
                    required
                  />
                </label>
                <label>
                  First Name:
                  <input
                    value={editUser.first_name || ""}
                    onChange={(e) =>
                      setEditUser({ ...editUser, first_name: e.target.value })
                    }
                    placeholder="First Name"
                    className="form-input"
                  />
                </label>
                <label>
                  Last Name:
                  <input
                    value={editUser.last_name || ""}
                    onChange={(e) =>
                      setEditUser({ ...editUser, last_name: e.target.value })
                    }
                    placeholder="Last Name"
                    className="form-input"
                  />
                </label>
                <label>
                  Job Role:
                  <input
                    value={editUser.job_role || ""}
                    onChange={(e) =>
                      setEditUser({ ...editUser, job_role: e.target.value })
                    }
                    placeholder="Job Role (e.g., Firefighter)"
                    className="form-input"
                  />
                </label>
                <label>
                  Main Location/Home Base:
                  <select
                    value={editUser.main_location}
                    onChange={(e) =>
                      setEditUser({
                        ...editUser,
                        main_location: e.target.value,
                      })
                    }
                    className="form-input"
                    required
                  >
                    <option value="">Select a Common Location</option>
                    {commonLocations.map((location) => (
                      <option key={location.id} value={location.name}>
                        {location.name}
                      </option>
                    ))}
                  </select>
                </label>
                <label>
                  Phone Number:
                  <input
                    type="tel"
                    value={editUser.phone || ""}
                    onChange={(e) =>
                      setEditUser({ ...editUser, phone: e.target.value })
                    }
                    placeholder="Phone Number (e.g., ************)"
                    className="form-input"
                  />
                </label>
                <label>
                  Home Address:
                  {mapsLoaded ? (
                    <Autocomplete
                      onLoad={onLoadEditAddressAutocomplete}
                      onPlaceChanged={() => onPlaceChanged(true)}
                    >
                      <input
                        value={editUser.home_address || ""}
                        onChange={(e) =>
                          setEditUser({
                            ...editUser,
                            home_address: e.target.value,
                          })
                        }
                        placeholder="Home Address"
                        className="form-input"
                      />
                    </Autocomplete>
                  ) : (
                    <input
                      value={editUser.home_address || ""}
                      onChange={(e) =>
                        setEditUser({
                          ...editUser,
                          home_address: e.target.value,
                        })
                      }
                      placeholder="Home Address (Maps Unavailable)"
                      className="form-input"
                    />
                  )}
                </label>
                <label>
                  City:
                  <input
                    value={editUser.city || ""}
                    onChange={(e) =>
                      setEditUser({ ...editUser, city: e.target.value })
                    }
                    placeholder="City"
                    className="form-input"
                  />
                </label>
                <label>
                  State:
                  <input
                    value={editUser.state || ""}
                    onChange={(e) =>
                      setEditUser({ ...editUser, state: e.target.value })
                    }
                    placeholder="State"
                    className="form-input"
                  />
                </label>
                <label>
                  Zip:
                  <input
                    value={editUser.zip || ""}
                    onChange={(e) =>
                      setEditUser({ ...editUser, zip: e.target.value })
                    }
                    placeholder="Zip Code"
                    className="form-input"
                  />
                </label>
                <div
                  style={{ display: "flex", gap: "1rem", marginTop: "1rem" }}
                >
                  <button type="submit" className="btn-primary">
                    Save Changes
                  </button>
                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={() => setEditUser(null)}
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
      <ToastContainer />
    </div>
  );
}

export default UserManagement;