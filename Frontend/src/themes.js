// src/themes.js
export const themes = {
    EMS: {
        primaryColor: '#1a73e8', // Blue for EMS
        secondaryColor: '#00bcd4', // Cyan
        backgroundColor: '#e3f2fd', // Light blue background
        iconSet: 'medical', // Use medical icons
        incidentTypes: ['Medical Emergency', 'Mass Casualty Incident']
    },
    Fire: {
        primaryColor: '#d32f2f', // Red for Fire
        secondaryColor: '#ff9800', // Orange
        backgroundColor: '#ffebee', // Light red background
        iconSet: 'fire', // Use fire icons
        incidentTypes: ['Structure Fire', 'Wildfire', 'Hazmat Incident']
    },
    Hospital: {
        primaryColor: '#388e3c', // Green for Hospital
        secondaryColor: '#4caf50', // Light green
        backgroundColor: '#e8f5e9', // Light green background
        iconSet: 'hospital', // Use hospital icons
        incidentTypes: ['Patient Surge', 'Internal Emergency']
    },
    LawEnforcement: {
        primaryColor: '#0d47a1', // Dark blue for Law Enforcement
        secondaryColor: '#ffeb3b', // Yellow
        backgroundColor: '#e3f2fd', // Light blue background
        iconSet: 'law', // Use law enforcement icons
        incidentTypes: ['Active Shooter', 'Crowd Control']
    },
    NaturalDisaster: {
        primaryColor: '#616161', // Gray for Natural Disaster
        secondaryColor: '#ff9800', // Orange
        backgroundColor: '#f5f5f5', // Light gray background
        iconSet: 'disaster', // Use disaster icons
        incidentTypes: ['Hurricane', 'Earthquake']
    }
    // Add more modules as needed
};