// src/Login.js
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import Footer from "./Footer";
import "./styles.css";
import { Link } from 'react-router-dom';

import config from './config'; 
const { baseUrl } = config;


function Login({ setToken, token }) {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  // console.log(process.env.NEXT_PUBLIC_API_BASE);

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await fetch(`${baseUrl}/login`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ username, password }),
      });
      if (!response.ok) {
        throw new Error("Invalid credentials");
      }
      const data = await response.json();
      localStorage.setItem("token", data.token);
      setToken(data.token);
      navigate("/dashboard");
      
      // navigate("/launch-event");
    } catch (err) {
      setError(err.message);
      setTimeout(() => setError(null), 3000);
    }
  };

  return (
    <div
      style={{ display: "flex", flexDirection: "column", minHeight: "100vh" }}
    >
      <div style={{ flex: "1" }}>
        <div className="container">
          <div className="card">
            <h1>Login</h1>
            {error && (
              <div
                style={{
                  backgroundColor: "var(--danger)",
                  color: "#fff",
                  padding: "10px",
                  borderRadius: "5px",
                  marginBottom: "10px",
                }}
              >
                {error}
              </div>
            )}
            <form onSubmit={handleSubmit}>
              <label>
                Username:
                <input
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder="Username"
                  className="form-input"
                />
              </label>
              <label>
                Password:
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Password"
                  className="form-input"
                />
              </label>
              <button type="submit" className="btn-primary">
                Login
              </button>

              <div style={{ textAlign: 'center', marginBottom: '10px' }}>
                Dont have an account ?
                <Link to="/signup" style={{ color: '#4f46e5', fontWeight: 'bold', textDecoration: 'underline', marginLeft: '5px' }}>
                  Sign Up
                </Link>
              </div>
              <div style={{ textAlign: 'center' }}>
                <Link to="/reset-password" style={{ color: '#4f46e5', fontWeight: 'bold', textDecoration: 'underline' }}>
                  Forgot Password?
                </Link>
              </div>
              
            </form>
          </div>
        </div>
      </div>
      <Footer token={token} />
    </div>
  );
}

export default Login;
