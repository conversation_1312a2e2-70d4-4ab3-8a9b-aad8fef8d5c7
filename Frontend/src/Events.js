import React, { useState, useEffect, useCallback } from "react";
import { Link } from "react-router-dom";
import { jwtDecode } from "jwt-decode";
import { toast } from "react-toastify";

import Footer from "./Footer";
import "./styles.css";

// Add simple CSS animations
const styles = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @media (max-width: 768px) {
    .grid-container {
      grid-template-columns: 1fr !important;
      gap: 12px !important;
      padding: 15px !important;
    }

    .control-panel {
      flex-direction: column !important;
      align-items: stretch !important;
    }

    .control-panel > div {
      justify-content: center !important;
    }

    .pagination-container {
      flex-wrap: wrap !important;
      gap: 3px !important;
    }
  }

  @media (max-width: 480px) {
    .grid-container {
      grid-template-columns: 1fr !important;
      padding: 10px !important;
    }

    .section-header {
      padding: 12px 15px !important;
      font-size: 1em !important;
    }

    .main-header {
      padding: 15px 0 !important;
    }

    .main-header h1 {
      font-size: 1.5em !important;
    }
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = styles;
  document.head.appendChild(styleSheet);
}

function Events({ token, onLogout }) {
  const [activeEvents, setActiveEvents] = useState([]);
  const [previousEvents, setPreviousEvents] = useState([]);
  const [selectedEvents, setSelectedEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [fetchError, setFetchError] = useState(null);
  const [role, setRole] = useState("");
  // const location = useLocation(); // Removed unused
  // const navigate = useNavigate(); // Used in EventListItem
  const baseUrl = process.env.REACT_APP_BASE_URL;

  // Pagination states
  const [activePage, setActivePage] = useState(1);
  const [previousPage, setPreviousPage] = useState(1);
  const eventsPerPage = 12; // Increased for better grid layout

  // View mode state - default to list view
  const [viewMode, setViewMode] = useState(() => {
    const saved = localStorage.getItem("events-viewMode");
    return saved || "list"; // Default to list view
  });

  // Initialize state from localStorage
  const [activeExpanded, setActiveExpanded] = useState(() => {
    const saved = localStorage.getItem("events-activeExpanded");
    return saved !== null ? JSON.parse(saved) : true;
  });
  const [previousExpanded, setPreviousExpanded] = useState(() => {
    const saved = localStorage.getItem("events-previousExpanded");
    return saved !== null ? JSON.parse(saved) : false;
  });

  const fetchEvents = useCallback(async () => {
    if (!token) {
      console.log("No token provided, skipping fetch");
      setFetchError("Please log in to view events");
      setLoading(false);
      return;
    }

    try {
      const decoded = jwtDecode(token);
      setRole(decoded.role || "unknown");
      const response = await fetch(`${baseUrl}/all-events`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      console.log("Response status:", response.status);
      if (!response.ok) {
        // Handle 403 Forbidden (Invalid token) by logging out
        if (response.status === 403) {
          console.error("Invalid token detected, logging out user");
          if (onLogout) {
            onLogout();
          }
          return;
        }
        const errorText = await response.text();
        throw new Error(`Failed to fetch events: ${response.status} - ${errorText}`);
      }
      const data = await response.json();
      setActiveEvents(data.filter((event) => event.status !== "resolved"));
      setPreviousEvents(data.filter((event) => event.status === "resolved"));
    } catch (err) {
      console.error("Error fetching events:", err.message);
      setFetchError(err.message);
    } finally {
      setLoading(false);
    }
  }, [token, onLogout, baseUrl]);

  useEffect(() => {
    fetchEvents();
  }, [fetchEvents]);

  // Sync state to localStorage on change
  useEffect(() => {
    localStorage.setItem("events-activeExpanded", JSON.stringify(activeExpanded));
    localStorage.setItem("events-previousExpanded", JSON.stringify(previousExpanded));
    localStorage.setItem("events-viewMode", viewMode);
  }, [activeExpanded, previousExpanded, viewMode]);

  const handleSelectEvent = (eventId) => {
    setSelectedEvents((prev) =>
      prev.includes(eventId) ? prev.filter((id) => id !== eventId) : [...prev, eventId]
    );
  };

  const handleDeleteSelected = async () => {
    if (selectedEvents.length === 0) {
      alert("No events selected for deletion.");
      return;
    }
    if (!window.confirm(`Are you sure you want to delete ${selectedEvents.length} event(s)?`)) return;

    try {
      setLoading(true);
      await Promise.all(
        selectedEvents.map((eventId) =>
          fetch(`${baseUrl}/events/${eventId}`, {
            method: "DELETE",
            headers: { Authorization: `Bearer ${token}` },
          }).then((res) => {
            if (!res.ok) throw new Error(`Failed to delete event ${eventId}`);
            toast.success( "Event deleted successfully!", {
              position: "top-right",
              autoClose: 3000,
            });
            return res.json();
          })
        )
      );
      await fetchEvents();
      setSelectedEvents([]);
      console.log("Deleted events:", selectedEvents);
    } catch (err) {
      console.error("Error deleting selected events:", err.message);
      setFetchError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteEvent = async (eventId) => {
    if (!window.confirm("Are you sure you want to delete this event?")) return;
    console.log("Deleting event with ID:", eventId);
    try {
      setLoading(true);
      const response = await fetch(`${baseUrl}/events/${eventId}`, {
        method: "DELETE",
        headers: { Authorization: `Bearer ${token}` },
      });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to delete event: ${response.status} - ${errorText}`);
      }
      toast.success("Event deleted successfully!", {
        position: "top-right",
        autoClose: 3000,
      });
      // await fetchEvents();
    } catch (err) {
      console.error("Error deleting event:", err.message);
      setFetchError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCloseEvent = async (eventId) => {
    if (!window.confirm("Are you sure you want to close this event?")) return;
    console.log("Closing event with ID:", eventId);
    try {
      setLoading(true);
      const response = await fetch(`${baseUrl}/event/${eventId}`, {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: "resolved" }),
      });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to close event: ${response.status} - ${errorText}`);
      }
      await fetchEvents();
    } catch (err) {
      console.error("Error closing event:", err.message);
      setFetchError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Pagination logic
  const getPaginatedEvents = (events, page) => {
    const startIndex = (page - 1) * eventsPerPage;
    const endIndex = startIndex + eventsPerPage;
    return events.slice(startIndex, endIndex);
  };

  const getPageNumbers = (totalEvents) => {
    const pageCount = Math.ceil(totalEvents / eventsPerPage);
    return Array.from({ length: pageCount }, (_, i) => i + 1);
  };

  // Modern responsive styles
  const containerStyle = {
    maxWidth: "1400px",
    margin: "0 auto",
    padding: "20px",
    fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
  };

  const headerStyle = {
    backgroundColor: "#fff",
    color: "#333",
    padding: "25px 0",
    marginBottom: "25px",
    borderRadius: "8px",
    textAlign: "center",
    border: "1px solid #e1e8ed",
  };

  const sectionStyle = {
    marginBottom: "25px",
    borderRadius: "8px",
    overflow: "hidden",
    backgroundColor: "#fff",
    border: "1px solid #e1e8ed",
  };

  const sectionHeaderStyle = {
    backgroundColor: "#f8f9fa",
    color: "#333",
    cursor: "pointer",
    padding: "15px 20px",
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    fontSize: "1.1em",
    fontWeight: "600",
    transition: "background-color 0.2s ease",
    borderBottom: "1px solid #e1e8ed",
  };

  const contentStyle = (isOpen) => ({
    maxHeight: isOpen ? "none" : "0",
    overflow: "hidden",
    transition: "max-height 0.3s ease",
    backgroundColor: "#fff",
  });

  const gridStyle = {
    display: "grid",
    gridTemplateColumns: "repeat(auto-fill, minmax(320px, 1fr))",
    gap: "15px",
    padding: "20px",
  };

  const listStyle = {
    padding: "0",
  };

  const cardStyle = {
    backgroundColor: "#fff",
    borderRadius: "6px",
    padding: "15px",
    border: "1px solid #e1e8ed",
    transition: "border-color 0.2s ease",
    position: "relative",
  };

  const listItemStyle = {
    backgroundColor: "#fff",
    padding: "15px 20px",
    borderBottom: "1px solid #e1e8ed",
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    transition: "background-color 0.2s ease",
  };

  const statusBadgeStyle = (status) => ({
    display: "inline-block",
    padding: "4px 8px",
    borderRadius: "4px",
    fontSize: "0.8em",
    fontWeight: "500",
    backgroundColor: status === "resolved" ? "#f8f9fa" : "#f8f9fa",
    color: status === "resolved" ? "#6c757d" : "#495057",
    border: "1px solid #dee2e6",
  });

  // Event card component
  const EventCard = ({ event, isActive = true }) => (
    <div
      className="event-card"
      style={{
        ...cardStyle,
        borderLeft: `3px solid ${isActive ? '#28a745' : '#6c757d'}`,
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.borderColor = "#007bff";
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.borderColor = "#e1e8ed";
      }}
    >
      {/* Selection checkbox */}
      <div style={{ position: "absolute", top: "12px", right: "12px" }}>
        <input
          type="checkbox"
          checked={selectedEvents.includes(event.id)}
          onChange={() => handleSelectEvent(event.id)}
          style={{
            width: "16px",
            height: "16px",
            cursor: "pointer",
          }}
        />
      </div>

      {/* Event title */}
      <h3 style={{
        margin: "0 0 10px 0",
        fontSize: "1.1em",
        fontWeight: "600",
        color: "#333",
        paddingRight: "35px",
        lineHeight: "1.3",
      }}>
        <Link
          to={`/dashboard/${event.id}`}
          style={{
            color: "inherit",
            textDecoration: "none",
          }}
          onMouseEnter={(e) => e.target.style.color = "#007bff"}
          onMouseLeave={(e) => e.target.style.color = "#333"}
        >
          {event.title || "Untitled Event"}
        </Link>
      </h3>

      {/* Status badge */}
      <div style={{ marginBottom: "15px" }}>
        <span style={statusBadgeStyle(event.status)}>
          {event.status}
        </span>
      </div>

      {/* Action buttons */}
      <div style={{
        display: "flex",
        gap: "8px",
        flexWrap: "wrap",
      }}>
        <button
          onClick={() => handleDeleteEvent(event.id)}
          disabled={role === "staff"}
          style={{
            padding: "6px 12px",
            borderRadius: "4px",
            border: "1px solid #dc3545",
            backgroundColor: role === "staff" ? "#f8f9fa" : "#dc3545",
            color: role === "staff" ? "#6c757d" : "white",
            fontSize: "0.85em",
            fontWeight: "500",
            cursor: role === "staff" ? "not-allowed" : "pointer",
            opacity: role === "staff" ? 0.6 : 1,
            transition: "all 0.2s ease",
          }}
        >
          Delete
        </button>

        {isActive && (
          <button
            onClick={() => handleCloseEvent(event.id)}
            disabled={role === "staff"}
            style={{
              padding: "6px 12px",
              borderRadius: "4px",
              border: "1px solid #6c757d",
              backgroundColor: role === "staff" ? "#f8f9fa" : "#6c757d",
              color: role === "staff" ? "#6c757d" : "white",
              fontSize: "0.85em",
              fontWeight: "500",
              cursor: role === "staff" ? "not-allowed" : "pointer",
              opacity: role === "staff" ? 0.6 : 1,
              transition: "all 0.2s ease",
            }}
          >
            Close
          </button>
        )}
      </div>
    </div>
  );

  // Event list item component
  const EventListItem = ({ event, isActive = true }) => (
    <div
      style={{
        ...listItemStyle,
        borderLeft: `3px solid ${isActive ? '#28a745' : '#6c757d'}`,
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = "#f8f9fa";
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = "#fff";
      }}
    >
      <div style={{ display: "flex", alignItems: "center", gap: "15px", flex: 1 }}>
        <input
          type="checkbox"
          checked={selectedEvents.includes(event.id)}
          onChange={() => handleSelectEvent(event.id)}
          style={{
            width: "16px",
            height: "16px",
            cursor: "pointer",
          }}
        />

        <div style={{ flex: 1 }}>
          <Link
            to={`/dashboard/${event.id}`}
            style={{
              color: "#333",
              textDecoration: "none",
              fontSize: "1em",
              fontWeight: "500",
            }}
            onMouseEnter={(e) => e.target.style.color = "#007bff"}
            onMouseLeave={(e) => e.target.style.color = "#333"}
          >
            {event.title || "Untitled Event"}
          </Link>
        </div>

        <span style={statusBadgeStyle(event.status)}>
          {event.status}
        </span>
      </div>

      <div style={{ display: "flex", gap: "8px", marginLeft: "15px" }}>
        <button
          onClick={() => handleDeleteEvent(event.id)}
          disabled={role === "staff"}
          style={{
            padding: "4px 8px",
            borderRadius: "4px",
            border: "1px solid #dc3545",
            backgroundColor: role === "staff" ? "#f8f9fa" : "#dc3545",
            color: role === "staff" ? "#6c757d" : "white",
            fontSize: "0.8em",
            cursor: role === "staff" ? "not-allowed" : "pointer",
            opacity: role === "staff" ? 0.6 : 1,
          }}
        >
          Delete
        </button>

        {isActive && (
          <button
            onClick={() => handleCloseEvent(event.id)}
            disabled={role === "staff"}
            style={{
              padding: "4px 8px",
              borderRadius: "4px",
              border: "1px solid #6c757d",
              backgroundColor: role === "staff" ? "#f8f9fa" : "#6c757d",
              color: role === "staff" ? "#6c757d" : "white",
              fontSize: "0.8em",
              cursor: role === "staff" ? "not-allowed" : "pointer",
              opacity: role === "staff" ? 0.6 : 1,
            }}
          >
            Close
          </button>
        )}
      </div>
    </div>
  );

  return (
    <div style={{ display: "flex", flexDirection: "column", minHeight: "100vh", backgroundColor: "#f8f9fa" }}>
      <div style={{ flex: "1" }}>
        <div style={containerStyle}>
          {/* Simple Header */}
          <div className="main-header" style={headerStyle}>
            <h1 style={{
              margin: "0",
              fontSize: "1.8em",
              fontWeight: "600",
              color: "#333",
            }}>
              Events
            </h1>
            <p style={{
              margin: "5px 0 0 0",
              fontSize: "0.9em",
              color: "#6c757d",
              fontWeight: "400",
            }}>
              Manage your events
            </p>
          </div>
          {loading ? (
            <div style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              padding: "60px 20px",
              fontSize: "1.2em",
              color: "#667eea",
            }}>
              <div style={{
                display: "flex",
                alignItems: "center",
                gap: "15px",
              }}>
                <div style={{
                  width: "40px",
                  height: "40px",
                  border: "4px solid #f3f3f3",
                  borderTop: "4px solid #667eea",
                  borderRadius: "50%",
                  animation: "spin 1s linear infinite",
                }}></div>
                Loading events...
              </div>
            </div>
          ) : fetchError ? (
            <div style={{
              backgroundColor: "#ffebee",
              color: "#c62828",
              padding: "20px",
              borderRadius: "12px",
              border: "1px solid #ffcdd2",
              textAlign: "center",
              fontSize: "1.1em",
            }}>
              ⚠️ Error: {fetchError}
            </div>
          ) : (
            <>
              {/* Control Panel */}
              <div className="control-panel" style={{
                backgroundColor: "#fff",
                padding: "15px 20px",
                borderRadius: "6px",
                marginBottom: "20px",
                border: "1px solid #e1e8ed",
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                flexWrap: "wrap",
                gap: "15px",
              }}>
                <div style={{ display: "flex", alignItems: "center", gap: "15px", flexWrap: "wrap" }}>
                  <button
                    onClick={handleDeleteSelected}
                    disabled={selectedEvents.length === 0 || role === "staff"}
                    style={{
                      padding: "8px 16px",
                      borderRadius: "4px",
                      border: "1px solid #dc3545",
                      backgroundColor: selectedEvents.length > 0 && role !== "staff" ? "#dc3545" : "#f8f9fa",
                      color: selectedEvents.length > 0 && role !== "staff" ? "white" : "#6c757d",
                      fontSize: "0.9em",
                      fontWeight: "500",
                      cursor: selectedEvents.length > 0 && role !== "staff" ? "pointer" : "not-allowed",
                      opacity: role === "staff" ? 0.6 : 1,
                      transition: "all 0.2s ease",
                    }}
                  >
                    Delete Selected ({selectedEvents.length})
                  </button>

                  <div style={{
                    padding: "6px 12px",
                    backgroundColor: "#f8f9fa",
                    borderRadius: "4px",
                    fontSize: "0.85em",
                    color: "#6c757d",
                    border: "1px solid #e1e8ed",
                  }}>
                    Total: {activeEvents.length + previousEvents.length} events
                  </div>
                </div>

                <div style={{ display: "flex", gap: "8px", alignItems: "center" }}>
                  <button
                    onClick={() => setViewMode(viewMode === "grid" ? "list" : "grid")}
                    style={{
                      padding: "6px 12px",
                      borderRadius: "4px",
                      border: "1px solid #dee2e6",
                      backgroundColor: "#fff",
                      color: "#495057",
                      cursor: "pointer",
                      fontSize: "0.85em",
                      transition: "all 0.2s ease",
                    }}
                  >
                    {viewMode === "grid" ? "List View" : "Grid View"}
                  </button>
                </div>
              </div>

              {/* Active Events Section */}
              <div style={sectionStyle}>
                <div
                  className="section-header"
                  style={sectionHeaderStyle}
                  onClick={() => setActiveExpanded(!activeExpanded)}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = "#e9ecef";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = "#f8f9fa";
                  }}
                >
                  <span>Active Events ({activeEvents.length})</span>
                  <span style={{
                    fontSize: "1em",
                    transition: "transform 0.2s ease",
                    transform: activeExpanded ? "rotate(90deg)" : "rotate(0deg)",
                  }}>
                    ▶
                  </span>
                </div>
                <div style={contentStyle(activeExpanded)}>
                  {activeEvents.length > 0 ? (
                    <>
                      {viewMode === "grid" ? (
                        <div className="grid-container" style={gridStyle}>
                          {getPaginatedEvents(activeEvents, activePage).map((event) => (
                            <EventCard key={event.id} event={event} isActive={true} />
                          ))}
                        </div>
                      ) : (
                        <div style={listStyle}>
                          {getPaginatedEvents(activeEvents, activePage).map((event) => (
                            <EventListItem key={event.id} event={event} isActive={true} />
                          ))}
                        </div>
                      )}
                      {/* Modern Pagination Controls for Active Events */}
                      {Math.ceil(activeEvents.length / eventsPerPage) > 1 && (
                        <div className="pagination-container" style={{
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          gap: "5px",
                          padding: "15px",
                          backgroundColor: "#f8f9fa",
                          borderTop: "1px solid #e1e8ed",
                        }}>
                          <button
                            onClick={() => setActivePage((prev) => Math.max(prev - 1, 1))}
                            disabled={activePage === 1}
                            style={{
                              padding: "6px 12px",
                              borderRadius: "4px",
                              border: "1px solid #dee2e6",
                              backgroundColor: activePage === 1 ? "#f8f9fa" : "#007bff",
                              color: activePage === 1 ? "#6c757d" : "#fff",
                              cursor: activePage === 1 ? "not-allowed" : "pointer",
                              fontSize: "0.85em",
                              transition: "all 0.2s ease",
                            }}
                          >
                            Previous
                          </button>

                          <div style={{ display: "flex", gap: "2px" }}>
                            {getPageNumbers(activeEvents.length).map((page) => (
                              <button
                                key={page}
                                onClick={() => setActivePage(page)}
                                style={{
                                  padding: "6px 10px",
                                  borderRadius: "4px",
                                  border: "1px solid #dee2e6",
                                  backgroundColor: activePage === page ? "#007bff" : "#fff",
                                  color: activePage === page ? "#fff" : "#495057",
                                  cursor: "pointer",
                                  fontSize: "0.85em",
                                  fontWeight: activePage === page ? "500" : "400",
                                  transition: "all 0.2s ease",
                                  minWidth: "32px",
                                }}
                              >
                                {page}
                              </button>
                            ))}
                          </div>

                          <button
                            onClick={() => setActivePage((prev) => Math.min(prev + 1, Math.ceil(activeEvents.length / eventsPerPage)))}
                            disabled={activePage === Math.ceil(activeEvents.length / eventsPerPage)}
                            style={{
                              padding: "6px 12px",
                              borderRadius: "4px",
                              border: "1px solid #dee2e6",
                              backgroundColor: activePage === Math.ceil(activeEvents.length / eventsPerPage) ? "#f8f9fa" : "#007bff",
                              color: activePage === Math.ceil(activeEvents.length / eventsPerPage) ? "#6c757d" : "#fff",
                              cursor: activePage === Math.ceil(activeEvents.length / eventsPerPage) ? "not-allowed" : "pointer",
                              fontSize: "0.85em",
                              transition: "all 0.2s ease",
                            }}
                          >
                            Next
                          </button>
                        </div>
                      )}
                    </>
                  ) : (
                    <div style={{
                      padding: "40px 20px",
                      textAlign: "center",
                      color: "#6c757d",
                      fontSize: "0.9em",
                    }}>
                      <p style={{ margin: "0" }}>No active events</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Previous Events Section */}
              <div style={sectionStyle}>
                <div
                  className="section-header"
                  style={sectionHeaderStyle}
                  onClick={() => setPreviousExpanded(!previousExpanded)}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = "#e9ecef";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = "#f8f9fa";
                  }}
                >
                  <span>Previous Events ({previousEvents.length})</span>
                  <span style={{
                    fontSize: "1em",
                    transition: "transform 0.2s ease",
                    transform: previousExpanded ? "rotate(90deg)" : "rotate(0deg)",
                  }}>
                    ▶
                  </span>
                </div>
                <div style={contentStyle(previousExpanded)}>
                  {previousEvents.length > 0 ? (
                    <>
                      {viewMode === "grid" ? (
                        <div className="grid-container" style={gridStyle}>
                          {getPaginatedEvents(previousEvents, previousPage).map((event) => (
                            <EventCard key={event.id} event={event} isActive={false} />
                          ))}
                        </div>
                      ) : (
                        <div style={listStyle}>
                          {getPaginatedEvents(previousEvents, previousPage).map((event) => (
                            <EventListItem key={event.id} event={event} isActive={false} />
                          ))}
                        </div>
                      )}
                      {/* Modern Pagination Controls for Previous Events */}
                      {Math.ceil(previousEvents.length / eventsPerPage) > 1 && (
                        <div className="pagination-container" style={{
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          gap: "5px",
                          padding: "15px",
                          backgroundColor: "#f8f9fa",
                          borderTop: "1px solid #e1e8ed",
                        }}>
                          <button
                            onClick={() => setPreviousPage((prev) => Math.max(prev - 1, 1))}
                            disabled={previousPage === 1}
                            style={{
                              padding: "6px 12px",
                              borderRadius: "4px",
                              border: "1px solid #dee2e6",
                              backgroundColor: previousPage === 1 ? "#f8f9fa" : "#6c757d",
                              color: previousPage === 1 ? "#6c757d" : "#fff",
                              cursor: previousPage === 1 ? "not-allowed" : "pointer",
                              fontSize: "0.85em",
                              transition: "all 0.2s ease",
                            }}
                          >
                            Previous
                          </button>

                          <div style={{ display: "flex", gap: "2px" }}>
                            {getPageNumbers(previousEvents.length).map((page) => (
                              <button
                                key={page}
                                onClick={() => setPreviousPage(page)}
                                style={{
                                  padding: "6px 10px",
                                  borderRadius: "4px",
                                  border: "1px solid #dee2e6",
                                  backgroundColor: previousPage === page ? "#6c757d" : "#fff",
                                  color: previousPage === page ? "#fff" : "#495057",
                                  cursor: "pointer",
                                  fontSize: "0.85em",
                                  fontWeight: previousPage === page ? "500" : "400",
                                  transition: "all 0.2s ease",
                                  minWidth: "32px",
                                }}
                              >
                                {page}
                              </button>
                            ))}
                          </div>

                          <button
                            onClick={() => setPreviousPage((prev) => Math.min(prev + 1, Math.ceil(previousEvents.length / eventsPerPage)))}
                            disabled={previousPage === Math.ceil(previousEvents.length / eventsPerPage)}
                            style={{
                              padding: "6px 12px",
                              borderRadius: "4px",
                              border: "1px solid #dee2e6",
                              backgroundColor: previousPage === Math.ceil(previousEvents.length / eventsPerPage) ? "#f8f9fa" : "#6c757d",
                              color: previousPage === Math.ceil(previousEvents.length / eventsPerPage) ? "#6c757d" : "#fff",
                              cursor: previousPage === Math.ceil(previousEvents.length / eventsPerPage) ? "not-allowed" : "pointer",
                              fontSize: "0.85em",
                              transition: "all 0.2s ease",
                            }}
                          >
                            Next
                          </button>
                        </div>
                      )}
                    </>
                  ) : (
                    <div style={{
                      padding: "40px 20px",
                      textAlign: "center",
                      color: "#6c757d",
                      fontSize: "0.9em",
                    }}>
                      <p style={{ margin: "0" }}>No previous events</p>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
      <Footer token={token} />
    </div>
  );
}

export default Events;