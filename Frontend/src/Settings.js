import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useUseType } from "./context/UseTypeContext";
import { useFontSize } from "./context/FontSizeContext";
import { FaPlus } from "react-icons/fa";
import { GiCycle } from "react-icons/gi";
import { toast } from "react-toastify";
import Swal from "sweetalert2";

// Cookie utility function to invalidate company settings cache
const deleteCookie = (name) => {
  document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;`;
};

const COMPANY_SETTINGS_COOKIE = 'alertcomm_company_settings';

function Settings({ role, token, userId, baseUrl }) {

  const {
    activeModules,
    selectedModule,
    updateSelectedModule,
    formConfig,
    updateFormConfig,
    locations,
    updateLocations,
  } = useUseType();

  // Ensure formConfig always includes default fields - wrapped in useMemo to avoid dependency issues
  const defaultFields = useMemo(() => [
    { name: "title", label: "Title", type: "text", required: true },
    { name: "info", label: "Info", type: "textarea", required: false },
    { name: "description", label: "Description", type: "textarea", required: false },
    { name: "scale", label: "Scale", type: "select", options: ["Small", "Medium", "Large"], required: true },
    { name: "urgency", label: "Urgency", type: "select", options: ["Low", "Medium", "High", "Immediate"], required: true },
  ], []);

  // Use useMemo to properly handle the form config for each module
  const allFormConfig = useMemo(() => {

    // The formConfig from context already contains the complete config for the selected module
    // including default fields, so we don't need to combine them again
    const moduleConfig = formConfig || defaultFields;

    // console.log('Module Form Config:', moduleConfig);
    return moduleConfig;
  }, [selectedModule, formConfig, defaultFields]);

  const [newField, setNewField] = useState({
    name: "",
    label: "",
    type: "text",
    required: false,
    options: [],
  });
  const [newLocation, setNewLocation] = useState({
    commonName: "",
    address: "",
    city: "",
    state: "",
    zip: "",
  });

  // AI Settings state
  const [aiSettings, setAiSettings] = useState({
    ai_prompts: {},
    ai_preconditions: {}
  });
  const [loadingAiSettings, setLoadingAiSettings] = useState(false);
  const [isAiSettingsExpanded, setIsAiSettingsExpanded] = useState(true);
  const [newModule, setNewModule] = useState("");
  const [moduleInfo, setModuleInfo] = useState({});
  const [showAddModule, setShowAddModule] = useState(false);

  // Collapse/Expand state for all sections
  const [sectionStates, setSectionStates] = useState({
    activeModule: true,
    customizeForm: true,
    manageLocations: true,
    manageResources: true,
    manageCompanyCodes: true,
    aiPreconditions: true
  });

  const toggleSection = (sectionName) => {
    setSectionStates(prev => ({
      ...prev,
      [sectionName]: !prev[sectionName]
    }));
  };
  // Use the fontSizeScale from the FontSizeContext
  const { fontSizeScale, setFontSizeScale } = useFontSize();
  const [editingLocation, setEditingLocation] = useState(null);
  const [editLocation, setEditLocation] = useState({
    commonName: "",
    address: "",
    city: "",
    state: "",
    zip: "",
  });

  // Resources management state
  const [resources, setResources] = useState([]);
  const [loadingResources, setLoadingResources] = useState(false);
  const [editingResource, setEditingResource] = useState(null);
  const [newResource, setNewResource] = useState({
    name: "",
    description: "",
    category: "",
  });
  const [newCode, setNewCode] = useState("");
  const [companyCode, setCompanyCode] = useState([]);
  // State to store the list of saved company codes
  const [savedCodes, setSavedCodes] = useState([]);
  const [name, setName] = useState(""); // State for company name
  const [description, setDescription] = useState(""); // State for company description
  const [companyId, setCompanyId] = useState(null); // State for company ID


  
  // Optimized fetchModuleInfo - only calls API when necessary
  const fetchModuleInfo = useCallback(async (forceRefresh = false) => {
    try {
      // console.log('fetchModuleInfo called, forceRefresh:', forceRefresh);

      // Check localStorage first (only if not forcing refresh)
      if (!forceRefresh) {
        const localModuleInfo = localStorage.getItem('moduleInfo');
        if (localModuleInfo) {
          setModuleInfo(JSON.parse(localModuleInfo));
          // console.log('Using cached moduleInfo from localStorage');
          return; // Exit early if we have cached data
        }
      }

      // Only fetch from backend if no cached data or forcing refresh
      // console.log('Fetching moduleInfo from API...');
      const response = await fetch(`${baseUrl}/company-settings`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setModuleInfo(data.moduleInfo || {});
        localStorage.setItem('moduleInfo', JSON.stringify(data.moduleInfo || {}));
        // console.log('ModuleInfo fetched and cached successfully');

        // Note: We don't update locations here because UseTypeContext handles location loading
        // The Settings page only manages user-added locations, not the global location state
      }
    } catch (err) {
      console.error("Error fetching module info:", err);
    }
  }, [baseUrl, token, setModuleInfo]);


  // Add a function to fetch saved company codes
  const fetchSavedCodes = useCallback(async () => {
    try {
      // console.log('Fetching saved company codes...');
      const response = await fetch(`${baseUrl}/user/company?user_id=${userId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const codes = await response.json();
        // console.log('Fetched company codes:', codes);
        setSavedCodes(codes || []);
      } else {
        // console.log('Failed to fetch company codes:', response.status);
      }
    } catch (err) {
      console.error('Error fetching company codes:', err);
    }
  }, [baseUrl, token, userId]);

  // useEffect(() => {
  //   if (userId) {
  //     // Only fetch company codes and settings once on mount
  //     fetchSavedCodes(); // This also sets company ID
  //     // fetchModuleInfo and fetchCompanySettings are handled by UseTypeContext
  //   }
  // }, [userId, fetchSavedCodes]);


  // Resources management functions
  const fetchResources = useCallback(async () => {
    try {
      setLoadingResources(true);
      const response = await fetch(`${baseUrl}/resources`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      if (response.ok) {
        const data = await response.json();
        setResources(data);
      } else {
        toast.error("Failed to fetch resources");
      }
    } catch (error) {
      console.error("Error fetching resources:", error);
      toast.error("Error fetching resources");
    } finally {
      setLoadingResources(false);
    }
  }, [baseUrl, token]);

  const handleAddResource = async () => {
    if (!newResource.name.trim()) {
      toast.error("Name is required");
      return;
    }

    try {
      const response = await fetch(`${baseUrl}/resources`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newResource),
      });

      if (response.ok) {
        const data = await response.json();
        setResources(prev => [...prev, data]);
        setNewResource({ name: "", description: "", category: "" });
        toast.success("Resource added successfully");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to add resource");
      }
    } catch (error) {
      console.error("Error adding resource:", error);
      toast.error("Error adding resource");
    }
  };

  const handleUpdateResource = async (id, updatedResource) => {
    try {
      const response = await fetch(`${baseUrl}/resources/${id}`, {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updatedResource),
      });

      if (response.ok) {
        const data = await response.json();
        setResources(prev => prev.map(r => r.id === id ? data : r));
        setEditingResource(null);
        toast.success("Resource updated successfully");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to update resource");
      }
    } catch (error) {
      console.error("Error updating resource:", error);
      toast.error("Error updating resource");
    }
  };

  const handleDeleteResource = async (id) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "This will deactivate the resource. You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, deactivate it!",
    });

    if (result.isConfirmed) {
      try {
        const response = await fetch(`${baseUrl}/resources/${id}`, {
          method: "DELETE",
          headers: { Authorization: `Bearer ${token}` },
        });

        if (response.ok) {
          setResources(prev => prev.filter(r => r.id !== id));
          toast.success("Resource deactivated successfully");
        } else {
          const error = await response.json();
          toast.error(error.error || "Failed to deactivate resource");
        }
      } catch (error) {
        console.error("Error deleting resource:", error);
        toast.error("Error deleting resource");
      }
    }
  };

  // Only fetch data once on mount when userId is available
  useEffect(() => {
    if (userId) {
      fetchModuleInfo(false); // Changed to false to avoid unnecessary API calls
      fetchSavedCodes();
      fetchResources();
    }
  }, [userId, fetchModuleInfo, fetchSavedCodes, fetchResources]); // Added fetchResources



  // Apply font size scaling to the document root
  useEffect(() => {
    document.documentElement.style.setProperty('--font-size-scale', fontSizeScale);
    localStorage.setItem('fontSizeScale', fontSizeScale);
  }, [fontSizeScale]);

  // AI Settings functions
  const loadAiSettings = useCallback(async () => {
    // console.log('🔄 loadAiSettings called - token:', !!token, 'baseUrl:', !!baseUrl);

    if (!token || !baseUrl) {
      // console.log('❌ Missing token or baseUrl for AI settings');
      return;
    }

    setLoadingAiSettings(true);
    try {
      const response = await fetch(`${baseUrl}/ai-settings`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        
        setAiSettings({
          ai_prompts: data.ai_prompts || {},
          ai_preconditions: data.ai_preconditions || {}
        });
        
      } else if (response.status === 403) {
        // console.log('Access denied to AI settings - user is not Super Admin');
        toast.warning('Access denied: Only Super Admin can access AI settings');
      } else {
        const errorText = await response.text();
        console.error('Failed to load AI settings:', response.status, errorText);
        toast.error(`Failed to load AI settings: ${response.status}`);
      }
    } catch (error) {
      console.error('Error loading AI settings:', error);
      toast.error(`Error loading AI settings: ${error.message}`);
    } finally {
      setLoadingAiSettings(false);
    }
  }, [baseUrl, token]);

  // Load AI settings on component mount and when token changes
  useEffect(() => {
    // console.log('useEffect triggered - userId:', userId, 'type:', typeof userId, 'token exists:', !!token);

    // Check if user is Super Admin (handle both string and number types)
    const isSupeAdmin = userId === 2 || userId === '2' || userId === 'ttornstrom' || role === 'commander';
    // console.log('Is Super Admin check:', isSupeAdmin);

    if (isSupeAdmin && token) {
      // console.log('Loading AI settings for user:', userId);
      loadAiSettings();
    } else {
      // console.log('Not loading AI settings - userId:', userId, 'isSupeAdmin:', isSupeAdmin, 'token:', !!token);
    }
  }, [userId, token, loadAiSettings]);

  const saveAiSettings = async () => {
    if (!token || !baseUrl) {
      toast.error('Missing authentication or configuration');
      return;
    }

    setLoadingAiSettings(true);
    try {
      console.log('💾 Saving AI settings:', JSON.stringify(aiSettings, null, 2));
      const response = await fetch(`${baseUrl}/ai-settings`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(aiSettings)
      });

      console.log('📡 Save response status:', response.status);

      // console.log('Save response status:', response.status);

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Save result:', result);
        toast.success('AI settings saved successfully!');

        // Reload settings to verify they were saved
        setTimeout(() => {
          console.log('🔄 Reloading settings to verify save...');
          loadAiSettings();
        }, 500);
      } else {
        const errorText = await response.text();
        console.error('Failed to save AI settings:', response.status, errorText);
        toast.error(`Failed to save AI settings: ${response.status}`);
      }
    } catch (error) {
      console.error('Error saving AI settings:', error);
      toast.error(`Error saving AI settings: ${error.message}`);
    } finally {
      setLoadingAiSettings(false);
    }
  };

  const updateAiPrompt = (promptType, value) => {
    console.log('🔄 updateAiPrompt called:', promptType, 'value length:', value.length);
    setAiSettings(prev => {
      const newSettings = {
        ...prev,
        ai_prompts: {
          ...prev.ai_prompts,
          [promptType]: value
        }
      };
      console.log('📝 Updated aiSettings:', newSettings);
      return newSettings;
    });
  };

  const updateAiPrecondition = (preconditionType, value) => {
    console.log('🔄 updateAiPrecondition called:', preconditionType, 'value length:', value.length);
    setAiSettings(prev => {
      const newSettings = {
        ...prev,
        ai_preconditions: {
          ...prev.ai_preconditions,
          [preconditionType]: value
        }
      };
      console.log('📝 Updated aiSettings:', newSettings);
      return newSettings;
    });
  };

  if (role !== "commander") {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
          fontSize: "24px",
          color: "#d32f2f",
          fontFamily: "-apple-system, BlinkMacSystemFont, sans-serif",
        }}
      >
        Access Denied
      </div>
    );
  }
  const handleModuleChange = (e) => {
    const newModule = e.target.value;
    updateSelectedModule(newModule);
  };
  
  // fetchModuleInfo is already defined above using useCallback
  
  const handleFontSizeChange = (change) => {
    setFontSizeScale(prevScale => {
      const newScale = Math.max(0.8, Math.min(1.4, prevScale + change));
      return parseFloat(newScale.toFixed(1));
    });
  };
  const handleAddField = () => {
    console.log('🔄 handleAddField called with newField:', JSON.stringify(newField, null, 2));
    console.log('📋 Current allFormConfig:', JSON.stringify(allFormConfig, null, 2));

    if (!newField.name || !newField.label) {
      toast.error('Please fill in both Name and Label fields', {
        position: "top-right",
        autoClose: 3000,
      });
      return;
    }

    // Check if field name already exists
    if (allFormConfig.some(field => field.name === newField.name)) {
      toast.error('Field name already exists', {
        position: "top-right",
        autoClose: 3000,
      });
      return;
    }

    const updatedConfig = [...allFormConfig, { ...newField }];
    console.log('📝 Updated config after adding field:', JSON.stringify(updatedConfig, null, 2));

    // FIXED: Update local state only, don't save to backend yet
    updateFormConfig(updatedConfig, false);
    setNewField({
      name: "",
      label: "",
      type: "text",
      required: false,
      options: [],
    });

    toast.success('Field added successfully!', {
      position: "top-right",
      autoClose: 3000,
    });
  };

  const handleRemoveField = (index) => {
    const field = allFormConfig[index];
    // Don't allow removing default fields
    if (defaultFields.some(defaultField => defaultField.name === field.name)) {
      toast.error('Cannot remove default fields (Title, Info, Description, Scale, Urgency)', {
        position: "top-right",
        autoClose: 3000,
      });
      return;
    }

    // Remove the field from the config
    const updatedConfig = allFormConfig.filter((_, i) => i !== index);
    updateFormConfig(updatedConfig, false); // Don't save to backend yet

    toast.success('Field removed successfully!', {
      position: "top-right",
      autoClose: 3000,
    });
  };

  const handleFieldChange = (index, key, value) => {
    // Create a copy of the current form config and update the specific field
    const updatedConfig = [...allFormConfig];
    updatedConfig[index] = { ...updatedConfig[index], [key]: value };

    // Update the form config for the current module (local state only)
    updateFormConfig(updatedConfig, false); // Don't save to backend yet
  };

  // Add a function to save the form configuration to the backend
  const handleSaveFormConfig = async () => {
    try {
      console.log('💾 FIXED: Using context updateFormConfig instead of duplicate API call');
      console.log('📋 Current allFormConfig:', JSON.stringify(allFormConfig, null, 2));

      // FIXED: Use the context function which already handles the API call properly
      // This prevents duplicate API calls and ensures state synchronization
      await updateFormConfig(allFormConfig);

      toast.success('Form configuration saved successfully!', {
        position: "top-right",
        autoClose: 3000,
      });

    } catch (err) {
      console.error("Error saving form config:", err);
      toast.error('Failed to save form configuration: ' + err.message, {
        position: "top-right",
        autoClose: 3000,
      });
    }
  };



  const handleRemoveLocation = async (index) => {
    const location = locations[index];
    const confirmDelete = window.confirm(
      `Are you sure you want to delete "${location.commonName}"?\n\nAddress: ${location.address}\nCity: ${location.city}, ${location.state} ${location.zip}`
    );

    if (confirmDelete) {
      const updatedLocations = locations.filter((_, i) => i !== index);
      updateLocations(updatedLocations);

      // Refresh module info after deleting location
      await fetchModuleInfo(true);
      // console.log('Location deleted successfully');
    }
  };

  const handleEditLocation = (index) => {
    setEditingLocation(index);
    setEditLocation({ ...locations[index] });
    // Clear the add form when editing
    setNewLocation({
      commonName: "",
      address: "",
      city: "",
      state: "",
      zip: "",
    });
  };

  const handleSaveEdit = async () => {
    if (!editLocation.commonName || !editLocation.address) {
      alert('Please fill in both Common Name and Address fields');
      return;
    }

    const updatedLocations = [...locations];
    updatedLocations[editingLocation] = { ...editLocation };
    updateLocations(updatedLocations);
    setEditingLocation(null);
    setEditLocation({
      commonName: "",
      address: "",
      city: "",
      state: "",
      zip: "",
    });

    // Refresh module info after editing location
    await fetchModuleInfo(true);
    // console.log('Location updated successfully');
  };

  const handleCancelEdit = () => {
    setEditingLocation(null);
    setEditLocation({
      commonName: "",
      address: "",
      city: "",
      state: "",
      zip: "",
    });
  };

  const handleAddLocation = async () => {
    if (!newLocation.commonName || !newLocation.address) {
      alert('Please fill in both Common Name and Address fields');
      return;
    }

    // console.log('Adding new location:', newLocation);
    const updatedLocations = [newLocation, ...locations]; // Add to beginning for latest first
    updateLocations(updatedLocations);
    setNewLocation({
      commonName: "",
      address: "",
      city: "",
      state: "",
      zip: ""
    });

    // Refresh module info after adding location
    await fetchModuleInfo(true);
    // console.log('Location added successfully');
  };

  const generateRandomCode = () => {
    const randomCode = Math.floor(100000 + Math.random() * 900000).toString(); // Generate a 6-digit random code
    setNewCode(randomCode); // Set the 6-digit random code
  };

  //validations
  const validations = () => {
    // Validate company name
    if (!name) {
      toast.error("Please enter a valid company name.", {
        position: "top-right",
        autoClose: 3000,
      });
      return false;
    }

    if (!newCode || newCode.length < 4) {
      toast.error(
        "Please enter a valid company code (at least 6 characters).",
        {
          position: "top-right",
          autoClose: 3000,
        }
      );
      return false;
    }

    // if (!description ) {
    //   toast.error('Please enter a description .', {
    //     position: 'top-right',
    //     autoClose: 3000,
    //   });
    //   return false;
    // }

    if (!newCode.trim()) {
      toast.error("Please enter or generate a valid code before saving.", {
        position: "top-right",
        autoClose: 3000,
      });
      return false;
    }

    return true;
  };

  // Save the company code
  const saveCode = async () => {
    if (!validations()) {
      return;
    }
    try {
      const response = await fetch(`${baseUrl}/user/store-company`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          name,
          description,
          code: newCode,
          user_id: userId,
        }),
      });

      if (response.ok) {


        setSavedCodes((prevCodes) => [
          ...prevCodes,
          { name, description, code: newCode },
        ]);
        setNewCode(""); // Clear the input field
        setName(""); // Clear the name field
        setDescription(""); // Clear the description field

        // Invalidate company settings cache when new company is saved
        deleteCookie(COMPANY_SETTINGS_COOKIE);

        toast.success("Code saved successfully!", {
          position: "top-right",
          autoClose: 3000,
        });
      } else {
        const errorData = await response.json();
        console.error("Error response:", errorData);

        const errorMessage =
          errorData.error ||
          errorData.message ||
          "Failed to save the code. Please try again.";
        toast.error(errorMessage, {
          position: "top-right",
          autoClose: 3000,
        });
      }
    } catch (error) {
      console.error("Error saving company data:", error);

      toast.error("An unexpected error occurred. Please try again.", {
        position: "top-right",
        autoClose: 3000,
      });
    }
  };

  // fetchCompanyId is already defined above

  //delete company code
  const handleDeleteCode = async (index, id) => {
    try {
      // Display confirmation dialog using SweetAlert2
      const result = await Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "Cancel",
        confirmButtonColor: "#d32f2f",
        cancelButtonColor: "#777",
      });

      if (result.isConfirmed) {
        const response = await fetch(
          `${baseUrl}/user/delete-company/${id}?user_id=${userId}`,
          {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.ok) {
          setSavedCodes((prevCodes) => prevCodes.filter((_, i) => i !== index));

          // Invalidate company settings cache when company is deleted
          deleteCookie(COMPANY_SETTINGS_COOKIE);
          // console.log("Company settings cache invalidated due to company deletion");

          toast.success("Company code deleted successfully!", {
            position: "top-right",
            autoClose: 3000,
          });

        } else {
          throw new Error(`Failed to delete: ${response.statusText}`);
        }
      }
    } catch (error) {
      console.error("Error deleting company code:", error);
      toast.error("An unexpected error occurred. Please try again.", {
        position: "top-right",
        autoClose: 3000,
      });
    }
  };

  const handleCopyCode = (code) => {
    navigator.clipboard.writeText(code);

    toast.success("Code copied to clipboard!", {
      position: "top-right",
      autoClose: 3000,
    });
  };

  return (
    <>
      <style>
        {`
          /* Update the global font-size-scale variable */
          :root {
            --font-size-scale: ${fontSizeScale};
          }
          
          /* Font size controls styling */
          
          .font-size-controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            background-color: white;
            padding: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
          }
          
          .font-size-btn {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            border: none;
            background-color: #f0f0f0;
            color: #333;
            font-size: 18px;
            cursor: pointer;
            transition: background-color 0.2s;
          }
          
          .font-size-btn:hover {
            background-color: #e0e0e0;
          }
          
          .font-size-value {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
            min-width: 40px;
          }

          .locations-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            align-items: start;
          }

          .locations-list-container,
          .locations-form-container {
            height: 400px;
            display: flex;
            flex-direction: column;
          }

          .locations-list {
            flex: 1;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fff;
          }

          .locations-form {
            height: 100%;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background-color: #fff;
            display: flex;
            flex-direction: column;
          }

          @media (max-width: 768px) {
            .locations-container {
              grid-template-columns: 1fr;
              gap: 20px;
            }

            .locations-list-container,
            .locations-form-container {
              height: auto;
              min-height: 400px;
            }
          }

          @media (max-width: 480px) {
            .locations-container {
              gap: 15px;
            }
          }

          /* Enhanced Settings Page Styles */
          .settings-section {
            background: #fff;
            border-radius: 12px;
            border: 1px solid #e0e0e0;
            margin-bottom: 30px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            overflow: hidden;
          }

          .settings-section-header {
            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
            color: white !important;
            padding: 12px 20px;
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.2s ease;
            min-height: auto;
          }

          .settings-section-header:hover {
            background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
          }

          .expand-arrow {
            font-size: 16px;
            transition: transform 0.2s ease;
          }

          .expand-arrow.expanded {
            transform: rotate(90deg);
          }

          .settings-section-content {
            padding: 25px;
          }

          .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
          }

          .form-field {
            display: flex;
            flex-direction: column;
          }

          .form-label {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
          }

          .form-input {
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s ease;
          }

          .form-input:focus {
            outline: none;
            border-color: #1976d2;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
          }

          .btn-primary {
            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
          }

          .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
          }

          .btn-secondary {
            background: #f5f5f5;
            color: #666;
            border: 2px solid #e0e0e0;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
          }

          .btn-secondary:hover {
            background: #e0e0e0;
            border-color: #ccc;
          }

          .btn-danger {
            background: linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
          }

          .btn-danger:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3);
          }

          .settings-header {
            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
            color: white;
            padding: 30px 20px;
            margin: -20px -20px 30px -20px;
            text-align: center;
          }

          .settings-title {
            font-size: 28px;
            font-weight: 700;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }

          .settings-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin: 8px 0 0 0;
          }
        `}
      </style>
      <div className="container">
        <div
          className="card"
          style={{ height: "100% !important,", maxHeight: "100% " }}
        >
        <div className="settings-header">
          <h1 className="settings-title" style={{ color: "#fff" }}>Settings</h1>
          <p className="settings-subtitle">Configure your AlertComm system preferences</p>

        </div>
        
        {/* Font Size Controls */}
        {/* <div className="font-size-controls">
          <button 
            className="font-size-btn" 
            onClick={() => handleFontSizeChange(-0.1)}
            title="Decrease font size"
          >
            -
          </button>
          <span className="font-size-value">{Math.round(fontSizeScale * 100)}%</span>
          <button 
            className="font-size-btn" 
            onClick={() => handleFontSizeChange(0.1)}
            title="Increase font size"
          >
            +
          </button>
        </div> */}
        {/* Module Management */}
        <section
          style={{
            marginBottom: "40px",
            padding: "20px",
            border: "1px solid #e0e0e0",
            borderRadius: "8px",
            backgroundColor: "#fafafa",
          }}
        >
          <h2
            style={{
              fontSize: "20px",
              fontWeight: "500",
              color: "#333",
              marginBottom: "15px",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center"
            }}
          >
            <span>Manage Modules</span>
          </h2>
          
          {/* Module Selection */}
          <div style={{ marginBottom: "20px" }}>
            <label
              style={{
                display: "block",
                fontSize: "16px",
                fontWeight: "500",
                color: "#555",
                marginBottom: "8px",
              }}
            >
              Select Active Module
            </label>
            <select
              value={selectedModule}
              onChange={handleModuleChange}
              style={{
                width: "100%",
                padding: "10px",
                fontSize: "16px",
                border: "1px solid #ccc",
                borderRadius: "6px",
                backgroundColor: "#fff",
                outline: "none",
                transition: "border-color 0.2s",
              }}
              onFocus={(e) => (e.target.style.borderColor = "#1976d2")}
              onBlur={(e) => (e.target.style.borderColor = "#ccc")}
            >
              {(activeModules && activeModules.length > 0 ? activeModules : ["EMS", "Fire", "Police", "Medical"]).map((module) => (
                <option key={module} value={module}>
                  {module}
                </option>
              ))}
            </select>
          </div>
          

        </section>

        {/* Customize Launch Form */}
        <section className="settings-section">
          <h2
            className="settings-section-header"
            onClick={() => toggleSection('customizeForm')}
          >
            <span>Customize Launch Form for {selectedModule}</span>
            <span className={`expand-arrow ${sectionStates.customizeForm ? 'expanded' : ''}`}>
              ▶
            </span>
          </h2>
          {sectionStates.customizeForm && (
          <div className="settings-section-content">
          <div style={{ 
            marginBottom: "20px",
            backgroundColor: "#fff",
            padding: "15px",
            borderRadius: "8px",
            border: "1px solid #e0e0e0"
          }}>
            <h3
              style={{
                fontSize: "18px",
                fontWeight: "500",
                color: "#555",
                marginBottom: "15px",
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                borderBottom: "1px solid #eee",
                paddingBottom: "10px"
              }}
            >
              <span>Current Fields</span>
              <span style={{ fontSize: "14px", color: "#777" }}>
                {allFormConfig.length} field{allFormConfig.length !== 1 ? 's' : ''}
              </span>
            </h3>
            {allFormConfig.length > 0 ? (
              <ul style={{ listStyle: "none", padding: 0 }}>
                {allFormConfig.map((field, index) => (
                  <li
                    key={index}
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "12px",
                      marginBottom: "15px",
                      flexWrap: "wrap",
                      padding: "10px",
                      backgroundColor: index % 2 === 0 ? "#f9f9f9" : "#fff",
                      borderRadius: "6px",
                      border: "1px solid #eee"
                    }}
                  >
                    <div style={{ flex: "1 1 200px" }}>
                      <label
                        style={{
                          display: "block",
                          fontSize: "12px",
                          fontWeight: "500",
                          color: "#666",
                          marginBottom: "4px",
                        }}
                      >
                        Field Label
                      </label>
                      <input
                        type="text"
                        value={field.label}
                        onChange={(e) =>
                          handleFieldChange(index, "label", e.target.value)
                        }
                        placeholder="Field Label"
                        style={{
                          width: "100%",
                          padding: "8px 10px",
                          fontSize: "14px",
                          border: "1px solid #ccc",
                          borderRadius: "6px",
                          outline: "none",
                          transition: "border-color 0.2s, box-shadow 0.2s",
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = "#1976d2";
                          e.target.style.boxShadow = "0 0 0 2px rgba(25, 118, 210, 0.1)";
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = "#ccc";
                          e.target.style.boxShadow = "none";
                        }}
                      />
                    </div>
                    
                    <div style={{ flex: "1 1 150px" }}>
                      <label
                        style={{
                          display: "block",
                          fontSize: "12px",
                          fontWeight: "500",
                          color: "#666",
                          marginBottom: "4px",
                        }}
                      >
                        Field Type
                      </label>
                      <select
                        value={field.type}
                        onChange={(e) =>
                          handleFieldChange(index, "type", e.target.value)
                        }
                        style={{
                          width: "100%",
                          padding: "8px 10px",
                          fontSize: "14px",
                          border: "1px solid #ccc",
                          borderRadius: "6px",
                          backgroundColor: "#fff",
                          outline: "none",
                          transition: "border-color 0.2s, box-shadow 0.2s",
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = "#1976d2";
                          e.target.style.boxShadow = "0 0 0 2px rgba(25, 118, 210, 0.1)";
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = "#ccc";
                          e.target.style.boxShadow = "none";
                        }}
                      >
                        <option value="text">Text</option>
                        <option value="textarea">Textarea</option>
                        <option value="select">Select</option>
                        <option value="checkbox">Checkbox</option>
                        <option value="autocomplete">Autocomplete</option>
                      </select>
                    </div>
                    {field.type === "select" && (
                      <div style={{ flex: "1 1 200px" }}>
                        <label
                          style={{
                            display: "block",
                            fontSize: "12px",
                            fontWeight: "500",
                            color: "#666",
                            marginBottom: "4px",
                          }}
                        >
                          Options (comma-separated)
                        </label>
                        <input
                          type="text"
                          value={(field.options || []).join(",")}
                          onChange={(e) =>
                            handleFieldChange(
                              index,
                              "options",
                              e.target.value.split(",").map((opt) => opt.trim())
                            )
                          }
                          placeholder="Option1, Option2, Option3"
                          style={{
                            width: "100%",
                            padding: "8px 10px",
                            fontSize: "14px",
                            border: "1px solid #ccc",
                            borderRadius: "6px",
                            outline: "none",
                            transition: "border-color 0.2s, box-shadow 0.2s",
                          }}
                          onFocus={(e) => {
                            e.target.style.borderColor = "#1976d2";
                            e.target.style.boxShadow = "0 0 0 2px rgba(25, 118, 210, 0.1)";
                          }}
                          onBlur={(e) => {
                            e.target.style.borderColor = "#ccc";
                            e.target.style.boxShadow = "none";
                          }}
                        />
                      </div>
                    )}
                    <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
                      <label
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: "5px",
                          fontSize: "14px",
                          color: "#555",
                          whiteSpace: "nowrap",
                        }}
                      >
                        <input
                          type="checkbox"
                          checked={field.required}
                          onChange={(e) =>
                            handleFieldChange(index, "required", e.target.checked)
                          }
                          style={{
                            width: "16px",
                            height: "16px",
                            cursor: "pointer",
                            accentColor: "#1976d2",
                          }}
                        />
                        Required
                      </label>
                      
                      {defaultFields.some(defaultField => defaultField.name === field.name) ? (
                        <span
                          style={{
                            padding: "4px 8px",
                            fontSize: "12px",
                            color: "#666",
                            backgroundColor: "#f5f5f5",
                            border: "1px solid #ddd",
                            borderRadius: "4px",
                            fontStyle: "italic",
                          }}
                        >
                          Default Field
                        </span>
                      ) : (
                        <button
                          onClick={() => handleRemoveField(index)}
                          style={{
                            padding: "4px 10px",
                            fontSize: "13px",
                            color: "#fff",
                            backgroundColor: "#d32f2f",
                            border: "none",
                            borderRadius: "4px",
                            cursor: "pointer",
                            transition: "background-color 0.2s",
                          }}
                          onMouseOver={(e) =>
                            (e.target.style.backgroundColor = "#b71c1c")
                          }
                          onMouseOut={(e) =>
                            (e.target.style.backgroundColor = "#d32f2f")
                          }
                        >
                          Remove
                        </button>
                      )}
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <p style={{ color: "#777", fontSize: "16px" }}>
                No custom fields defined.
              </p>
            )}
          </div>
          <div style={{ 
            marginTop: "25px",
            backgroundColor: "#fff",
            padding: "15px",
            borderRadius: "8px",
            border: "1px solid #e0e0e0" 
          }}>
            <h3
              style={{
                fontSize: "18px",
                fontWeight: "500",
                color: "#555",
                marginBottom: "15px",
                borderBottom: "1px solid #eee",
                paddingBottom: "10px",
              }}
            >
              Add New Field
            </h3>
            <div style={{ display: "flex", flexWrap: "wrap", gap: "15px" }}>
              <div style={{ flex: "1 1 200px" }}>
                <label
                  style={{
                    display: "block",
                    fontSize: "12px",
                    fontWeight: "500",
                    color: "#666",
                    marginBottom: "4px",
                  }}
                >
                  Field Name
                </label>
                <input
                  type="text"
                  value={newField.name}
                  onChange={(e) => {
                    console.log('📝 Field name changed to:', e.target.value);
                    setNewField({ ...newField, name: e.target.value });
                  }}
                  placeholder="Field Name (e.g., custom_field_1)"
                  style={{
                    width: "100%",
                    padding: "8px 10px",
                    fontSize: "14px",
                    border: "1px solid #ccc",
                    borderRadius: "6px",
                    outline: "none",
                    transition: "border-color 0.2s, box-shadow 0.2s",
                  }}
                  onFocus={(e) => {
                    e.target.style.borderColor = "#1976d2";
                    e.target.style.boxShadow = "0 0 0 2px rgba(25, 118, 210, 0.1)";
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = "#ccc";
                    e.target.style.boxShadow = "none";
                  }}
                />
              </div>
              
              <div style={{ flex: "1 1 200px" }}>
                <label
                  style={{
                    display: "block",
                    fontSize: "12px",
                    fontWeight: "500",
                    color: "#666",
                    marginBottom: "4px",
                  }}
                >
                  Field Label
                </label>
                <input
                  type="text"
                  value={newField.label}
                  onChange={(e) => {
                    console.log('📝 Field label changed to:', e.target.value);
                    setNewField({ ...newField, label: e.target.value });
                  }}
                  placeholder="Field Label"
                  style={{
                    width: "100%",
                    padding: "8px 10px",
                    fontSize: "14px",
                    border: "1px solid #ccc",
                    borderRadius: "6px",
                    outline: "none",
                    transition: "border-color 0.2s, box-shadow 0.2s",
                  }}
                  onFocus={(e) => {
                    e.target.style.borderColor = "#1976d2";
                    e.target.style.boxShadow = "0 0 0 2px rgba(25, 118, 210, 0.1)";
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = "#ccc";
                    e.target.style.boxShadow = "none";
                  }}
                />
              </div>
              
              <div style={{ flex: "1 1 150px" }}>
                <label
                  style={{
                    display: "block",
                    fontSize: "12px",
                    fontWeight: "500",
                    color: "#666",
                    marginBottom: "4px",
                  }}
                >
                  Field Type
                </label>
                <select
                  value={newField.type}
                  onChange={(e) =>
                    setNewField({ ...newField, type: e.target.value })
                  }
                  style={{
                    width: "100%",
                    padding: "8px 10px",
                    fontSize: "14px",
                    border: "1px solid #ccc",
                    borderRadius: "6px",
                    backgroundColor: "#fff",
                    outline: "none",
                    transition: "border-color 0.2s, box-shadow 0.2s",
                  }}
                  onFocus={(e) => {
                    e.target.style.borderColor = "#1976d2";
                    e.target.style.boxShadow = "0 0 0 2px rgba(25, 118, 210, 0.1)";
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = "#ccc";
                    e.target.style.boxShadow = "none";
                  }}
                >
                <option value="text">Text</option>
                <option value="textarea">Textarea</option>
                <option value="select">Select</option>
                <option value="checkbox">Checkbox</option>
                <option value="autocomplete">Autocomplete</option>
              </select>
              </div>
              
              {newField.type === "select" && (
                <div style={{ flex: "1 1 200px" }}>
                  <label
                    style={{
                      display: "block",
                      fontSize: "12px",
                      fontWeight: "500",
                      color: "#666",
                      marginBottom: "4px",
                    }}
                  >
                    Options (comma-separated)
                  </label>
                  <input
                    type="text"
                    value={newField.options.join(",")}
                    onChange={(e) =>
                      setNewField({
                        ...newField,
                        options: e.target.value
                          .split(",")
                          .map((opt) => opt.trim()),
                      })
                    }
                    placeholder="Option1, Option2, Option3"
                    style={{
                      width: "100%",
                      padding: "8px 10px",
                      fontSize: "14px",
                      border: "1px solid #ccc",
                      borderRadius: "6px",
                      outline: "none",
                      transition: "border-color 0.2s, box-shadow 0.2s",
                    }}
                    onFocus={(e) => {
                      e.target.style.borderColor = "#1976d2";
                      e.target.style.boxShadow = "0 0 0 2px rgba(25, 118, 210, 0.1)";
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = "#ccc";
                      e.target.style.boxShadow = "none";
                    }}
                  />
                </div>
              )}
              
              <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
                <label
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "5px",
                    fontSize: "14px",
                    color: "#555",
                    whiteSpace: "nowrap",
                  }}
                >
                  <input
                    type="checkbox"
                    checked={newField.required}
                    onChange={(e) =>
                      setNewField({ ...newField, required: e.target.checked })
                    }
                    style={{ 
                      width: "16px", 
                      height: "16px", 
                      cursor: "pointer",
                      accentColor: "#1976d2",
                    }}
                  />
                  Required
                </label>
                
                <button
                  onClick={handleAddField}
                  className="btn-primary"
                  style={{ marginLeft: "auto" }}
                >
                  <FaPlus size={12} />
                  Add Field
                </button>
              </div>
            </div>

            {/* Save Form Configuration Button */}
            <div style={{
              marginTop: "20px",
              paddingTop: "20px",
              borderTop: "1px solid #e0e0e0",
              display: "flex",
              justifyContent: "center"
            }}>
              <button
                onClick={handleSaveFormConfig}
                className="btn-primary"
                style={{
                  padding: "12px 30px",
                  fontSize: "16px",
                  fontWeight: "600",
                  marginRight: "10px"
                }}
              >
                💾 Save Form Configuration
              </button>

            </div>
          </div>
          </div>
          )}
        </section>
        {/* Manage Main Locations */}
        <section className="settings-section">
          <h2
            className="settings-section-header"
            onClick={() => toggleSection('manageLocations')}
          >
            <span>Manage Main Locations</span>
            <span className={`expand-arrow ${sectionStates.manageLocations ? 'expanded' : ''}`}>
              ▶
            </span>
          </h2>
          {sectionStates.manageLocations && (
          <div className="settings-section-content">


          {/* Side-by-side layout */}
          <div className="locations-container">
            {/* Left side - Current Locations List */}
            <div className="locations-list-container">
              <h3
                style={{
                  fontSize: "18px",
                  fontWeight: "500",
                  color: "#555",
                  marginBottom: "15px",
                }}
              >
                Current Locations ({locations?.length || 0})
              </h3>
              
              <div className="locations-list" style={{ 
                border: "1px solid #ddd", 
                borderRadius: "8px", 
                backgroundColor: "#fff",
                maxHeight: "400px",
                overflowY: "auto"
              }}>
                {locations && locations.length > 0 ? (
                  <div>
                    {[...locations].map((location, index) => (
                      <div
                        key={index}
                        style={{
                          padding: "15px",
                          borderBottom: index < locations.length - 1 ? "1px solid #eee" : "none",
                          backgroundColor: "#fff",
                          transition: "background-color 0.2s",
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = "#f8f9fa"}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = "#fff"}
                      >
                        <div>
                            <div style={{ display: "flex", justifyContent: "space-between", alignItems: "flex-start", marginBottom: "8px" }}>
                              <div style={{ flex: 1 }}>
                                <div style={{ fontSize: "16px", color: "#333", fontWeight: "600", marginBottom: "4px" }}>
                                  {location.commonName}
                                </div>
                                <div style={{ fontSize: "14px", color: "#666", lineHeight: "1.4" }}>
                                  {location.address}
                                  {location.city && <><br />{location.city}{location.state && `, ${location.state}`}{location.zip && ` ${location.zip}`}</>}
                                </div>
                              </div>
                            </div>
                            <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
                              <button
                                onClick={() => handleEditLocation(index)}
                                style={{
                                  padding: "6px 12px",
                                  fontSize: "12px",
                                  color: "#1976d2",
                                  backgroundColor: "#e3f2fd",
                                  border: "1px solid #1976d2",
                                  borderRadius: "4px",
                                  cursor: "pointer",
                                  transition: "all 0.2s",
                                }}
                                onMouseOver={(e) => {
                                  e.target.style.backgroundColor = "#1976d2";
                                  e.target.style.color = "#fff";
                                }}
                                onMouseOut={(e) => {
                                  e.target.style.backgroundColor = "#e3f2fd";
                                  e.target.style.color = "#1976d2";
                                }}
                              >
                                Edit
                              </button>
                              <button
                                onClick={() => handleRemoveLocation(index)}
                                style={{
                                  padding: "6px 12px",
                                  fontSize: "12px",
                                  color: "#d32f2f",
                                  backgroundColor: "#ffebee",
                                  border: "1px solid #d32f2f",
                                  borderRadius: "4px",
                                  cursor: "pointer",
                                  transition: "all 0.2s",
                                }}
                                onMouseOver={(e) => {
                                  e.target.style.backgroundColor = "#d32f2f";
                                  e.target.style.color = "#fff";
                                }}
                                onMouseOut={(e) => {
                                  e.target.style.backgroundColor = "#ffebee";
                                  e.target.style.color = "#d32f2f";
                                }}
                              >
                                Delete
                              </button>
                            </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div style={{
                    textAlign: "center",
                    color: "#777",
                    fontSize: "16px",
                    padding: "40px 20px",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                    height: "100%"
                  }}>
                    <div style={{ fontSize: "48px", marginBottom: "10px" }}>📍</div>
                    <p>No locations defined yet.</p>
                    <p style={{ fontSize: "14px", color: "#999" }}>Add your first location using the form on the right.</p>
                  </div>
                )}
              </div>
            </div>

            {/* Right side - Add/Edit Location Form */}
            <div className="locations-form-container">
              <h3
                style={{
                  fontSize: "18px",
                  fontWeight: "500",
                  color: "#555",
                  marginBottom: "15px",
                }}
              >
                {editingLocation !== null ? 'Edit Location' : 'Add New Location'}
              </h3>
              <div className="locations-form">
                <div style={{ display: "grid", gridTemplateColumns: "1fr", gap: "15px", marginBottom: "15px", flex: "1" }}>
                  <input
                    type="text"
                    value={editingLocation !== null ? editLocation.commonName : newLocation.commonName}
                    onChange={(e) => {
                      if (editingLocation !== null) {
                        setEditLocation({ ...editLocation, commonName: e.target.value });
                      } else {
                        setNewLocation({ ...newLocation, commonName: e.target.value });
                      }
                    }}
                    placeholder="Common Name (e.g., Main Hospital)"
                    style={{
                      padding: "12px",
                      fontSize: "16px",
                      border: "1px solid #ccc",
                      borderRadius: "6px",
                      outline: "none",
                      transition: "border-color 0.2s",
                    }}
                    onFocus={(e) => (e.target.style.borderColor = "#1976d2")}
                    onBlur={(e) => (e.target.style.borderColor = "#ccc")}
                  />
                  <input
                    type="text"
                    value={editingLocation !== null ? editLocation.address : newLocation.address}
                    onChange={(e) => {
                      if (editingLocation !== null) {
                        setEditLocation({ ...editLocation, address: e.target.value });
                      } else {
                        setNewLocation({ ...newLocation, address: e.target.value });
                      }
                    }}
                    placeholder="Street Address"
                    style={{
                      padding: "12px",
                      fontSize: "16px",
                      border: "1px solid #ccc",
                      borderRadius: "6px",
                      outline: "none",
                      transition: "border-color 0.2s",
                    }}
                    onFocus={(e) => (e.target.style.borderColor = "#1976d2")}
                    onBlur={(e) => (e.target.style.borderColor = "#ccc")}
                  />
                </div>
                <div style={{ display: "grid", gridTemplateColumns: "2fr 1fr 1fr", gap: "10px", marginBottom: "20px" }}>
                  <input
                    type="text"
                    value={editingLocation !== null ? editLocation.city : newLocation.city}
                    onChange={(e) => {
                      if (editingLocation !== null) {
                        setEditLocation({ ...editLocation, city: e.target.value });
                      } else {
                        setNewLocation({ ...newLocation, city: e.target.value });
                      }
                    }}
                    placeholder="City"
                    style={{
                      padding: "12px",
                      fontSize: "16px",
                      border: "1px solid #ccc",
                      borderRadius: "6px",
                      outline: "none",
                      transition: "border-color 0.2s",
                    }}
                    onFocus={(e) => (e.target.style.borderColor = "#1976d2")}
                    onBlur={(e) => (e.target.style.borderColor = "#ccc")}
                  />
                  <input
                    type="text"
                    value={editingLocation !== null ? editLocation.state : newLocation.state}
                    onChange={(e) => {
                      if (editingLocation !== null) {
                        setEditLocation({ ...editLocation, state: e.target.value });
                      } else {
                        setNewLocation({ ...newLocation, state: e.target.value });
                      }
                    }}
                    placeholder="State"
                    style={{
                      padding: "12px",
                      fontSize: "16px",
                      border: "1px solid #ccc",
                      borderRadius: "6px",
                      outline: "none",
                      transition: "border-color 0.2s",
                    }}
                    onFocus={(e) => (e.target.style.borderColor = "#1976d2")}
                    onBlur={(e) => (e.target.style.borderColor = "#ccc")}
                  />
                  <input
                    type="text"
                    value={editingLocation !== null ? editLocation.zip : newLocation.zip}
                    onChange={(e) => {
                      if (editingLocation !== null) {
                        setEditLocation({ ...editLocation, zip: e.target.value });
                      } else {
                        setNewLocation({ ...newLocation, zip: e.target.value });
                      }
                    }}
                    placeholder="ZIP Code"
                    style={{
                      padding: "12px",
                      fontSize: "16px",
                      border: "1px solid #ccc",
                      borderRadius: "6px",
                      outline: "none",
                      transition: "border-color 0.2s",
                    }}
                    onFocus={(e) => (e.target.style.borderColor = "#1976d2")}
                    onBlur={(e) => (e.target.style.borderColor = "#ccc")}
                  />
                </div>
                <div style={{ marginTop: "auto", display: "flex", gap: "10px" }}>
                  {editingLocation !== null ? (
                    <>
                      <button
                        onClick={handleSaveEdit}
                        style={{
                          flex: "1",
                          padding: "12px 20px",
                          fontSize: "16px",
                          fontWeight: "600",
                          color: "#fff",
                          backgroundColor: "#4caf50",
                          border: "none",
                          borderRadius: "6px",
                          cursor: "pointer",
                          transition: "background-color 0.2s",
                        }}
                        onMouseOver={(e) => (e.target.style.backgroundColor = "#45a049")}
                        onMouseOut={(e) => (e.target.style.backgroundColor = "#4caf50")}
                      >
                        Save Changes
                      </button>
                      <button
                        onClick={handleCancelEdit}
                        style={{
                          flex: "1",
                          padding: "12px 20px",
                          fontSize: "16px",
                          fontWeight: "600",
                          color: "#666",
                          backgroundColor: "#f5f5f5",
                          border: "1px solid #ddd",
                          borderRadius: "6px",
                          cursor: "pointer",
                          transition: "background-color 0.2s",
                        }}
                        onMouseOver={(e) => (e.target.style.backgroundColor = "#e0e0e0")}
                        onMouseOut={(e) => (e.target.style.backgroundColor = "#f5f5f5")}
                      >
                        Cancel
                      </button>
                    </>
                  ) : (
                    <button
                      onClick={handleAddLocation}
                      style={{
                        width: "100%",
                        padding: "12px 20px",
                        fontSize: "16px",
                        fontWeight: "600",
                        color: "#fff",
                        backgroundColor: "#1976d2",
                        border: "none",
                        borderRadius: "6px",
                        cursor: "pointer",
                        transition: "background-color 0.2s",
                      }}
                      onMouseOver={(e) => (e.target.style.backgroundColor = "#1565c0")}
                      onMouseOut={(e) => (e.target.style.backgroundColor = "#1976d2")}
                    >
                      Add Location
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
          </div>
          )}
        </section>

        {/* Manage Resources */}
        <section className="settings-section">
          <h2
            className="settings-section-header"
            onClick={() => toggleSection('manageResources')}
          >
            <span>Manage Resources</span>
            <span className={`expand-arrow ${sectionStates.manageResources ? 'expanded' : ''}`}>
              ▶
            </span>
          </h2>
          {sectionStates.manageResources && (
          <div className="settings-section-content">

          {/* Side-by-side layout */}
          <div className="resources-container" style={{
            display: "grid",
            gridTemplateColumns: "1fr 1fr",
            gap: "30px",
            alignItems: "start",
          }}>
            {/* Left side - Current Resources List */}
            <div style={{
              backgroundColor: "#f8f9fa",
              padding: "20px",
              borderRadius: "8px",
              border: "1px solid #e0e0e0",
            }}>
              <h3 style={{
                color: "#333",
                marginBottom: "15px",
                fontSize: "16px",
                fontWeight: "600"
              }}>
                Current Resources ({resources.length})
              </h3>

              {loadingResources ? (
                <div style={{ textAlign: "center", padding: "20px" }}>
                  <div style={{
                    display: "inline-block",
                    width: "20px",
                    height: "20px",
                    border: "2px solid #f3f3f3",
                    borderTop: "2px solid #007bff",
                    borderRadius: "50%",
                    animation: "spin 1s linear infinite"
                  }}></div>
                  <p style={{ marginTop: "10px", color: "#666" }}>Loading resources...</p>
                </div>
              ) : (
                <div style={{
                  maxHeight: "430px",
                  overflowY: "auto",
                  border: "1px solid #ddd",
                  borderRadius: "6px",
                  backgroundColor: "#fff"
                }}>
                  {resources.length === 0 ? (
                    <div style={{
                      padding: "20px",
                      textAlign: "center",
                      color: "#666"
                    }}>
                      No resources found. Add your first resource using the form on the right.
                    </div>
                  ) : (
                    resources.map((resource) => (
                      <div
                        key={resource.id}
                        style={{
                          padding: "15px",
                          borderBottom: "1px solid #eee",
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center"
                        }}
                      >
                        {editingResource === resource.id ? (
                          <div style={{ flex: 1, marginRight: "10px" }}>
                            <input
                              type="text"
                              defaultValue={resource.name}
                              onBlur={(e) => {
                                if (e.target.value.trim()) {
                                  handleUpdateResource(resource.id, {
                                    ...resource,
                                    name: e.target.value.trim()
                                  });
                                } else {
                                  setEditingResource(null);
                                }
                              }}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  e.target.blur();
                                } else if (e.key === 'Escape') {
                                  setEditingResource(null);
                                }
                              }}
                              autoFocus
                              style={{
                                width: "100%",
                                padding: "8px",
                                border: "1px solid #ddd",
                                borderRadius: "4px",
                                fontSize: "14px"
                              }}
                            />
                          </div>
                        ) : (
                          <div style={{ flex: 1 }}>
                            <div style={{
                              fontWeight: "500",
                              color: "#333",
                              marginBottom: "4px"
                            }}>
                              {resource.name}
                            </div>
                            {resource.category && (
                              <div style={{
                                fontSize: "12px",
                                color: "#666",
                                marginBottom: "2px"
                              }}>
                                Category: {resource.category}
                              </div>
                            )}
                            {resource.description && (
                              <div style={{
                                fontSize: "12px",
                                color: "#888"
                              }}>
                                {resource.description}
                              </div>
                            )}
                          </div>
                        )}

                        <div style={{ display: "flex", gap: "8px" }}>
                          <button
                            onClick={() => setEditingResource(
                              editingResource === resource.id ? null : resource.id
                            )}
                            style={{
                              padding: "6px 12px",
                              backgroundColor: "#007bff",
                              color: "#fff",
                              border: "none",
                              borderRadius: "4px",
                              fontSize: "12px",
                              cursor: "pointer"
                            }}
                          >
                            {editingResource === resource.id ? "Cancel" : "Edit"}
                          </button>
                          <button
                            onClick={() => handleDeleteResource(resource.id)}
                            style={{
                              padding: "6px 12px",
                              backgroundColor: "#dc3545",
                              color: "#fff",
                              border: "none",
                              borderRadius: "4px",
                              fontSize: "12px",
                              cursor: "pointer"
                            }}
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              )}
            </div>

            {/* Right side - Add New Resource Form */}
            <div style={{
              backgroundColor: "#fff",
              padding: "20px",
              borderRadius: "8px",
              border: "1px solid #e0e0e0"
            }}>
              <h3 style={{
                color: "#333",
                marginBottom: "15px",
                fontSize: "16px",
                fontWeight: "600"
              }}>
                Add New Resource
              </h3>

              <div style={{ marginBottom: "15px" }}>
                <label style={{
                  display: "block",
                  marginBottom: "5px",
                  fontWeight: "500",
                  color: "#333"
                }}>
                  Resource Name *
                </label>
                <input
                  type="text"
                  value={newResource.name}
                  onChange={(e) => setNewResource(prev => ({
                    ...prev,
                    name: e.target.value
                  }))}
                  placeholder="e.g., Ambulance, Fire Engine"
                  style={{
                    width: "100%",
                    padding: "10px",
                    border: "1px solid #ddd",
                    borderRadius: "6px",
                    fontSize: "14px"
                  }}
                />
              </div>

              <div style={{ marginBottom: "15px" }}>
                <label style={{
                  display: "block",
                  marginBottom: "5px",
                  fontWeight: "500",
                  color: "#333"
                }}>
                  Category (Optional)
                </label>
                <select
                  value={newResource.category}
                  onChange={(e) => setNewResource(prev => ({
                    ...prev,
                    category: e.target.value
                  }))}
                  style={{
                    width: "100%",
                    padding: "10px",
                    border: "1px solid #ddd",
                    borderRadius: "6px",
                    fontSize: "14px"
                  }}
                >
                  <option value="">No Category</option>
                  <option value="Medical">Medical</option>
                  <option value="Fire">Fire</option>
                  <option value="Law Enforcement">Law Enforcement</option>
                  <option value="Emergency Response">Emergency Response</option>
                  <option value="Specialized">Specialized</option>
                  <option value="Transportation">Transportation</option>
                  <option value="Communication">Communication</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <div style={{ marginBottom: "20px" }}>
                <label style={{
                  display: "block",
                  marginBottom: "5px",
                  fontWeight: "500",
                  color: "#333"
                }}>
                  Description
                </label>
                <textarea
                  value={newResource.description}
                  onChange={(e) => setNewResource(prev => ({
                    ...prev,
                    description: e.target.value
                  }))}
                  placeholder="Brief description of the resource"
                  rows={3}
                  style={{
                    width: "100%",
                    padding: "10px",
                    border: "1px solid #ddd",
                    borderRadius: "6px",
                    fontSize: "14px",
                    resize: "vertical"
                  }}
                />
              </div>

              <button
                onClick={handleAddResource}
                disabled={!newResource.name.trim()}
                style={{
                  width: "100%",
                  padding: "12px",
                  backgroundColor: !newResource.name.trim()
                    ? "#ccc" : "#28a745",
                  color: "#fff",
                  border: "none",
                  borderRadius: "6px",
                  fontSize: "14px",
                  fontWeight: "500",
                  cursor: !newResource.name.trim()
                    ? "not-allowed" : "pointer",
                  transition: "background-color 0.2s"
                }}
              >
                Add Resource
              </button>

              <div style={{
                marginTop: "15px",
                padding: "10px",
                backgroundColor: "#e7f3ff",
                border: "1px solid #b3d9ff",
                borderRadius: "6px",
                fontSize: "12px",
                color: "#0066cc"
              }}>
                <strong>Note:</strong> Resources added here will be available for ALL types of
                event launches in the "Report-to Locations" section. You can assign
                specific quantities and roles for each resource at each location.
                Categories are optional and for organizational purposes only.
              </div>
            </div>
          </div>
          </div>
          )}
        </section>

        {/* Manage Company Code */}
        <section className="settings-section">
          <h2
            className="settings-section-header"
            onClick={() => toggleSection('manageCompanyCodes')}
          >
            <span>Manage Company Code</span>
            <span className={`expand-arrow ${sectionStates.manageCompanyCodes ? 'expanded' : ''}`}>
              ▶
            </span>
          </h2>
          {sectionStates.manageCompanyCodes && (
          <div className="settings-section-content">



          <div
            style={{
              display: "flex",
              flexDirection: "column",
              gap: "15px",
              marginTop: "20px",
            }}
          >
            {/* Row with Name, Code, and Generate Button */}
            <div
              style={{
                display: "flex",
                gap: "10px",
                alignItems: "flex-start",
                marginTop: "10px",
              }}
            >
              {/* Name Field */}
              <div style={{ flex: "1" }}>
                <label
                  htmlFor="companyName"
                  style={{
                    display: "block",
                    fontSize: "16px",
                    fontWeight: "500",
                    color: "#555",
                    marginBottom: "8px",
                  }}
                >
                  Company Name <span style={{ color: "red" }}>* </span>
                </label>
                <input
                  id="companyName"
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Enter company name"
                  style={{
                    width: "100%",
                    padding: "10px",
                    fontSize: "16px",
                    border: "1px solid #ccc",
                    borderRadius: "6px",
                  }}
                />
              </div>

              {/* Code Input */}
              <div style={{ flex: "1" }}>
                <label
                  htmlFor="companyCode"
                  style={{
                    display: "block",
                    fontSize: "16px",
                    fontWeight: "500",
                    color: "#555",
                    marginBottom: "8px",
                  }}
                >
                  Company Code <span style={{ color: "red" }}>* </span>
                </label>
                <input
                  id="companyCode"
                  type="text"
                  value={newCode}
                  onChange={(e) => setNewCode(e.target.value)}
                  placeholder="Enter or generate a code"
                  style={{
                    width: "100%",
                    padding: "10px",
                    fontSize: "16px",
                    border: "1px solid #ccc",
                    borderRadius: "6px",
                  }}
                />
              </div>

              {/* Generate Button */}
              <button
                onClick={generateRandomCode}
                style={{
                  marginTop: "30px", // Align button with inputs
                  padding: "10px 20px",
                  fontSize: "16px",
                  color: "#fff",
                  backgroundColor: "#1976d2",
                  border: "none",
                  borderRadius: "6px",
                  cursor: "pointer",
                  whiteSpace: "nowrap",
                }}
              >
                <GiCycle size={20} />
              </button>
            </div>

            {/* Description Field */}
            <div>
              <label
                htmlFor="companyDescription"
                style={{
                  display: "block",
                  fontSize: "16px",
                  fontWeight: "500",
                  color: "#555",
                  marginBottom: "8px",
                }}
              >
                Description
                <span style={{ fontSize: "12px", color: "#777" }}>
                  (optional)
                </span>
              </label>
              <textarea
                id="companyDescription"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows="4"
                placeholder="Enter company description"
                style={{
                  width: "100%",
                  padding: "10px",
                  fontSize: "16px",
                  border: "1px solid #ccc",
                  borderRadius: "6px",
                  resize: "none",
                }}
              ></textarea>
            </div>

            {/* Save Button */}
            <div style={{ marginTop: "10px" }}>
              <button
                onClick={saveCode}
                style={{
                  padding: "10px 20px",
                  fontSize: "16px",
                  color: "#fff",
                  backgroundColor: "#4caf50",
                  border: "none",
                  borderRadius: "6px",
                  cursor: "pointer",
                }}
              >
                Save Code
              </button>
            </div>
          </div>
          
          {/* Display Saved Company Codes */}
          <div
            style={{
              marginTop: "20px",
              backgroundColor: "#fff",
              padding: "15px",
              borderRadius: "8px",
              border: "1px solid #e0e0e0",
            }}
          >
            <h3
              style={{
                fontSize: "18px",
                fontWeight: "500",
                color: "#555",
                marginBottom: "15px",
                borderBottom: "1px solid #eee",
                paddingBottom: "10px",
              }}
            >
              Saved Company Codes ({savedCodes.length})
            </h3>
            
            {savedCodes && savedCodes.length > 0 ? (
              <div style={{ maxHeight: "300px", overflowY: "auto" }}>
                {savedCodes.map((code, index) => (
                  <div
                    key={index}
                    style={{
                      padding: "8px 12px",
                      borderBottom: index < savedCodes.length - 1 ? "1px solid #eee" : "none",
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      backgroundColor: "#fff",
                      transition: "background-color 0.2s",
                      minHeight: "auto"
                    }}
                    onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = "#f8f9fa")}
                    onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = "#fff")}
                  >
                    <div style={{ flex: 1 }}>
                      <div style={{ fontSize: "14px", fontWeight: "500", color: "#333", marginBottom: "2px" }}>
                        {code.name}
                      </div>
                      <div style={{ fontSize: "13px", color: "#666" }}>
                        Code: <span style={{ fontWeight: "500" }}>{code.code}</span>
                      </div>
                      {code.description && (
                        <div style={{ fontSize: "12px", color: "#777", marginTop: "2px" }}>
                          {code.description}
                        </div>
                      )}
                    </div>
                    <div style={{ display: "flex", gap: "6px", alignItems: "center" }}>
                      <button
                        onClick={() => handleCopyCode(code.code)}
                        style={{
                          padding: "4px 8px",
                          fontSize: "11px",
                          color: "#1976d2",
                          backgroundColor: "#e3f2fd",
                          border: "1px solid #1976d2",
                          borderRadius: "4px",
                          cursor: "pointer",
                          transition: "all 0.2s",
                        }}
                        onMouseOver={(e) => {
                          e.target.style.backgroundColor = "#1976d2";
                          e.target.style.color = "#fff";
                        }}
                        onMouseOut={(e) => {
                          e.target.style.backgroundColor = "#e3f2fd";
                          e.target.style.color = "#1976d2";
                        }}
                      >
                        Copy
                      </button>
                      <button
                        onClick={() => handleDeleteCode(index, code.id)}
                        style={{
                          padding: "4px 8px",
                          fontSize: "11px",
                          color: "#d32f2f",
                          backgroundColor: "#ffebee",
                          border: "1px solid #d32f2f",
                          borderRadius: "4px",
                          cursor: "pointer",
                          transition: "all 0.2s",
                        }}
                        onMouseOver={(e) => {
                          e.target.style.backgroundColor = "#d32f2f";
                          e.target.style.color = "#fff";
                        }}
                        onMouseOut={(e) => {
                          e.target.style.backgroundColor = "#ffebee";
                          e.target.style.color = "#d32f2f";
                        }}
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div
                style={{
                  textAlign: "center",
                  color: "#777",
                  fontSize: "16px",
                  padding: "30px 20px",
                }}
              >
                <p>No company codes saved yet.</p>
                <p style={{ fontSize: "14px", color: "#999" }}>
                  Add your first company code using the form above.
                </p>
              </div>
            )}
          </div>
          </div>
          )}
        </section>

        {/* AI Agent Preconditions Section - Super Admin Only */}
        {(userId === 2 || userId === '2' || userId === 'ttornstrom' || role === 'commander') && (
          <section className="settings-section">
            <h2
              className="settings-section-header"
              onClick={() => setIsAiSettingsExpanded(!isAiSettingsExpanded)}
            >
              <span>AI Agent Preconditions</span>
              <span style={{
                fontSize: "16px",
                transition: "transform 0.2s ease",
                transform: isAiSettingsExpanded ? "rotate(90deg)" : "rotate(0deg)"
              }}>
                ▶
              </span>
            </h2>

            {isAiSettingsExpanded && (
              <div className="settings-section-content">
                
                <div style={{
                  backgroundColor: "#fff3cd",
                  border: "1px solid #ffeaa7",
                  borderRadius: "8px",
                  padding: "15px",
                  marginBottom: "20px"
                }}>
                  <p style={{ margin: 0, fontSize: "14px", color: "#856404" }}>
                    <strong>Super Admin Access:</strong> Configure AI prompts and behavior for document summarization, question-answering, and action generation.
                  </p>
                </div>

                {/* Document Summary Prompt */}
                <div style={{ marginBottom: "25px" }}>
                  <label style={{
                    display: "block",
                    fontSize: "16px",
                    fontWeight: "500",
                    marginBottom: "8px"
                  }}>
                    Document Summary Prompt
                  </label>
                  <textarea
                    value={aiSettings.ai_prompts?.document_summary || ""}
                    onChange={(e) => updateAiPrompt('document_summary', e.target.value)}
                    placeholder="Enter the AI prompt for document summarization..."
                    rows={4}
                    style={{
                      width: "100%",
                      padding: "12px",
                      fontSize: "14px",
                      border: "1px solid #ccc",
                      borderRadius: "6px",
                      outline: "none",
                      resize: "vertical",
                      fontFamily: "monospace"
                    }}
                  />
                </div>

                {/* Document Q&A Prompt */}
                <div style={{ marginBottom: "25px" }}>
                  <label style={{
                    display: "block",
                    fontSize: "16px",
                    fontWeight: "500",
                    marginBottom: "8px"
                  }}>
                    Document Q&A Prompt
                  </label>
                  <textarea
                    value={aiSettings.ai_preconditions?.document_qa_prompt || ""}
                    onChange={(e) => updateAiPrecondition('document_qa_prompt', e.target.value)}
                    placeholder="Enter the AI prompt for document question-answering..."
                    rows={4}
                    style={{
                      width: "100%",
                      padding: "12px",
                      fontSize: "14px",
                      border: "1px solid #ccc",
                      borderRadius: "6px",
                      outline: "none",
                      resize: "vertical",
                      fontFamily: "monospace"
                    }}
                  />
                </div>
                
                {/* Action Generation Prompt */}
                <div style={{ marginBottom: "25px" }}>
                  <label style={{
                    display: "block",
                    fontSize: "16px",
                    fontWeight: "500",
                    marginBottom: "8px"
                  }}>
                    Action Generation Prompt
                  </label>
                  <textarea
                    value={aiSettings.ai_preconditions?.action_generation_prompt || ""}
                    onChange={(e) => updateAiPrecondition('action_generation_prompt', e.target.value)}
                    placeholder="Enter the AI prompt for generating action checklists..."
                    rows={4}
                    style={{
                      width: "100%",
                      padding: "12px",
                      fontSize: "14px",
                      border: "1px solid #ccc",
                      borderRadius: "6px",
                      outline: "none",
                      resize: "vertical",
                      fontFamily: "monospace"
                    }}
                  />
                </div>

                {/* Save Button */}
                <div style={{ textAlign: "right" }}>
                  <button
                    onClick={saveAiSettings}
                    disabled={loadingAiSettings}
                    style={{
                      padding: "12px 24px",
                      fontSize: "16px",
                      fontWeight: "600",
                      color: "#fff",
                      backgroundColor: loadingAiSettings ? "#ccc" : "#1976d2",
                      border: "none",
                      borderRadius: "6px",
                      cursor: loadingAiSettings ? "not-allowed" : "pointer",
                      transition: "background-color 0.2s"
                    }}
                  >
                    {loadingAiSettings ? "Saving..." : "Save AI Settings"}
                  </button>
                </div>
              </div>
            )}
          </section>
        )}

      </div>
    </div>
    </>
  );
}
export default Settings;
