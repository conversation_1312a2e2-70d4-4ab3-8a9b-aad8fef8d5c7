import React, { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { Autocomplete, GoogleMap, Marker } from "@react-google-maps/api";
import "./styles.css";
import config from "./config";
import { IoMdArrowRoundBack } from "react-icons/io";
import { toast } from "react-toastify";
import Swal from "sweetalert2";

function Templates({ token, mapsLoaded }) {
  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [templateName, setTemplateName] = useState("");
  const [templateDescription, setTemplateDescription] = useState(""); // New Description field
  const [title, setTitle] = useState("");
  const [info, setInfo] = useState("");
  const [scale, setScale] = useState("");
  const [urgency, setUrgency] = useState("");
  const [checked, setChecked] = useState(false);
  const [location, setLocation] = useState({
    commonName: "",
    address: "",
    city: "",
    state: "",
    zip: "",
  });
  const [reportToLocations, setReportToLocations] = useState([
    {
      commonName: "",
      address: "",
      city: "",
      state: "",
      zip: "",
      primary: false,
      staffNeeded: 2,
      resources: [{ name: "", responderCount: 2 }],
    },
  ]);
  const [status, setStatus] = useState("open");
  const [assignedTo, setAssignedTo] = useState([]);
  const [eventType, setEventType] = useState("response");
  const [locationUpdateInterval, setLocationUpdateInterval] = useState(60);
  const [notificationChannels, setNotificationChannels] = useState(["web_app"]);
  const [eventDocuments, setEventDocuments] = useState([]);
  const [success, setSuccess] = useState(null);
  const [error, setError] = useState(null);
  const [previousLocations, setPreviousLocations] = useState([]);
  const [loadingTemplates, setLoadingTemplates] = useState(true);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [templateToDelete, setTemplateToDelete] = useState(null);
  const [eventAutocomplete, setEventAutocomplete] = useState(null);
  const [reportAutocompletes, setReportAutocompletes] = useState([]);
  const [showMapPicker, setShowMapPicker] = useState(null); // Now tracks which location is being edited (null for Event Location, index for Report to Locations)
  const [mapCenter, setMapCenter] = useState({ lat: 43.8349, lng: -91.3188 });
  const [selectedPosition, setSelectedPosition] = useState(null);
  const navigate = useNavigate();
  const { baseUrl } = config;

  const scaleOptions = ["Small", "Medium", "Large"];
  const urgencyOptions = ["Low", "Medium", "High", "Immediate"];
  const [isCreate, setIsCreate] = useState(false);
  const [isEdit, setIsEdit] = useState(false);

  useEffect(() => {

    fetch(`${baseUrl}/templates`, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    })
      .then((res) => {
        if (!res.ok) throw new Error(`HTTP error! Status: ${res.status}`);
        return res.json();
      })
      .then((data) => {
      
        setTemplates(data);
        const locs = data
          .flatMap((t) => t.config?.included_report_to_locations || [])
          .map((loc) => ({
            address: loc.address || loc.location || "",
            city: loc.city || "",
            state: loc.state || "",
            zip: loc.zip || "",
          }));
        setPreviousLocations([
          ...new Set(
            locs.map((l) =>
              `${l.address}, ${l.city}, ${l.state} ${l.zip}`.trim()
            )
          ),
        ]);
        setLoadingTemplates(false);
      })
      .catch((err) => {
        console.error("Error fetching templates:", err.message);
        setError(err.message);
        setLoadingTemplates(false);
      });
  }, [token]);

  const handleSaveTemplate = () => {
    if (!templateName) {
      setError("Please enter a template name");
      return;
    }
    const templateData = {
      name: templateName,
      description: templateDescription, // Include description in template data
      config: {
        title,
        info,
        scale,
        urgency,
        location,
        event_type: eventType,
        location_update_interval: locationUpdateInterval,
        notification_channels: notificationChannels,
        event_documents: eventDocuments,
        included_report_to_locations: reportToLocations,
        assigned_to: assignedTo,
        status,
      },
    };
    const method = selectedTemplate ? "PUT" : "POST";
    const url = selectedTemplate
      ? `${baseUrl}/templates/${selectedTemplate.id}`
      : `${baseUrl}/templates`;
    fetch(url, {
      method,
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(templateData),
    })
      .then((res) => {
        if (!res.ok) throw new Error(`HTTP error! Status: ${res.status}`);
        return res.json();
      })
      .then((data) => {
        setTemplates((prev) => {
          if (selectedTemplate) {
            // Update the existing template in the array
            return prev.map((t) => (t.id === data.id ? data : t));
          } else {
            return [data, ...prev];
          }
        });
      
        
        setSuccess(`Template ${selectedTemplate ? "updated" : "created"}!`);
        resetForm();
        toast.success(
          `Template ${selectedTemplate ? "updated" : "created"} successfully!`,
          {
            position: "top-right",
            autoClose: 3000,
          }
        );
        setIsCreate(false);
        setIsEdit(false);
        setSelectedTemplate(null);
      })
      .catch((err) => {
        console.error("Error saving template:", err);
        setError(`Failed to save template: ${err.message}`);
      });
  };

  const handleEditTemplate = (template) => {
    setIsCreate(false);
    setIsEdit(true);
    setSelectedTemplate(template);
    setTemplateName(template.name || "");
    setTemplateDescription(template.description || ""); // Load description
    const config = template.config;

    setTitle(config.title || "");
    setInfo(config.info || "");
    setScale(config.scale || "");
    setUrgency(config.urgency || "");
    setLocation(
      config.location || {
        commonName: "",
        address: "",
        city: "",
        state: "",
        zip: "",
      }
    );
    setReportToLocations(
      config.included_report_to_locations &&
        Array.isArray(config.included_report_to_locations)
        ? config.included_report_to_locations.map((loc) => ({
            commonName: loc.commonName || "",
            address: loc.address || loc.location || "",
            city: loc.city || "",
            state: loc.state || "",
            zip: loc.zip || "",
            primary: loc.primary || false,
            staffNeeded: loc.staffNeeded || 2,
            resources: loc.resources || [{ name: "", responderCount: 2 }],
          }))
        : [
            {
              commonName: "",
              address: "",
              city: "",
              state: "",
              zip: "",
              primary: false,
              staffNeeded: 2,
              resources: [{ name: "", responderCount: 2 }],
            },
          ]
    );
    setAssignedTo(config.assigned_to || []);
    setStatus(config.status || "open");
    setEventType(config.event_type || "response");
    setLocationUpdateInterval(config.location_update_interval || 60);
    setNotificationChannels(config.notification_channels || ["web_app"]);
    setEventDocuments(config.event_documents || []);
  };

  const handleDeleteTemplate = async (id) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "Cancel",
      confirmButtonColor: "#d32f2f",
      cancelButtonColor: "#777",
    });

    // setTemplateToDelete(id);
    if (result.isConfirmed) {
      fetch(`${baseUrl}/templates/${id}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      })
        .then((res) => {
          if (!res.ok) throw new Error(`HTTP error! Status: ${res.status}`);
          return res.json();
        })
        .then(() => {
          setTemplates((prev) => prev.filter((t) => t.id !== id));
          setSuccess("Template deleted!");
          toast.success("Template deleted successfully!", {
            position: "top-right",
            autoClose: 3000,
          });
          if (selectedTemplate && selectedTemplate.id === id) resetForm();
          setShowDeleteModal(false);
          setTemplateToDelete(null);
          setIsCreate(false);
          setIsEdit(false);
        })
        .catch((err) => {
          console.error("Error deleting template:", err);
          setError(`Failed to delete template: ${err.message}`);
          toast.error(`Failed to delete template: ${err.message}`, {
            position: "top-right",
            autoClose: 3000,
          });
          setShowDeleteModal(false);
          setTemplateToDelete(null);
        });
      // You can uncomment this line if you need to show a modal
      // setShowDeleteModal(true);
      // confirmDelete();
    }
  };

  const confirmDelete = () => {
    fetch(`${baseUrl}/templates/${templateToDelete}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    })
      .then((res) => {
        if (!res.ok) throw new Error(`HTTP error! Status: ${res.status}`);
        return res.json();
      })
      .then(() => {
        console.log(`Template ${templateToDelete} deleted`);
        setTemplates((prev) => prev.filter((t) => t.id !== templateToDelete));
        setSuccess("Template deleted!");
        if (selectedTemplate && selectedTemplate.id === templateToDelete)
          resetForm();
        setShowDeleteModal(false);
        setTemplateToDelete(null);
      })
      .catch((err) => {
        console.error("Error deleting template:", err);
        setError(`Failed to delete template: ${err.message}`);
        setShowDeleteModal(false);
        setTemplateToDelete(null);
      });
  };

  const cancelDelete = () => {
    setShowDeleteModal(false);
    setTemplateToDelete(null);
  };

  const addLocation = () => {

    setReportToLocations((prev) => [
      ...prev,
      {
        commonName: "",
        address: "",
        city: "",
        state: "",
        zip: "",
        primary: false,
        staffNeeded: 2,
        resources: [{ name: "", responderCount: 2 }],
      },
    ]);
    setReportAutocompletes((prev) => [...prev, null]);
  };

  const deleteLocation = (index) => {
    setReportToLocations((prev) => prev.filter((_, i) => i !== index));
    setReportAutocompletes((prev) => prev.filter((_, i) => i !== index));
  };

  const clearLocation = (index) => {
    setReportToLocations((prev) => {
      const newLocs = [...prev];
      newLocs[index] = {
        commonName: "",
        address: "",
        city: "",
        state: "",
        zip: "",
        primary: false,
        staffNeeded: 2,
        resources: [{ name: "", responderCount: 2 }],
      };
      return newLocs;
    });
  };

  const addResource = (locIndex) => {
    setReportToLocations((prev) => {
      const newLocs = [...prev];
      newLocs[locIndex].resources.push({ name: "", responderCount: 2 });
      return newLocs;
    });
  };

  const removeResource = (locIndex, resIndex) => {
    setReportToLocations((prev) => {
      const newLocs = [...prev];
      newLocs[locIndex].resources.splice(resIndex, 1);
      return newLocs;
    });
  };

  const handleResourceChange = (locIndex, resIndex, field, value) => {
    setReportToLocations((prev) => {
      const newLocs = [...prev];
      newLocs[locIndex].resources[resIndex][field] =
        field === "responderCount" ? parseInt(value) || 2 : value;
      return newLocs;
    });
  };

  const onLoadEventAutocomplete = useCallback((autoC) => {

    setEventAutocomplete(autoC);
  }, []);

  const onLoadReportAutocomplete = useCallback((autoC, index) => {
  
    setReportAutocompletes((prev) => {
      const newAutocompletes = [...prev];
      newAutocompletes[index] = autoC;
      return newAutocompletes;
    });
  }, []);

  const onPlaceChanged = useCallback(
    (index = -1) => {
      const autoC =
        index === -1 ? eventAutocomplete : reportAutocompletes[index];
      if (!autoC) {
        console.error("Autocomplete not initialized for index:", index);
        return;
      }
      const place = autoC.getPlace();
      if (!place || !place.geometry) {
        console.warn("No valid place selected or geometry unavailable:", place);
        return;
      }
      const addressComponents = place.address_components || [];
      const getComponent = (type) =>
        addressComponents.find((c) => c.types.includes(type))?.short_name || "";
      const streetNumber = getComponent("street_number");
      const route = getComponent("route");
      const address =
        streetNumber && route
          ? `${streetNumber} ${route}`
          : streetNumber || route || "";
      const city = getComponent("locality");
      const state = getComponent("administrative_area_level_1");
      const zip = getComponent("postal_code");
      if (index === -1) {
        setLocation((prev) => ({ ...prev, address, city, state, zip }));
      } else {
        setReportToLocations((prev) => {
          const newLocs = [...prev];
          newLocs[index] = {
            ...newLocs[index],
            address,
            city,
            state,
            zip,
          };
         
          return newLocs;
        });
      }
    },
    [eventAutocomplete, reportAutocompletes]
  );

  const handleMapClick = (event) => {
    const lat = event.latLng.lat();
    const lng = event.latLng.lng();
    setSelectedPosition({ lat, lng });
    const geocoder = new window.google.maps.Geocoder();
    geocoder.geocode({ location: { lat, lng } }, (results, status) => {
      if (status === "OK" && results[0]) {
        const place = results[0];
        const addressComponents = place.address_components || [];
        const getComponent = (type) =>
          addressComponents.find((c) => c.types.includes(type))?.short_name ||
          "";
        const streetNumber = getComponent("street_number");
        const route = getComponent("route");
        const address =
          streetNumber && route
            ? `${streetNumber} ${route}`
            : streetNumber || route || "";
        const city = getComponent("locality");
        const state = getComponent("administrative_area_level_1");
        const zip = getComponent("postal_code");
        if (showMapPicker === null) {
          setLocation((prev) => ({ ...prev, address, city, state, zip }));
        } else {
          setReportToLocations((prev) => {
            const newLocs = [...prev];
            newLocs[showMapPicker] = {
              ...newLocs[showMapPicker],
              address,
              city,
              state,
              zip,
            };
            return newLocs;
          });
        }
        setShowMapPicker(null);
      } else {
        console.error("Geocoding failed:", status);
        setError("Failed to get address from map selection");
        setTimeout(() => setError(null), 3000);
      }
    });
  };

  const handleLocationSelect = (index, value) => {
 
    const parts = value.split(/,\s*/).map((part) => part.trim());
    const address = parts[0] || "";
    const city = parts[1] || "";
    const stateZip = parts[2] || "";
    const [state, zip] = stateZip.split(/\s+/).map((part) => part.trim());
    setReportToLocations((prev) => {
      const newLocs = [...prev];
      newLocs[index] = {
        ...newLocs[index],
        address,
        city,
        state: state || "",
        zip: zip || "",
      };
      console.log("Updated reportToLocations:", newLocs[index]);
      return newLocs;
    });
  };

  const resetForm = () => {
    setTemplateName("");
    setTemplateDescription("");
    setTitle("");
    setInfo("");
    setScale("");
    setUrgency("");
    setLocation({ commonName: "", address: "", city: "", state: "", zip: "" });
    setReportToLocations([
      {
        commonName: "",
        address: "",
        city: "",
        state: "",
        zip: "",
        primary: false,
        staffNeeded: 2,
        resources: [{ name: "", responderCount: 2 }],
      },
    ]);
    setAssignedTo([]);
    setStatus("open");
    setEventType("response");
    setLocationUpdateInterval(60);
    setNotificationChannels(["web_app"]);
    setEventDocuments([]);
    setSelectedTemplate(null);
    setSuccess(null);
    setError(null);
    setReportAutocompletes([]);
  };

  // Document handling functions
  const handleDocumentUpload = (e) => {
    const files = Array.from(e.target.files);
    const newDocuments = files.map(file => ({
      name: file.name,
      size: file.size,
      type: file.type,
      file: file,
      id: Date.now() + Math.random(), // Simple unique ID
    }));
    
    setEventDocuments(prev => [...prev, ...newDocuments]);
    e.target.value = ''; // Reset file input
  };

  const removeDocument = (index) => {
    setEventDocuments(prev => prev.filter((_, i) => i !== index));
  };



  // Function to handle previous location checkbox change
  const handlePreviousLocationCheck = (e) => {
    const isChecked = e.target.checked;

 

    if (isChecked) {
      setChecked(true);
      setReportToLocations([
        {
          commonName: location.commonName || "",
          address: location.address || "",
          city: location.city || "",
          state: location.state || "",
          zip: location.zip || "",
          primary: false,
          staffNeeded: 2,
          resources: [{ name: "", responderCount: 2 }],
        },
      ]);

      toast.success("Previous locations added successfully!", {
        position: "top-right",
        autoClose: 3000,
      });
    } else {
     
      setReportToLocations([
        {
          commonName: "",
          address: "",
          city: "",
          state: "",
          zip: "",
          primary: false,
          staffNeeded: 2,
          resources: [{ name: "", responderCount: 2 }],
        },
      ]);
      setChecked(false);
    }
  };

  return (
    <div className="container">
      <div className="user-management-card">
        <h1>Manage Templates</h1>
        {loadingTemplates && !error ? (
          <div>Loading templates...</div>
        ) : (
          <div>
            {error && <p style={{ color: "var(--danger)" }}>Error: {error}</p>}
            {success && <p style={{ color: "var(--success)" }}>{success}</p>}

            {!isEdit && !isCreate && (
              <>
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: "1rem",
                  }}
                >
                  <h2>Templates List</h2>
                  <button
                    className="btn-primary"
                    onClick={() => {
                      setIsEdit(false);
                      setIsCreate(true);
                      setSelectedTemplate(null);
                      resetForm();
                    }}
                    style={{ padding: "0.5rem 1rem" }}
                  >
                    + Create Template
                  </button>
                </div>



<ul style={{ listStyle: "none", padding: 0 }}>
  <li
    style={{
      display: "flex",
      justifyContent: "space-between",
      fontWeight: "bold",
      marginBottom: "1rem",
    }}
  >
    <span>Template Name</span>
    <span style={{paddingRight :"50px"}}>Actions</span>
  </li>
  {templates.map((template) => (
    <li
      key={template.id}
      style={{
        marginBottom: "0.5rem",
        display: "flex",
        justifyContent: "space-between", // Align text to the left and buttons to the right
        alignItems: "center",
      }}
    >
      <span>{template.name}</span> {/* Template name */}
      <div>
        <button
          className="btn-primary"
          onClick={() => handleEditTemplate(template)}
          style={{ marginRight: "0.5rem" }}
        >
          Edit
        </button>
        <button
          className="btn-danger"
          onClick={() => handleDeleteTemplate(template.id)}
        >
          Delete
        </button>
      </div>
    </li>
  ))}
</ul>
              </>
            )}

            {(isEdit || isCreate) && (
              <>
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: "1rem",
                  }}
                >
                  <h2>
                    {selectedTemplate ? "Edit Template" : "Create New Template"}
                  </h2>
                  <button
                    className="btn-primary"
                    onClick={() => {
                      setIsEdit(false);
                      setIsCreate(false);
                      setSelectedTemplate(null);
                      resetForm();
                    }}
                    style={{ padding: "0.5rem 1rem" }}
                  >
                    <IoMdArrowRoundBack size={10} /> Back to List
                  </button>
                </div>

                <form
                  onSubmit={(e) => {
                    e.preventDefault();
                    handleSaveTemplate();
                  }}
                >
                  <div
                    style={{
                      backgroundColor: "#fff",
                      border: "1px solid #ddd",
                      borderRadius: "8px",
                      padding: "20px",
                      maxWidth: "100%",
                      margin: "20px auto",
                      boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                    }}
                  >
                    <h3
                      style={{
                        marginBottom: "1rem",
                        fontSize: "1.5rem",
                        fontWeight: "bold",
                        textAlign: "left",
                        color: "#333",
                      }}
                    >
                      Template Details
                    </h3>
                    <label>
                      Template Name:
                      <input
                        value={templateName || ""}
                        onChange={(e) => setTemplateName(e.target.value)}
                        placeholder="Template Name"
                        className="form-input"
                        required
                      />
                    </label>
                    <label>
                      Description:
                      <textarea
                        value={templateDescription || ""}
                        onChange={(e) => setTemplateDescription(e.target.value)}
                        placeholder="Describe what this template is used for"
                        className="form-input"
                        style={{ height: "100px", resize: "vertical" }}
                      />
                    </label>
                    <label>
                      Title:
                      <input
                        value={title || ""}
                        onChange={(e) => setTitle(e.target.value)}
                        placeholder="Title"
                        className="form-input"
                      />
                    </label>
                    <label>
                      Info:
                      <textarea
                        value={info || ""}
                        onChange={(e) => setInfo(e.target.value)}
                        placeholder="Info"
                        className="form-input"
                        style={{ height: "150px", resize: "vertical" }}
                      />
                    </label>
                    <label>
                      Scale:
                      <select
                        value={scale}
                        onChange={(e) => setScale(e.target.value)}
                        className="form-input"
                      >
                        <option value="">Select Scale</option>
                        {scaleOptions.map((opt) => (
                          <option key={opt} value={opt}>
                            {opt}
                          </option>
                        ))}
                      </select>
                    </label>
                    <label>
                      Urgency:
                      <select
                        value={urgency}
                        onChange={(e) => setUrgency(e.target.value)}
                        className="form-input"
                      >
                        <option value="">Select Urgency</option>
                        {urgencyOptions.map((opt) => (
                          <option key={opt} value={opt}>
                            {opt}
                          </option>
                        ))}
                      </select>
                    </label>
                  </div>

            
                  <div
                    style={{
                      backgroundColor: "#fff",
                      border: "1px solid #ddd",
                      borderRadius: "8px",
                      padding: "20px",
                      maxWidth: "100%",
                      margin: "20px auto",
                      boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                    }}
                  >
                    <h3
                      style={{
                        marginBottom: "1rem",
                        fontSize: "1.5rem",
                        fontWeight: "bold",
                        textAlign: "left",
                        color: "#333",
                      }}
                    >
                      Event Location
                    </h3>

                    <div style={{ marginBottom: "1rem" }}>
                      <label
                        style={{
                          display: "block",
                          marginBottom: "0.5rem",
                          fontWeight: 500,
                        }}
                      >
                        Common Name:
                        <input
                          value={location.commonName || ""}
                          onChange={(e) =>
                            setLocation((prev) => ({
                              ...prev,
                              commonName: e.target.value,
                            }))
                          }
                          placeholder="e.g., Station 5"
                          style={{
                            width: "100%",
                            padding: "0.5rem",
                            border: "1px solid #ccc",
                            borderRadius: "4px",
                            fontSize: "1rem",
                            marginTop: "0.5rem",
                          }}
                        />
                      </label>
                    </div>

                    {mapsLoaded ? (
                      <>
                        <div style={{ marginBottom: "1rem" }}>
                          <Autocomplete
                            onLoad={onLoadEventAutocomplete}
                            onPlaceChanged={() => onPlaceChanged()}
                          >
                            <input
                              value={location.address || ""}
                              onChange={(e) =>
                                setLocation((prev) => ({
                                  ...prev,
                                  address: e.target.value,
                                }))
                              }
                              placeholder="Address"
                              style={{
                                width: "100%",
                                padding: "0.5rem",
                                border: "1px solid #ccc",
                                borderRadius: "4px",
                                fontSize: "1rem",
                                marginTop: "0.5rem",
                              }}
                            />
                          </Autocomplete>
                        </div>
                        {/* <button
        type="button"
        onClick={() => setShowMapPicker(null)}
        style={{
          backgroundColor: "#007bff",
          color: "#fff",
          border: "none",
          padding: "0.5rem 1rem",
          borderRadius: "4px",
          fontSize: "1rem",
          cursor: "pointer",
          marginTop: "0.5rem",
          display: "block",
          maxWidth: "200px",
          marginLeft: "auto",
          marginRight: "auto",
        }}
      >
        Pick Location on Map
      </button> */}
                      </>
                    ) : (
                      <div style={{ marginBottom: "1rem" }}>
                        <input
                          value={location.address || ""}
                          onChange={(e) =>
                            setLocation((prev) => ({
                              ...prev,
                              address: e.target.value,
                            }))
                          }
                          placeholder="Address (Maps Unavailable)"
                          style={{
                            width: "100%",
                            padding: "0.5rem",
                            border: "1px solid #ccc",
                            borderRadius: "4px",
                            fontSize: "1rem",
                            marginTop: "0.5rem",
                          }}
                        />
                      </div>
                    )}

                    <div style={{ marginBottom: "1rem" }}>
                      <label
                        style={{
                          display: "block",
                          marginBottom: "0.5rem",
                          fontWeight: 500,
                        }}
                      >
                        City:
                        <input
                          value={location.city || ""}
                          onChange={(e) =>
                            setLocation((prev) => ({
                              ...prev,
                              city: e.target.value,
                            }))
                          }
                          placeholder="City"
                          style={{
                            width: "100%",
                            padding: "0.5rem",
                            border: "1px solid #ccc",
                            borderRadius: "4px",
                            fontSize: "1rem",
                            marginTop: "0.5rem",
                          }}
                        />
                      </label>
                    </div>

                    <div style={{ marginBottom: "1rem" }}>
                      <label
                        style={{
                          display: "block",
                          marginBottom: "0.5rem",
                          fontWeight: 500,
                        }}
                      >
                        State:
                        <input
                          value={location.state || ""}
                          onChange={(e) =>
                            setLocation((prev) => ({
                              ...prev,
                              state: e.target.value,
                            }))
                          }
                          placeholder="State"
                          style={{
                            width: "100%",
                            padding: "0.5rem",
                            border: "1px solid #ccc",
                            borderRadius: "4px",
                            fontSize: "1rem",
                            marginTop: "0.5rem",
                          }}
                        />
                      </label>
                    </div>

                    <div style={{ marginBottom: "1rem" }}>
                      <label
                        style={{
                          display: "block",
                          marginBottom: "0.5rem",
                          fontWeight: 500,
                        }}
                      >
                        Zip:
                        <input
                          value={location.zip || ""}
                          onChange={(e) =>
                            setLocation((prev) => ({
                              ...prev,
                              zip: e.target.value,
                            }))
                          }
                          placeholder="Zip"
                          style={{
                            width: "100%",
                            padding: "0.5rem",
                            border: "1px solid #ccc",
                            borderRadius: "4px",
                            fontSize: "1rem",
                            marginTop: "0.5rem",
                          }}
                        />
                      </label>
                    </div>

                    <label
                      style={{
                        display: "flex",
                        alignItems: "center",
                        marginBottom: "16px",
                        marginLeft: "5px",
                        fontSize: "14px",
                      }}
                    >
                      <input
                        type="checkbox"
                        onChange={handlePreviousLocationCheck}
                        style={{
                          marginRight: "12px",
                          transform: "scale(1.2)",
                        }} // Slightly increase the checkbox size
                      />
                      <span style={{ color: "#333", fontWeight: "500" }}>
                        Do you use same as Report-to Locations?
                      </span>
                    </label>
                  </div>

                  {/* Event Type and Notification Settings */}
                  <div
                    style={{
                      backgroundColor: "#fff",
                      border: "1px solid #ddd",
                      borderRadius: "8px",
                      padding: "20px",
                      maxWidth: "100%",
                      margin: "20px auto",
                      boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                    }}
                  >
                    <h3
                      style={{
                        marginBottom: "1rem",
                        fontSize: "1.5rem",
                        fontWeight: "bold",
                        textAlign: "left",
                        color: "#333",
                      }}
                    >
                      Event Type & Notifications
                    </h3>

                    <label style={{ display: "block", marginBottom: "1rem" }}>
                      Event Type:
                      <select
                        value={eventType}
                        onChange={(e) => setEventType(e.target.value)}
                        className="form-input"
                        style={{ marginTop: "0.5rem" }}
                      >
                        <option value="response">Response Event</option>
                        <option value="notification_only">Notification Only Event</option>
                      </select>
                      <small style={{ color: "#666", fontSize: "12px", display: "block", marginTop: "4px" }}>
                        Response events require responders to report to locations. Notification-only events are for awareness only.
                      </small>
                    </label>

                    <label style={{ display: "block", marginBottom: "1rem" }}>
                      Notification Methods:
                      <div style={{ display: "flex", flexWrap: "wrap", gap: "12px", marginTop: "8px" }}>
                        {["voice_call", "sms", "web_app", "email"].map((channel) => (
                          <label key={channel} style={{ display: "flex", alignItems: "center", gap: "6px" }}>
                            <input
                              type="checkbox"
                              checked={notificationChannels.includes(channel)}
                              onChange={(e) => {
                                const isChecked = e.target.checked;
                                setNotificationChannels(prev => 
                                  isChecked 
                                    ? [...prev, channel]
                                    : prev.filter(c => c !== channel)
                                );
                              }}
                            />
                            <span style={{ fontSize: "14px" }}>
                              {channel === "voice_call" ? "Voice Call" :
                               channel === "sms" ? "SMS" :
                               channel === "web_app" ? "Web App" : "Email"}
                            </span>
                          </label>
                        ))}
                      </div>
                    </label>

                    {eventType === "response" && (
                      <label style={{ display: "block", marginBottom: "1rem" }}>
                        Location Update Frequency:
                        <select
                          value={locationUpdateInterval}
                          onChange={(e) => setLocationUpdateInterval(parseInt(e.target.value))}
                          className="form-input"
                          style={{ marginTop: "0.5rem" }}
                        >
                          <option value={5}>Every 5 seconds</option>
                          <option value={10}>Every 10 seconds</option>
                          <option value={15}>Every 15 seconds</option>
                          <option value={30}>Every 30 seconds</option>
                          <option value={60}>Every 1 minute</option>
                          <option value={300}>Every 5 minutes</option>
                          <option value={600}>Every 10 minutes</option>
                        </select>
                        <small style={{ color: "#666", fontSize: "12px", display: "block", marginTop: "4px" }}>
                          How often responders' locations will be updated during the event.
                        </small>
                      </label>
                    )}
                  </div>

                  {/* Documents Section */}
                  <div
                    style={{
                      backgroundColor: "#fff",
                      border: "1px solid #ddd",
                      borderRadius: "8px",
                      padding: "20px",
                      maxWidth: "100%",
                      margin: "20px auto",
                      boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                    }}
                  >
                    <h3
                      style={{
                        marginBottom: "1rem",
                        fontSize: "1.5rem",
                        fontWeight: "bold",
                        textAlign: "left",
                        color: "#333",
                      }}
                    >
                      Event Documents
                    </h3>
                    
                    <p style={{ color: "#666", fontSize: "14px", marginBottom: "15px" }}>
                      Upload documents that will be shared with responders. These will be used to generate AI-powered action items based on each responder's role.
                    </p>
                    
                    <div style={{ marginBottom: "15px" }}>
                      <input
                        type="file"
                        multiple
                        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
                        onChange={handleDocumentUpload}
                        style={{
                          padding: "8px",
                          border: "1px solid #ddd",
                          borderRadius: "4px",
                          width: "100%",
                        }}
                      />
                      <small style={{ color: "#666", fontSize: "12px", display: "block", marginTop: "4px" }}>
                        Supported formats: PDF, Word documents, Images (JPG, PNG, GIF)
                      </small>
                    </div>
                    
                    {eventDocuments.length > 0 && (
                      <div>
                        <h4 style={{ marginBottom: "10px", color: "#333" }}>Uploaded Documents:</h4>
                        <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
                          {eventDocuments.map((doc, index) => (
                            <div
                              key={index}
                              style={{
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                                padding: "8px 12px",
                                backgroundColor: "#f8f9fa",
                                borderRadius: "4px",
                                border: "1px solid #e9ecef",
                              }}
                            >
                              <span style={{ fontSize: "14px", color: "#495057" }}>
                                {doc.name} ({(doc.size / 1024).toFixed(1)} KB)
                              </span>
                              <button
                                type="button"
                                onClick={() => removeDocument(index)}
                                style={{
                                  backgroundColor: "#dc3545",
                                  color: "white",
                                  border: "none",
                                  padding: "4px 8px",
                                  borderRadius: "4px",
                                  cursor: "pointer",
                                  fontSize: "12px",
                                }}
                              >
                                Remove
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  <div
                    style={{
                      backgroundColor: "#fff",
                      // borderTop: "1px solid #ddd",
                      border: "1px solid #ddd",
                      borderRadius: "8px",
                      padding: "20px",
                      maxWidth: "100%",
                      margin: "20px auto",
                      // boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                    }}
                  >
                    <div className="report-to-locations">
                      <h3 style={{marginBottom: "1rem",}} >{eventType === "notification_only" ? "Reference Locations (Optional)" : "Report to Locations"}</h3>

                      {reportToLocations.map((loc, index) => (
                        <div
                          key={index}
                          style={{
                            border: "1px solid #ddd",
                            padding: "1rem",
                            marginBottom: "1rem",
                            borderRadius: "5px",
                          }}
                        >
                          <label
                            style={{ display: "block", marginTop: "0.5rem" }}
                          >
                            Common Name:
                            <input
                              value={loc.commonName || ""}
                              onChange={(e) => {
                                const newLocs = [...reportToLocations];
                                newLocs[index].commonName = e.target.value;
                                setReportToLocations(newLocs);
                              }}
                              placeholder="e.g., The Paper Factory"
                              className="form-input"
                              disabled={   
                                checked &&
                                index === 0 &&
                                reportToLocations[0]
                              }
                            />
                          </label>
                          <label
                            style={{ flex: "1 1 100%", marginTop: "0.5rem" }}
                          >
                            Previous Locations:
                            <select
                              value={`${loc.address}, ${loc.city}, ${loc.state} ${loc.zip}`}
                              onChange={(e) =>
                                handleLocationSelect(index, e.target.value)
                              }
                              className="form-input"
                              disabled={   
                                checked &&
                                index === 0 &&
                                reportToLocations[0]
                              }
                            >
                              <option value="">Type or Select</option>
                              {previousLocations.map((prevLoc, i) => (
                                <option key={i} value={prevLoc}>
                                  {prevLoc}
                                </option>
                              ))}
                            </select>
                          </label>
                          <label
                            style={{ display: "block", marginTop: "0.5rem" }}
                          >
                            Address:
                            {mapsLoaded ? (
                              <>
                                <Autocomplete
                                  onLoad={(autoC) =>
                                    onLoadReportAutocomplete(autoC, index)
                                  }
                                  onPlaceChanged={() => onPlaceChanged(index)}
                                  disabled={   
                                    checked &&
                                    index === 0 &&
                                    reportToLocations[0]
                                  }
                                >
                                  <input
                                    value={loc.address || ""}
                                    onChange={(e) => {
                                      const newLocs = [...reportToLocations];
                                      newLocs[index].address = e.target.value;
                                      setReportToLocations(newLocs);
                                    }}
                                    placeholder="Address"
                                    className="form-input"
                                    disabled={   
                                      checked &&
                                      index === 0 &&
                                      reportToLocations[0]
                                    }
                                  />
                                </Autocomplete>
                                { checked &&
                                    index === 0 &&
                                    reportToLocations[0] ? null : (
                                <button
                                  type="button"
                                  className="btn-secondary"
                                  onClick={() => setShowMapPicker(index)}
                                  style={{ marginTop: "0.5rem" }}
                                  disabled={   
                                    checked &&
                                    index === 0 &&
                                    reportToLocations[0]
                                  }
                                >
                                  Pick Location on Map
                                </button>
                                )}
                              </>
                            ) : (
                              <input
                                value={loc.address || ""}
                                onChange={(e) => {
                                  const newLocs = [...reportToLocations];
                                  newLocs[index].address = e.target.value;
                                  setReportToLocations(newLocs);
                                }}
                                placeholder="Address (Maps Unavailable)"
                                className="form-input"
                                disabled={   
                                  checked &&
                                  index === 0 &&
                                  reportToLocations[0]
                                }
                              />
                            )}
                          </label>
                          <label
                            style={{ display: "block", marginTop: "0.5rem" }}
                          >
                            City:
                            <input
                              value={loc.city || ""}
                              onChange={(e) => {
                                const newLocs = [...reportToLocations];
                                newLocs[index].city = e.target.value;
                                setReportToLocations(newLocs);
                              }}
                              placeholder="City"
                              className="form-input"
                              disabled={   
                                checked &&
                                index === 0 &&
                                reportToLocations[0]
                              }
                            />
                          </label>
                          <label
                            style={{ display: "block", marginTop: "0.5rem" }}
                          >
                            State:
                            <input
                              value={loc.state || ""}
                              onChange={(e) => {
                                const newLocs = [...reportToLocations];
                                newLocs[index].state = e.target.value;
                                setReportToLocations(newLocs);
                              }}
                              placeholder="State"
                              className="form-input"
                              disabled={   
                                checked &&
                                index === 0 &&
                                reportToLocations[0]
                              }
                            />
                          </label>
                          <label
                            style={{ display: "block", marginTop: "0.5rem" }}
                          >
                            Zip:
                            <input
                              value={loc.zip || ""}
                              onChange={(e) => {
                                const newLocs = [...reportToLocations];
                                newLocs[index].zip = e.target.value;
                                setReportToLocations(newLocs);
                              }}
                              placeholder="Zip"
                              className="form-input"
                              disabled={   
                                checked &&
                                index === 0 &&
                                reportToLocations[0]
                              }
                            />
                          </label>
                          <label
                            style={{ display: "block", marginTop: "0.5rem" }}
                          >
                            Primary:
                            <input
                              type="checkbox"
                              checked={loc.primary || false}
                              onChange={(e) => {
                                const newLocs = [...reportToLocations];
                                newLocs[index].primary = e.target.checked;
                                setReportToLocations(newLocs);
                              }}
                              style={{ marginLeft: "0.5rem" }}
                            />
                          </label>
                          <label
                            style={{ display: "block", marginTop: "0.5rem" }}
                          >
                            Staff Needed:
                            <input
                              type="number"
                              value={loc.staffNeeded || 2}
                              onChange={(e) => {
                                const newLocs = [...reportToLocations];
                                newLocs[index].staffNeeded =
                                  parseInt(e.target.value) || 2;
                                setReportToLocations(newLocs);
                              }}
                              placeholder="Staff Needed"
                              className="form-input"
                            />
                          </label>
                          <div
                            className="resources"
                            style={{ marginTop: "1rem" }}
                          >
                            <h4>Resources</h4>
                            {loc.resources.map((res, resIndex) => (
                              <div
                                key={resIndex}
                                style={{
                                  display: "flex",
                                  gap: "1rem",
                                  marginBottom: "0.5rem",
                                  alignItems: "center",
                                }}
                              >
                                <input
                                  value={res.name || ""}
                                  onChange={(e) =>
                                    handleResourceChange(
                                      index,
                                      resIndex,
                                      "name",
                                      e.target.value
                                    )
                                  }
                                  placeholder="Resource Name (e.g., Ambulance 1)"
                                  className="form-input"
                                  style={{ flex: "1" }}
                                />
                                <input
                                  type="number"
                                  value={res.responderCount || 2}
                                  onChange={(e) =>
                                    handleResourceChange(
                                      index,
                                      resIndex,
                                      "responderCount",
                                      e.target.value
                                    )
                                  }
                                  placeholder="Responders Needed"
                                  className="form-input"
                                  style={{ width: "150px" }}
                                />
                                <button
                                  type="button"
                                  className="btn-danger"
                                  onClick={() =>
                                    removeResource(index, resIndex)
                                  }
                                >
                                  Remove
                                </button>
                              </div>
                            ))}
                            <button
                              type="button"
                              className="btn-secondary"
                              onClick={() => addResource(index)}
                              style={{ marginTop: "0.5rem" }}
                            >
                              Add Resource
                            </button>
                          </div>

                          <div
                            style={{
                              display: "flex",
                              flexDirection: "row",
                              gap: "1rem", // Adds spacing between buttons
                              alignItems: "center", // Vertically aligns buttons
                              justifyContent: "flex-start", // Aligns buttons to the left
                              marginTop: "1rem", // Adds spacing from previous content
                            }}
                          >
                            <button
                              type="button"
                              className="btn-secondary"
                              onClick={addLocation}
                              style={{
                                color: "#fff",
                                border: "none",
                                cursor: "pointer",
                              }}
                            >
                              Add Location
                            </button>

                            <button
                              type="button"
                              className="btn-secondary"
                              onClick={() => clearLocation(index)}
                              style={{
                                backgroundColor: "#6c757d",
                                color: "#fff",
                                border: "none",
                                cursor: "pointer",
                              }}
                            >
                              Clear Location
                            </button>

                            <button
                              type="button"
                              className="btn-danger"
                              onClick={() => deleteLocation(index)}
                              style={{
                                backgroundColor: "#dc3545",
                                color: "#fff",
                                border: "none",
                                cursor: "pointer",
                              }}
                            >
                              Delete Location
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div
                    style={{
                      backgroundColor: "#fff",
                      border: "1px solid #ddd",
                      borderRadius: "8px",
                      padding: "20px",
                      maxWidth: "100%",
                      margin: "20px auto",
                      boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                    }}
                  >
                    <label>
                      Status:
                      <select
                        value={status}
                        onChange={(e) => setStatus(e.target.value)}
                        className="form-input"
                      >
                        <option value="open">Open</option>
                        <option value="resolved">Resolved</option>
                      </select>
                    </label>
                    <label>
                      Assigned To (comma-separated IDs):
                      <input
                        value={assignedTo.join(",") || ""}
                        onChange={(e) =>
                          setAssignedTo(e.target.value.split(","))
                        }
                        placeholder="Assigned To"
                        className="form-input"
                      />
                    </label>
                  </div>

                  <div
                    style={{ display: "flex", gap: "1rem", marginTop: "1rem" }}
                  >
                    <button type="submit" className="btn-primary">
                      {selectedTemplate ? "Update Template" : "Save Template"}
                    </button>
                    <button
                      type="button"
                      className="btn-secondary"
                      onClick={resetForm}
                    >
                      Clear Form
                    </button>
                  </div>
                </form>
              </>
            )}
          </div>
        )}
      </div>

      {showDeleteModal && (
        <div className="delete-modal-overlay">
          <div className="delete-modal">
            <h3>Confirm Deletion</h3>
            <p>Are you sure you want to delete this template?</p>
            <div className="delete-modal-buttons">
              <button className="btn-danger" onClick={confirmDelete}>
                Yes, Delete
              </button>
              <button className="btn-secondary" onClick={cancelDelete}>
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {showMapPicker !== null && mapsLoaded && (
        <div className="delete-modal-overlay">
          <div
            className="delete-modal"
            style={{ width: "80%", maxWidth: "800px" }}
          >
            <h3>Pick Location on Map</h3>
            <GoogleMap
              mapContainerStyle={{ width: "100%", height: "400px" }}
              center={mapCenter}
              zoom={10}
              onClick={handleMapClick}
            >
              {selectedPosition && <Marker position={selectedPosition} />}
            </GoogleMap>
            <div
              style={{
                display: "flex",
                gap: "1rem",
                marginTop: "1rem",
                justifyContent: "center",
              }}
            >
              <button
                className="btn-primary"
                onClick={() => setShowMapPicker(null)}
              >
                Confirm
              </button>
              <button
                className="btn-secondary"
                onClick={() => setShowMapPicker(null)}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default Templates;
