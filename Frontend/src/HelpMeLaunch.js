import React, { useState, useRef } from "react";
import { Autocomplete, GoogleMap, Marker } from "@react-google-maps/api";
import { toast } from "react-toastify";
import Footer from "./Footer";
import Select from "react-select";
const HelpMeLaunch = ({
  formData,
  setFormData,
  formConfig,
  responders,
  locations,
  previousLocations,
  theme,
  mapsLoaded,
  launchEvent,
  onLoadEventAutocomplete,
  onLoadReportAutocomplete,
  onPlaceChanged,
  setShowMapPicker,
  showMapPicker,
  mapCenter,
  selectedPosition,
  handleMapClick,
  selectedModule,
  setMode,
  token,
  baseUrl,
}) => {
  const [guidedStep, setGuidedStep] = useState(0);
  const inputRefs = useRef({});

  const roles = ["staff", "lead", "viewer"];
  const [role, setRole] = React.useState("");
  // const [formData, setFormData] = React.useState({ assignedIds: [] });
  const [filteredResponders, setFilteredResponders] =
    React.useState(responders);
  const [customResources, setCustomResources] = React.useState([]);
  const [allResources, setAllResources] = React.useState([]);

  // Fetch custom resources from API
  React.useEffect(() => {
    const fetchCustomResources = async () => {
      if (token && baseUrl) {
        try {
          const response = await fetch(`${baseUrl}/resources`, {
            headers: { Authorization: `Bearer ${token}` },
          });
          if (response.ok) {
            const data = await response.json();
            setCustomResources(data);

            // Only use custom resources (no theme resources)
            const customResourcesFormatted = data.map(r => ({ name: r.name, source: 'custom', ...r }));
            setAllResources(customResourcesFormatted);
          } else {
            setAllResources([]);
          }
        } catch (error) {
          console.error("Error fetching custom resources:", error);
          setAllResources([]);
        }
      } else {
        setAllResources([]);
      }
    };

    fetchCustomResources();
  }, [token, baseUrl]);

  React.useEffect(() => {
    // Filter responders based on the selected role
    if (role) {
      const filtered = responders.filter(
        (responder) => responder.role === role
      );
      setFilteredResponders(filtered);

      // Automatically select responders with the selected role
      const selectedIds = filtered.map((responder) => responder.id);
      setFormData((prev) => ({ ...prev, assignedIds: selectedIds }));
    } else {
      setFilteredResponders(responders); // Show all responders if no role selected
      setFormData((prev) => ({ ...prev, assignedIds: [] })); // Clear selection
    }
  }, [role, responders]);

  // Function to handle previous location checkbox change
  const handlePreviousLocationCheck = (e) => {
    const isChecked = e.target.checked;
    const defaultLocation = {
      commonName: isChecked ? formData.location.commonName || "" : "",
      address: isChecked ? formData.location.address || "" : "",
      city: isChecked ? formData.location.city || "" : "",
      state: isChecked ? formData.location.state || "" : "",
      zip: isChecked ? formData.location.zip || "" : "",
      primary: false,
      staffNeeded: isChecked ? formData.location.staffNeeded || 1 : 2,
      resources: [],
    };

    setFormData({
      ...formData,
      usePreviousLocation: isChecked,
      included_report_to_locations: [defaultLocation],
    });

    if (isChecked) {
      toast.success("Previous locations added successfully!", {
        position: "top-right",
        autoClose: 3000,
      });
    }
  };

  const steps = [
    {
      title: "Event Details",
      questions: formConfig
        .filter((field) =>
          ["title", "description", "info"].includes(field.name)
        )
        .map((field) => ({
          key: field.name,
          label: field.label,
          type: field.type,
          required: field.required,
          nested: field.nested || false,
        })),
    },
    {
      title: "Scale and Urgency",
      questions: formConfig
        .filter((field) => ["scale", "urgency"].includes(field.name))
        .map((field) => ({
          key: field.name,
          label: field.label,
          type: field.type,
          options: field.options,
          required: field.required,
          nested: field.nested || false,
        })),
    },
    {
      title: "Event Location",
      questions: [
        ...formConfig
          .filter((field) => field.name.startsWith("location."))
          .map((field) => ({
            key: field.name,
            label: field.label,
            type: field.type,
            required: field.required,
            nested: field.nested || false,
          })),
        // Only add one checkbox here, if needed
        {
          key: "checkbox",
        },
      ],
    },

    {
      title: "Event Type",
      questions: [
        {
          label: "Select Event Type:",
          key: "event_type",
          type: "select",
          options: [
            { value: "response", label: "Response Event" },
            { value: "notification_only", label: "Notification Only Event" }
          ],
          required: true,
          description: "Response events require responders to report to locations. Notification-only events are for awareness only."
        }
      ],
    },

    {
      title: "Event Configuration",
      questions: [
        {
          label: "Notification Methods:",
          key: "notification_channels",
          type: "multiselect_checkbox",
          options: [
            { value: "voice_call", label: "Voice Call" },
            { value: "sms", label: "SMS" },
            { value: "web_app", label: "Web App" },
            { value: "email", label: "Email" }
          ],
        },
        {
          label: "Location Update Frequency:",
          key: "location_update_interval",
          type: "select",
          options: [
            { value: 5, label: "Every 5 seconds" },
            { value: 10, label: "Every 10 seconds" },
            { value: 15, label: "Every 15 seconds" },
            { value: 30, label: "Every 30 seconds" },
            { value: 60, label: "Every 1 minute" },
            { value: 300, label: "Every 5 minutes" },
            { value: 600, label: "Every 10 minutes" }
          ],
          conditional: { key: "event_type", value: "response" }, // Only show for response events
        },
      ],
    },

    {
      title: "Report-to Locations",
      questions: [
        {
          label: "Add report-to locations:",
          key: "included_report_to_locations",
          type: "locations",
        },
      ],
    },
    {
      title: "Responders",
      questions: [
        {
          label: "Assign specific responders:",
          key: "assignedIds",
          type: "multiselect",
          options: responders.map((r) => ({
            value: r.id,
            label: r.username,
          })),
        },
        {
          label: "Notify all ?",
          key: "notifyAllIfUnassigned",
          type: "checkbox",
        },
      ],
    },
  ];

  const handleResponderChange = (selectedOptions) => {
    const selectedIds = selectedOptions
      ? selectedOptions.map((option) => option.value)
      : [];
    setFormData((prev) => ({ ...prev, assignedIds: selectedIds }));
  };
  const handleInputChange = (key, value, nested = false) => {
    if (nested) {
      const [parent, child] = key.split(".");
      if (parent === "location") {
        setFormData((prev) => ({
          ...prev,
          location: { ...prev.location, [child]: value },
        }));
      }
    } else {
      setFormData((prev) => ({ ...prev, [key]: value }));
    }
  };

  const addLocation = () => {
    setFormData((prev) => ({
      ...prev,
      included_report_to_locations: [
        ...prev.included_report_to_locations,
        {
          commonName: "",
          address: "",
          city: "",
          state: "",
          zip: "",
          primary: false,
          staffNeeded: 2,
          resources: [{ name: "", responderCount: 2, requiredRoles: [] }],
        },
      ],
    }));
    setTimeout(() => {
      const newIndex = formData.included_report_to_locations.length;
      const refKey = `location-${newIndex}-commonName`;
      if (inputRefs.current[refKey]) {
        inputRefs.current[refKey].focus();
      }
    }, 0);
  };

  const handleLocationChange = (index, field, value) => {
    setFormData((prev) => {
      const newLocs = [...prev.included_report_to_locations];
      newLocs[index] = {
        ...newLocs[index],
        [field]: field === "staffNeeded" ? parseInt(value) || 2 : value,
        // Clear commonName when user manually types in address field
        ...(field === "address" && { commonName: "" })
      };
      return { ...prev, included_report_to_locations: newLocs };
    });
  };

  const deleteLocation = (index) => {
    setFormData((prev) => ({
      ...prev,
      included_report_to_locations: prev.included_report_to_locations.filter(
        (_, i) => i !== index
      ),
    }));
    Object.keys(inputRefs.current).forEach((key) => {
      if (key.startsWith(`location-${index}-`)) {
        delete inputRefs.current[key];
      }
    });
  };

  const addResource = (locIndex) => {
    setFormData((prev) => {
      const newLocs = [...prev.included_report_to_locations];
      newLocs[locIndex].resources.push({
        name: "",
        responderCount: 2,
        requiredRoles: [],
      });
      return { ...prev, included_report_to_locations: newLocs };
    });
  };

  const removeResource = (locIndex, resIndex) => {
    setFormData((prev) => {
      const newLocs = [...prev.included_report_to_locations];
      newLocs[locIndex].resources.splice(resIndex, 1);
      return { ...prev, included_report_to_locations: newLocs };
    });
  };

  const handleResourceChange = (locIndex, resIndex, field, value) => {
    setFormData((prev) => {
      const newLocs = [...prev.included_report_to_locations];
      newLocs[locIndex].resources[resIndex][field] =
        field === "responderCount" ? parseInt(value) || 2 : value;
      return { ...prev, included_report_to_locations: newLocs };
    });
  };

  const handleLocationSelect = (index, value) => {
    if (!value || value === "") {
      // Clear the location if empty value selected
      setFormData((prev) => {
        const newLocs = [...prev.included_report_to_locations];
        newLocs[index] = {
          ...newLocs[index],
          commonName: "",
          address: "",
          city: "",
          state: "",
          zip: "",
          lat: null,
          lng: null
        };
        return { ...prev, included_report_to_locations: newLocs };
      });
      return;
    }

    const selectedLocation = locations.find(
      (loc) => `${loc.commonName} - ${loc.address}` === value
    );

    if (selectedLocation) {
      setFormData((prev) => {
        const newLocs = [...prev.included_report_to_locations];
        newLocs[index] = {
          ...newLocs[index],
          commonName: selectedLocation.commonName || "",
          address: selectedLocation.address || "",
          city: selectedLocation.city || "",
          state: selectedLocation.state || "",
          zip: selectedLocation.zip || "",
          lat: selectedLocation.lat || null,
          lng: selectedLocation.lng || null
        };
        return { ...prev, included_report_to_locations: newLocs };
      });
    } else if (previousLocations.includes(value)) {
      const parts = value.split(/,\s*/).map((part) => part.trim());
      const address = parts[0] || "";
      const city = parts[1] || "";
      const stateZip = parts[2] || "";
      const [state, zip] = stateZip.split(/\s+/).map((part) => part.trim());
      setFormData((prev) => {
        const newLocs = [...prev.included_report_to_locations];
        newLocs[index] = {
          ...newLocs[index],
          address,
          city,
          state: state || "",
          zip: zip || "",
          commonName: address,
          lat: null,
          lng: null
        };
        return { ...prev, included_report_to_locations: newLocs };
      });
    }
  };

  const handleNext = () => {
    setGuidedStep((prev) => {
      const nextStep = prev + 1;
      return nextStep <= steps.length ? nextStep : prev;
    });
  };

  const handleBack = () => {
    setGuidedStep((prev) => (prev > 0 ? prev - 1 : 0));
  };

  const handleLaunchEvent = async (
    data,
    saveAsTemplate,
    isLaunchOnly = false
  ) => {
    const toastId = toast.loading("Creating event...");
    if (data.title === "") {
      toast.update(toastId, {
        render: "Please enter a title for the event.",
        type: "error",
        isLoading: false,
        autoClose: 3000,
      });
      return;
    }

    try {
      await new Promise((resolve) => setTimeout(resolve, 2000));
      await launchEvent(data, saveAsTemplate);
      toast.update(toastId, {
        render: isLaunchOnly
          ? "Launch Event successful!"
          : "Event created successfully!",
        type: "success",
        isLoading: false,
        autoClose: 3000,
      });
    } catch (err) {
      toast.update(toastId, {
        render: `Failed to create event: ${
          err.message || "An unexpected error occurred"
        }`,
        type: "error",
        isLoading: false,
        autoClose: 3000,
      });
    }
  };

  if (guidedStep < steps.length) {
    const currentStep = steps[guidedStep];

    if (!currentStep) {
      console.error("Invalid guidedStep:", guidedStep);
      return <div>Error: Invalid step. Please try again.</div>;
    }

    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          minHeight: "100vh",
          backgroundColor: theme.backgroundColor,
        }}
      >
        <div style={{ flex: "1" }}>
          <div
            className="container"
            style={{ padding: "20px", fontFamily: "Arial, sans-serif" }}
          >
            {/* <h1 style={{ color: theme.secondaryColor, marginBottom: "20px" }}>
              Help Me Launch - Step {guidedStep + 1}
            </h1> */}
            <div
              style={{
                backgroundColor: "#fff",
                padding: "20px",
                borderRadius: "8px",
                boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
              }}
            >
              <h2 style={{ color: "#333", marginBottom: "15px" }}>
                {currentStep.title}
              </h2>
              {currentStep.questions
                .filter(q => !q.conditional || formData[q.conditional.key] === q.conditional.value)
                .map((q) => (
                <div key={q.key} style={{ marginBottom: "15px" }}>
                  {/* {q.type === "text" && (
                    
                    <input
                      type="text"
                      value={
                        (q.nested
                          ? formData.location[q.key.split(".")[1]]
                          : formData[q.key]) || ""
                      }
                      onChange={(e) =>
                        handleInputChange(q.key, e.target.value, q.nested)
                      }
                      ref={(el) => (inputRefs.current[q.key] = el)}
                      placeholder={q.label}
                      style={{
                        width: "100%",
                        padding: "8px",
                        borderRadius: "4px",
                        border: "1px solid #ccc",
                      }}
                    />
                
                  )} */}
                  {q.type === "text" && (
                    <input
                      type="text"
                      value={
                        (q.nested
                          ? formData.location[q.key.split(".")[1]]
                          : formData[q.key]) || ""
                      }
                      onChange={(e) =>
                        handleInputChange(q.key, e.target.value, q.nested)
                      }
                      ref={(el) => (inputRefs.current[q.key] = el)}
                      placeholder={q.label}
                      style={{
                        width: "100%",
                        padding: "8px",
                        borderRadius: "4px",
                        border: "1px solid #ccc",
                      }}
                    />
                  )}
                  {q.key === "checkbox" && (
                    <label
                      style={{
                        display: "flex",
                        alignItems: "center",
                        marginBottom: "16px",
                        marginLeft: "5px",
                        fontSize: "14px",
                      }}
                    >
                      <input
                        type="checkbox"
                        onChange={handlePreviousLocationCheck}
                        style={{
                          marginRight: "12px",
                          transform: "scale(1.2)",
                        }} // Slightly increase the checkbox size
                      />
                      <span style={{ color: "#333", fontWeight: "500" }}>
                        Do you use same as Report-to Locations?
                      </span>
                    </label>
                  )}

                  {q.type === "textarea" && (
                    <textarea
                      value={formData[q.key] || ""}
                      onChange={(e) => handleInputChange(q.key, e.target.value)}
                      placeholder={q.label}
                      style={{
                        width: "100%",
                        padding: "8px",
                        borderRadius: "4px",
                        border: "1px solid #ccc",
                        minHeight: "80px",
                      }}
                    />
                  )}
                  {q.type === "select" && (
                    <select
                      value={formData[q.key] || ""}
                      onChange={(e) => handleInputChange(q.key, e.target.value)}
                      style={{
                        width: "100%",
                        padding: "8px",
                        borderRadius: "4px",
                        border: "1px solid #ccc",
                      }}
                    >
                      <option value="">Select {q.label}</option>
                      {(q.key === "scale"
                        ? ["Small", "Medium", "Large"]
                        : q.key === "urgency"
                        ? ["Low", "Medium", "High", "Immediate"]
                        : q.options || []
                      ).map((opt) => (
                        <option key={typeof opt === 'object' ? opt.value : opt} value={typeof opt === 'object' ? opt.value : opt}>
                          {typeof opt === 'object' ? opt.label : opt}
                        </option>
                      ))}
                    </select>
                  )}

                  {/* New field type: multiselect_checkbox */}
                  {q.type === "multiselect_checkbox" && (
                    <div>
                      <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold" }}>
                        {q.label}
                      </label>
                      <div style={{ display: "flex", flexWrap: "wrap", gap: "12px" }}>
                        {q.options.map((option) => (
                          <label key={option.value} style={{ display: "flex", alignItems: "center", gap: "6px" }}>
                            <input
                              type="checkbox"
                              checked={formData[q.key]?.includes(option.value) || false}
                              onChange={(e) => {
                                const isChecked = e.target.checked;
                                const currentValues = formData[q.key] || [];
                                const newValues = isChecked
                                  ? [...currentValues, option.value]
                                  : currentValues.filter(v => v !== option.value);
                                handleInputChange(q.key, newValues);
                              }}
                            />
                            <span style={{ fontSize: "14px" }}>{option.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  )}

                  {q.type === "autocomplete" && mapsLoaded && (
                    <>
                      <Autocomplete
                        onLoad={onLoadEventAutocomplete}
                        onPlaceChanged={() => onPlaceChanged()}
                      >
                        <input
                          type="text"
                          value={
                            formData.location.commonName ||
                            formData.location.address ||
                            ""
                          }
                          onChange={(e) =>
                            handleInputChange(
                              "location.address",
                              e.target.value,
                              true
                            )
                          }
                          ref={(el) =>
                            (inputRefs.current["location.address"] = el)
                          }
                          placeholder={q.label}
                          style={{
                            width: "100%",
                            padding: "8px",
                            borderRadius: "4px",
                            border: "1px solid #ccc",
                          }}
                        />
                      </Autocomplete>
                    </>
                  )}

                  {q.type === "locations" && (
                    <div>
                      {formData.included_report_to_locations.map(
                        (loc, locIndex) => (
                          <div
                            key={locIndex}
                            style={{
                              border: "2px solid #e0e0e0",
                              boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                              borderRadius: "8px",
                              padding: "16px",
                              marginBottom: "16px",
                              backgroundColor: "#f9f9f9",
                            }}
                          >
                            <input
                              type="text"
                              value={loc.commonName}
                              onChange={(e) =>
                                handleLocationChange(
                                  locIndex,
                                  "commonName",
                                  e.target.value
                                )
                              }
                              placeholder="Common Name"
                              ref={(el) =>
                                (inputRefs.current[
                                  `location-${locIndex}-commonName`
                                ] = el)
                              }
                              style={{
                                width: "100%",
                                padding: "12px",
                                borderRadius: "6px",
                                border: "1px solid #ddd",
                                marginBottom: "12px",
                                fontSize: "14px",
                              }}

                              disabled={formData.usePreviousLocation && locIndex === 0}
                            />
                            <select
                              value={
                                loc.commonName && loc.address
                                  ? `${loc.commonName} - ${loc.address}`
                                  : ""
                              }
                              onChange={(e) =>
                                handleLocationSelect(locIndex, e.target.value)
                              }
                              style={{
                                width: "100%",
                                padding: "12px",
                                borderRadius: "6px",
                                border: "1px solid #ddd",
                                marginBottom: "12px",
                                fontSize: "14px",
                              }}
                              disabled={formData.usePreviousLocation && locIndex === 0}
                            >
                              <option value="">Select Location</option>
                              {locations.map((loc, i) => (
                                <option
                                  key={i}
                                  value={`${loc.commonName} - ${loc.address}`}
                                >
                                  {loc.commonName} - {loc.address}
                                </option>
                              ))}
                              {previousLocations.map((loc, i) => (
                                <option key={`prev-${i}`} value={loc}>
                                  {loc}
                                </option>
                              ))}
                            </select>
                            {mapsLoaded && (
                              <Autocomplete
                                onLoad={(autoC) =>
                                  onLoadReportAutocomplete(autoC, locIndex)
                                }
                                onPlaceChanged={() => onPlaceChanged(locIndex)}
                              >
                                <input
                                  type="text"
                                  value={loc.commonName || loc.address || ""}
                                  onChange={(e) =>
                                    handleLocationChange(
                                      locIndex,
                                      "address",
                                      e.target.value
                                    )
                                  }
                                  placeholder="Address"
                                  ref={(el) =>
                                    (inputRefs.current[
                                      `location-${locIndex}-address`
                                    ] = el)
                                  }
                                  style={{
                                    width: "100%",
                                    padding: "12px",
                                    borderRadius: "6px",
                                    border: "1px solid #ddd",
                                    marginBottom: "12px",
                                    fontSize: "14px",
                                  }}
                                  disabled={formData.usePreviousLocation && locIndex === 0}
                                />
                              </Autocomplete>
                            )}
                            {!formData.usePreviousLocation  && locIndex === 0 &&(
                            <button
                              type="button"
                              onClick={() => setShowMapPicker(locIndex)}
                              style={{
                                padding: "10px 14px",
                                backgroundColor: theme.primaryColor,
                                color: "#fff",
                                borderRadius: "4px",
                                border: "none",
                                fontSize: "13px",
                                cursor: "pointer",
                                marginBottom: "12px",
                                display: "inline-block",
                              }}
                             
                            >
                              Pick on Map
                            </button>
                            )}
                            <input
                              type="text"
                              value={loc.city}
                              onChange={(e) =>
                                handleLocationChange(
                                  locIndex,
                                  "city",
                                  e.target.value
                                )
                              }
                              placeholder="City"
                              ref={(el) =>
                                (inputRefs.current[
                                  `location-${locIndex}-city`
                                ] = el)
                              }
                              style={{
                                width: "100%",
                                padding: "12px",
                                borderRadius: "6px",
                                border: "1px solid #ddd",
                                marginBottom: "12px",
                                fontSize: "14px",
                              }}
                              disabled={formData.usePreviousLocation && locIndex === 0}
                            />
                            <input
                              type="text"
                              value={loc.state}
                              onChange={(e) =>
                                handleLocationChange(
                                  locIndex,
                                  "state",
                                  e.target.value
                                )
                              }
                              placeholder="State"
                              ref={(el) =>
                                (inputRefs.current[
                                  `location-${locIndex}-state`
                                ] = el)
                              }
                              style={{
                                width: "100%",
                                padding: "12px",
                                borderRadius: "6px",
                                border: "1px solid #ddd",
                                marginBottom: "12px",
                                fontSize: "14px",
                              }}
                              disabled={formData.usePreviousLocation && locIndex === 0}
                            />
                            <input
                              type="text"
                              value={loc.zip}
                              onChange={(e) =>
                                handleLocationChange(
                                  locIndex,
                                  "zip",
                                  e.target.value
                                )
                              }
                              placeholder="Zip Code"
                              ref={(el) =>
                                (inputRefs.current[`location-${locIndex}-zip`] =
                                  el)
                              }
                              style={{
                                width: "100%",
                                padding: "12px",
                                borderRadius: "6px",
                                border: "1px solid #ddd",
                                marginBottom: "12px",
                                fontSize: "14px",
                              }}
                              disabled={formData.usePreviousLocation && locIndex === 0}
                            />
                            <label
                              style={{ display: "block", marginBottom: "12px" }}
                            >
                              <input
                                type="checkbox"
                                checked={loc.primary}
                                onChange={(e) =>
                                  handleLocationChange(
                                    locIndex,
                                    "primary",
                                    e.target.checked
                                  )
                                }
                                style={{ marginRight: "8px" }}
                              />
                              Primary
                            </label>
                            <input
                              type="number"
                              value={loc.staffNeeded}
                              onChange={(e) =>
                                handleLocationChange(
                                  locIndex,
                                  "staffNeeded",
                                  e.target.value
                                )
                              }
                              min="1"
                              placeholder="Staff Needed"
                              style={{
                                width: "100%",
                                padding: "12px",
                                borderRadius: "6px",
                                border: "1px solid #ddd",
                                marginBottom: "12px",
                                fontSize: "14px",
                              }}
                            />
                            <div style={{ marginBottom: "16px" }}>
                              {loc.resources.map((res, resIndex) => (
                                <div
                                  key={resIndex}
                                  style={{
                                    display: "flex",
                                    gap: "12px",
                                    alignItems: "center",
                                    marginBottom: "8px",
                                  }}
                                >
                                  <select
                                    value={res.name}
                                    onChange={(e) =>
                                      handleResourceChange(
                                        locIndex,
                                        resIndex,
                                        "name",
                                        e.target.value
                                      )
                                    }
                                    style={{
                                      flex: "1",
                                      padding: "12px",
                                      borderRadius: "6px",
                                      border: "1px solid #ddd",
                                      fontSize: "14px",
                                    }}
                                  >
                                    <option value="">Select Resource</option>
                                    {allResources.map((resource, idx) => (
                                      <option key={`${resource.name}-${idx}`} value={resource.name}>
                                        {resource.name}
                                      </option>
                                    ))}
                                  </select>
                                  <input
                                    type="number"
                                    value={res.responderCount}
                                    onChange={(e) =>
                                      handleResourceChange(
                                        locIndex,
                                        resIndex,
                                        "responderCount",
                                        e.target.value
                                      )
                                    }
                                    min="1"
                                    placeholder="Responder Count"
                                    style={{
                                      width: "120px",
                                      padding: "12px",
                                      borderRadius: "6px",
                                      border: "1px solid #ddd",
                                      fontSize: "14px",
                                    }}
                                  />
                                  <button
                                    type="button"
                                    onClick={() =>
                                      removeResource(locIndex, resIndex)
                                    }
                                    style={{
                                      backgroundColor: "#d32f2f",
                                      color: "#fff",
                                      padding: "8px 12px",
                                      borderRadius: "4px",
                                      border: "none",
                                      fontSize: "13px",
                                      cursor: "pointer",
                                    }}
                                    disabled={formData.usePreviousLocation && locIndex === 0}
                                  >
                                    Remove
                                  </button>
                                </div>
                              ))}
                              <button
                                type="button"
                                onClick={() => addResource(locIndex)}
                                style={{
                                  padding: "10px 14px",
                                  backgroundColor: theme.secondaryColor,
                                  color: "#fff",
                                  borderRadius: "4px",
                                  border: "none",
                                  fontSize: "13px",
                                  cursor: "pointer",
                                  display: "inline-block",
                                  marginTop: "8px",
                                }}
                              >
                                Add Resource
                              </button>
                            </div>
                            <button
                              type="button"
                              onClick={() => deleteLocation(locIndex)}
                              style={{
                                backgroundColor: "#d32f2f",
                                color: "#fff",
                                padding: "10px 14px",
                                borderRadius: "4px",
                                border: "none",
                                fontSize: "13px",
                                cursor: "pointer",
                                display: "inline-block",
                              }}
                              disabled={formData.usePreviousLocation && locIndex === 0}
                            >
                              Delete Location
                            </button>
                          </div>
                        )
                      )}
                      <button
                        type="button"
                        onClick={addLocation}
                        style={{
                          padding: "10px 14px",
                          backgroundColor: theme.secondaryColor,
                          color: "#fff",
                          borderRadius: "4px",
                          border: "none",
                          fontSize: "13px",
                          cursor: "pointer",
                          display: "inline-block",
                          marginTop: "16px",
                        }}
                      >
                        Add Location
                      </button>
                    </div>
                  )}

                  {q.type === "multiselect" && (
                    // {!formData.notifyAllIfUnassigned && (
                    <div style={{ marginBottom: "15px", marginTop: "20px" }}>
                      {/* Role Selection */}
                      <label>
                        Role:
                        <select
                          value={role}
                          onChange={(e) => setRole(e.target.value)}
                          className="form-input"
                          required
                        >
                          <option value="">Select Role</option>
                          {roles.map((roleOption) => (
                            <option key={roleOption} value={roleOption}>
                              {roleOption}
                            </option>
                          ))}
                        </select>
                      </label>

                      {/* Assigned Responders */}
                      <label>Assigned Responders:</label>
                      <Select
                        options={filteredResponders.map((responder) => ({
                          value: responder.id,
                          label: `${responder.username} (${responder.role})`,
                        }))}
                        isMulti
                        value={
                          formData.notifyAllIfUnassigned
                            ? []
                            : filteredResponders
                                .filter((responder) =>
                                  formData.assignedIds.includes(responder.id)
                                )
                                .map((responder) => ({
                                  value: responder.id,
                                  label: `${responder.username} (${responder.role})`,
                                }))
                        }
                        onChange={handleResponderChange}
                        closeMenuOnSelect={false}
                        classNamePrefix="select"
                        placeholder="Select responders"
                      />
                    </div>
                    // )}
                  )}

                  {q.type === "checkbox" && (
                    <label>
                      <input
                        type="checkbox"
                        onChange={(e) =>
                          handleInputChange(
                            "notifyAllIfUnassigned",
                            e.target.checked
                          )
                        }
                      />{" "}
                      {q.label}
                    </label>
                  )}
                </div>
              ))}
              <div style={{ display: "flex", gap: "10px", marginTop: "20px" }}>
                {guidedStep > 0 && (
                  <button
                    onClick={handleBack}
                    style={{
                      padding: "10px 20px",
                      backgroundColor: "#666",
                      color: "#fff",
                      borderRadius: "4px",
                      border: "none",
                    }}
                  >
                    Back
                  </button>
                )}
                <button
                  onClick={handleNext}
                  style={{
                    padding: "10px 20px",
                    backgroundColor: theme.secondaryColor,
                    color: "#fff",
                    borderRadius: "4px",
                    border: "none",
                  }}
                >
                  Next
                </button>
                <button
                  onClick={() => setMode(null)}
                  style={{
                    padding: "10px 20px",
                    backgroundColor: "#666",
                    color: "#fff",
                    borderRadius: "4px",
                    border: "none",
                  }}
                >
                  Cancel
                </button>
              </div>
            </div>
            {showMapPicker !== null && mapsLoaded && (
              <div
                style={{
                  position: "fixed",
                  top: 0,
                  left: 0,
                  width: "100%",
                  height: "100%",
                  backgroundColor: "rgba(0,0,0,0.5)",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <div
                  style={{
                    backgroundColor: "#fff",
                    padding: "20px",
                    borderRadius: "8px",
                    width: "80%",
                    maxWidth: "800px",
                  }}
                >
                  <h3>Pick Location on Map</h3>
                  <GoogleMap
                    mapContainerStyle={{ width: "100%", height: "400px" }}
                    center={mapCenter}
                    zoom={10}
                    onClick={handleMapClick}
                  >
                    {selectedPosition && <Marker position={selectedPosition} />}
                  </GoogleMap>
                  <div
                    style={{
                      display: "flex",
                      gap: "10px",
                      marginTop: "10px",
                      justifyContent: "center",
                    }}
                  >
                    <button
                      onClick={() => setShowMapPicker(null)}
                      style={{
                        padding: "10px 20px",
                        backgroundColor: theme.primaryColor,
                        color: "#fff",
                        borderRadius: "4px",
                        border: "none",
                      }}
                    >
                      Confirm
                    </button>
                    <button
                      onClick={() => setShowMapPicker(null)}
                      style={{
                        padding: "10px 20px",
                        backgroundColor: "#666",
                        color: "#fff",
                        borderRadius: "4px",
                        border: "none",
                      }}
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        <Footer token={token} />
      </div>
    );
  }

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        minHeight: "100vh",
        backgroundColor: theme.backgroundColor,
      }}
    >
      <div style={{ flex: "1" }}>
        <div
          className="container"
          style={{ padding: "20px", fontFamily: "Arial, sans-serif" }}
        >
          {/* <h1 style={{ color: theme.secondaryColor, marginBottom: "20px" }}>
            Help Me Launch - Finalize
          </h1> */}
          <div
            style={{
              backgroundColor: "#fff",
              padding: "20px",
              borderRadius: "8px",
              boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
            }}
          >
            <h2 style={{ color: "#333", marginBottom: "15px" }}>
              Review and Launch
            </h2>
            <div style={{ marginBottom: "20px" }}>
              <h3 style={{ color: theme.primaryColor, marginBottom: "10px" }}>
                Event Details
              </h3>
              <p>
                <strong>Title:</strong> {formData.title || "N/A"}
              </p>
              <p>
                <strong>Description:</strong> {formData.description || "N/A"}
              </p>
              <p>
                <strong>Info:</strong> {formData.infoReceipt || "N/A"}
              </p>
              <p>
                <strong>Scale:</strong> {formData.scale || "N/A"}
              </p>
              <p>
                <strong>Urgency:</strong> {formData.urgency || "N/A"}
              </p>
              <p>
                <strong>Location:</strong>{" "}
                {`${formData.location.commonName || ""}${
                  formData.location.commonName && formData.location.address
                    ? ", "
                    : ""
                }${formData.location.address || ""}${
                  formData.location.address || formData.location.commonName
                    ? ", "
                    : ""
                }${formData.location.city || ""}${
                  formData.location.city && formData.location.state ? ", " : ""
                }${formData.location.state || ""} ${
                  formData.location.zip || ""
                }`
                  .trim()
                  .replace(/,\s*,/g, ",") || "N/A"}
              </p>
            </div>

            <div style={{ marginBottom: "20px" }}>
              <h3 style={{ color: theme.primaryColor, marginBottom: "10px" }}>
                Report-to Locations
              </h3>
              {formData.included_report_to_locations.length > 0 ? (
                formData.included_report_to_locations.map((loc, index) => (
                  <div
                    key={index}
                    style={{
                      marginBottom: "15px",
                      padding: "10px",
                      border: "1px solid #eee",
                      borderRadius: "4px",
                    }}
                  >
                    <p>
                      <strong>Location {index + 1}:</strong>{" "}
                      {`${loc.commonName || ""}${
                        loc.commonName && loc.address ? ", " : ""
                      }${loc.address || ""}${
                        loc.address || loc.commonName ? ", " : ""
                      }${loc.city || ""}${loc.city && loc.state ? ", " : ""}${
                        loc.state || ""
                      } ${loc.zip || ""}`
                        .trim()
                        .replace(/,\s*,/g, ",") || "N/A"}
                    </p>
                    <p>
                      <strong>Primary:</strong> {loc.primary ? "Yes" : "No"}
                    </p>
                    <p>
                      <strong>Staff Needed:</strong> {loc.staffNeeded || "N/A"}
                    </p>
                    <p>
                      <strong>Resources:</strong>
                    </p>
                    {loc.resources.length > 0 ? (
                      <ul style={{ paddingLeft: "20px" }}>
                        {loc.resources.map((res, resIndex) => (
                          <li key={resIndex}>
                            {res.name || "Unnamed Resource"} -{" "}
                            {res.responderCount || "N/A"} responders
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <p>No resources assigned.</p>
                    )}
                  </div>
                ))
              ) : (
                <p>No report-to locations specified.</p>
              )}
            </div>

            <div style={{ marginBottom: "20px" }}>
              <h3 style={{ color: theme.primaryColor, marginBottom: "10px" }}>
                Responders
              </h3>
              <p>
                <strong>Assigned Responders:</strong>{" "}
                {formData.assignedIds.length > 0
                  ? formData.assignedIds
                      .map(
                        (id) =>
                          responders.find((r) => r.id === id)?.username ||
                          `ID ${id}`
                      )
                      .join(", ")
                  : "None"}
              </p>
              <p>
                <strong>Notify All if Unassigned:</strong>{" "}
                {formData.notifyAllIfUnassigned ? "Yes" : "No"}
              </p>
            </div>
            <div style={{ marginBottom: "20px" }}>
              <h3 style={{ color: theme.primaryColor, marginBottom: "10px" }}>
                Custom Fields
              </h3>
              {Object.entries(formData.customFields || {}).length > 0 ? (
                Object.entries(formData.customFields).map(([key, value]) => (
                  <p key={key}>
                    <strong>{key}:</strong> {value || "N/A"}
                  </p>
                ))
              ) : (
                <p>No custom fields specified.</p>
              )}
            </div>
            <div style={{ display: "flex", gap: "10px", marginTop: "20px" }}>
              <button
                onClick={() =>
                  handleLaunchEvent(
                    {
                      ...formData,
                      assignedIds: formData.assignedIds.filter((id) =>
                        responders.some((r) => r.id === id)
                      ),
                      module: selectedModule,
                    },
                    false,
                    true
                  )
                }
                style={{
                  padding: "10px 20px",
                  backgroundColor: theme.primaryColor,
                  color: "#fff",
                  borderRadius: "4px",
                  border: "none",
                }}
              >
                Launch Event
              </button>
              <button
                onClick={() =>
                  handleLaunchEvent(
                    {
                      ...formData,
                      assignedIds: formData.assignedIds.filter((id) =>
                        responders.some((r) => r.id === id)
                      ),
                      module: selectedModule,
                    },
                    true
                  )
                }
                style={{
                  padding: "10px 20px",
                  backgroundColor: theme.secondaryColor,
                  color: "#fff",
                  borderRadius: "4px",
                  border: "none",
                }}
              >
                Save as Template & Launch
              </button>
              <button
                onClick={handleBack}
                style={{
                  padding: "10px 20px",
                  backgroundColor: "#666",
                  color: "#fff",
                  borderRadius: "4px",
                  border: "none",
                }}
              >
                Back
              </button>
              <button
                onClick={() => setMode(null)}
                style={{
                  padding: "10px 20px",
                  backgroundColor: "#666",
                  color: "#fff",
                  borderRadius: "4px",
                  border: "none",
                }}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
      <Footer token={token} />
    </div>
  );
};

export default HelpMeLaunch;
