// src/context/UseTypeContext.js
import React, { createContext, useState, useContext, useEffect, useCallback } from "react";

const UseTypeContext = createContext();
const COMPANY_SETTINGS_COOKIE = 'alertcomm_company_settings';
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

// Cookie utility functions for company settings caching
const setCookie = (name, value, minutes = 5) => {
  const expires = new Date();
  expires.setTime(expires.getTime() + (minutes * 60 * 1000));
  document.cookie = `${name}=${encodeURIComponent(JSON.stringify(value))};expires=${expires.toUTCString()};path=/`;
};

const getCookie = (name) => {
  const nameEQ = name + "=";
  const ca = document.cookie.split(';');
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) {
      try {
        return JSON.parse(decodeURIComponent(c.substring(nameEQ.length, c.length)));
      } catch (e) {
        return null;
      }
    }
  }
  return null;
};

const deleteCookie = (name) => {
  document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;`;
};

const isCacheValid = (cachedData) => {
  if (!cachedData || !cachedData.timestamp) return false;
  return (Date.now() - cachedData.timestamp) < CACHE_DURATION;
};

// Define default form config function before it's used
const getDefaultFormConfig = () => [
  { name: "title", label: "Title", type: "text", required: true },
  { name: "info", label: "Info", type: "textarea", required: false },
  { name: "description", label: "Description", type: "textarea", required: false },
  { name: "scale", label: "Scale", type: "select", options: ["Small", "Medium", "Large"], required: true },
  { name: "urgency", label: "Urgency", type: "select", options: ["Low", "Medium", "High", "Immediate"], required: true },
];

export function UseTypeProvider({ children, token }) {
  const [activeModules, setActiveModules] = useState(["EMS", "Fire", "Police", "Medical"]); // List of active modules for the company with defaults
  const [selectedModule, setSelectedModule] = useState(
    localStorage.getItem("selectedModule") || "EMS"
  ); // Currently selected module
  const [formConfig, setFormConfig] = useState(() => {
    // Initialize with default form config
    return getDefaultFormConfig();
  }); // Company-specific form configuration
  const [locations, setLocations] = useState([]); // Company-specific locations

  const baseUrl = process.env.REACT_APP_BASE_URL;

  // Fetch company settings on component mount or when dependencies change
  useEffect(() => {
    const fetchCompanySettings = async () => {
      try {
        // Check if we have valid cached data first
        const cachedSettings = getCookie(COMPANY_SETTINGS_COOKIE);
        if (isCacheValid(cachedSettings)) {
          console.log("Using cached company settings");
          setActiveModules(cachedSettings.data.active_modules || ["EMS", "Fire", "Police", "Medical"]);
          // Form config will be set by separate useEffect for selectedModule
          // Load locations from localStorage and other sources even when using cache
          const localLocations = JSON.parse(localStorage.getItem('companyLocations') || '[]');
          setLocations([...cachedSettings.data.locations || [], ...localLocations]);
          return; // Exit early if we have valid cached data
        }

        // Check localStorage fallback
        const localLocations = localStorage.getItem('companyLocations');
        const localActiveModules = localStorage.getItem('activeModules');

        if (localLocations || localActiveModules) {
          console.log("Using localStorage fallback data");

          if (localLocations) {
            try {
              setLocations(JSON.parse(localLocations));
            } catch (e) {
              console.error('Error parsing localStorage locations:', e);
              setLocations([]);
            }
          }

          if (localActiveModules) {
            try {
              const modules = JSON.parse(localActiveModules);
              setActiveModules(modules);
              console.log('Loaded active modules from localStorage:', modules);
            } catch (e) {
              console.error('Error parsing localStorage active modules:', e);
              setActiveModules(["EMS", "Fire", "Police", "Medical"]);
            }
          }
        }

        console.log("Fetching fresh company settings from API");
        const response = await fetch(
          `${baseUrl}/company-settings`,
          {
            headers: { Authorization: `Bearer ${token}` },
          }
        );
        let settings = {};
        if (!response.ok) {
          console.log("Company settings not found, using defaults");
          // Keep default activeModules, don't throw error
        } else {
          settings = await response.json();

          // Cache the settings with timestamp
          const cacheData = {
            data: settings,
            timestamp: Date.now()
          };
          setCookie(COMPANY_SETTINGS_COOKIE, cacheData, 5); // Cache for 5 minutes

          const activeModulesFromDB = settings.active_modules || ["EMS", "Fire", "Police", "Medical"];
          setActiveModules(activeModulesFromDB);
          // Update localStorage with database data
          localStorage.setItem('activeModules', JSON.stringify(activeModulesFromDB));
          console.log('Active modules loaded from database and saved to localStorage:', activeModulesFromDB);
        }

        // Store form configs for later use when module changes
        window.cachedFormConfigs = settings.form_configs || {};
        // Fetch locations from the same sources as EventForm for consistency
        try {
          const [templatesRes, commonLocationsRes] = await Promise.all([
            fetch(`${baseUrl}/templates`, {
              headers: { Authorization: `Bearer ${token}` },
            }),
            fetch(`${baseUrl}/common-locations`, {
              headers: { Authorization: `Bearer ${token}` },
            }),
          ]);

          let allLocations = [];

          // Get locations from templates
          if (templatesRes.ok) {
            const templatesData = await templatesRes.json();
            const templateLocations = templatesData
              .flatMap((t) => t.config?.included_report_to_locations || [])
              .filter((loc) => loc.commonName && loc.address)
              .map((loc) => ({
                commonName: loc.commonName,
                address: loc.address,
                city: loc.city || "",
                state: loc.state || "",
                zip: loc.zip || "",
              }));
            allLocations = [...allLocations, ...templateLocations];
          }

          // Get common locations
          if (commonLocationsRes.ok) {
            const commonLocationsData = await commonLocationsRes.json();
            const commonLocations = commonLocationsData.map(loc => ({
              commonName: loc.name,
              address: loc.address,
              city: loc.city || "",
              state: loc.state || "",
              zip: loc.zip || ""
            }));
            allLocations = [...allLocations, ...commonLocations];
          }

          // Add localStorage locations
          const localLocations = JSON.parse(localStorage.getItem('companyLocations') || '[]');
          allLocations = [...allLocations, ...localLocations];

          // Deduplicate locations
          const uniqueLocations = allLocations.filter((loc, index, self) =>
            index === self.findIndex(l => l.commonName === loc.commonName && l.address === loc.address)
          );

          setLocations(uniqueLocations);
        } catch (locError) {
          console.error("Error fetching locations:", locError);
          // Fallback to localStorage
          const localLocations = JSON.parse(localStorage.getItem('companyLocations') || '[]');
          setLocations(localLocations);
        }
      } catch (err) {
        console.error("Error fetching company settings:", err);
        // Ensure we always have default values even if there's an error
        setActiveModules(["EMS", "Fire", "Police", "Medical"]);
        setFormConfig(getDefaultFormConfig());
        setLocations([]);
      }
    };

    if (token) {
      fetchCompanySettings();
    }
  }, [token, baseUrl]);

  // Handle form config when selectedModule changes
  useEffect(() => {
    const getDefaultFormConfig = () => [
      { name: "title", label: "Title", type: "text", required: true },
      { name: "info", label: "Info", type: "textarea", required: false },
      {
        name: "description",
        label: "Description",
        type: "textarea",
        required: false,
      },
      {
        name: "scale",
        label: "Scale",
        type: "select",
        options: ["Small", "Medium", "Large"],
        required: true,
      },
      {
        name: "urgency",
        label: "Urgency",
        type: "select",
        options: ["Low", "Medium", "High", "Immediate"],
        required: true,
      },
      {
        name: "location.commonName",
        label: "Location Common Name",
        type: "text",
        required: false,
        nested: true,
      },
      {
        name: "location.address",
        label: "Location Address",
        type: "autocomplete",
        required: false,
        nested: true,
      },
      {
        name: "location.city",
        label: "Location City",
        type: "text",
        required: false,
        nested: true,
      },
      {
        name: "location.state",
        label: "Location State",
        type: "text",
        required: false,
        nested: true,
      },
      {
        name: "location.zip",
        label: "Location Zip",
        type: "text",
        required: false,
        nested: true,
      },
    ];

    // Check localStorage first
    const localFormConfig = localStorage.getItem(`formConfig_${selectedModule}`);
    if (localFormConfig) {
      try {
        setFormConfig(JSON.parse(localFormConfig));
        return;
      } catch (e) {
        console.error('Error parsing localStorage form config:', e);
      }
    }

    // Check cached form configs
    if (window.cachedFormConfigs && window.cachedFormConfigs[selectedModule]) {
      setFormConfig(window.cachedFormConfigs[selectedModule]);
      return;
    }

    // Use default config
    setFormConfig(getDefaultFormConfig());
  }, [selectedModule]);

  // Update selected module and fetch its config
  const updateSelectedModule = useCallback((module) => {
    console.log('Updating selected module to:', module);
    setSelectedModule(module);
    localStorage.setItem("selectedModule", module);

    // Update form config for the new module
    const fetchModuleConfig = async () => {
      try {
        const cachedSettings = getCookie(COMPANY_SETTINGS_COOKIE);
        if (isCacheValid(cachedSettings)) {
          const moduleConfig = cachedSettings.data.form_configs?.[module] || getDefaultFormConfig();
          console.log('Setting form config for module', module, ':', moduleConfig);
          setFormConfig(moduleConfig);
        } else {
          // Fetch fresh data if cache is invalid
          const response = await fetch(`${baseUrl}/company-settings`, {
            headers: { Authorization: `Bearer ${token}` },
          });

          if (response.ok) {
            const settings = await response.json();
            const moduleConfig = settings.form_configs?.[module] || getDefaultFormConfig();
            console.log('Setting form config for module', module, ':', moduleConfig);
            setFormConfig(moduleConfig);
          } else {
            console.log('Using default form config for module:', module);
            setFormConfig(getDefaultFormConfig());
          }
        }
      } catch (err) {
        console.error('Error fetching module config:', err);
        setFormConfig(getDefaultFormConfig());
      }
    };

    fetchModuleConfig();
  }, [baseUrl, token]);

  // Update form configuration
  const updateFormConfig = useCallback(async (newConfig, saveToBackend = true) => {
    try {
      // Always update local state first
      setFormConfig(newConfig);

      // Save to localStorage as fallback
      const localKey = `formConfig_${selectedModule}`;
      localStorage.setItem(localKey, JSON.stringify(newConfig));
      console.log('Form config saved to localStorage for module:', selectedModule);

      // Only save to backend if requested
      if (!saveToBackend) {
        console.log('Skipping backend save (saveToBackend = false)');
        return;
      }

      // Try to update backend
      const response = await fetch(
        `${baseUrl}/company-settings/form-config`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            module: selectedModule,
            formConfig: newConfig,
          }),
        }
      );

      if (response.ok) {
        // Invalidate cache when settings are updated
        deleteCookie(COMPANY_SETTINGS_COOKIE);
        console.log("Form config updated successfully on backend");
      } else {
        console.log("Backend update failed, using localStorage fallback");
      }
    } catch (err) {
      console.error("Error updating form config:", err);
      console.log("Using localStorage fallback for form config");

      // Ensure local state is still updated even if backend fails
      setFormConfig(newConfig);
      const localKey = `formConfig_${selectedModule}`;
      localStorage.setItem(localKey, JSON.stringify(newConfig));
    }
  }, [baseUrl, selectedModule, token]);

  // Update active modules
  const updateActiveModules = useCallback(async (newModules) => {
    try {
      // Always update local state first
      setActiveModules(newModules);

      // Save to localStorage as fallback
      localStorage.setItem('activeModules', JSON.stringify(newModules));
      console.log('Active modules saved to localStorage');

      // Try to update backend
      const response = await fetch(
        `${baseUrl}/company-settings/active-modules`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ activeModules: newModules }),
        }
      );

      if (response.ok) {
        // Invalidate cache when settings are updated
        deleteCookie(COMPANY_SETTINGS_COOKIE);
        console.log("Active modules updated successfully on backend");
      } else {
        console.log("Backend update failed, using localStorage fallback for active modules");
      }
    } catch (err) {
      console.error("Error updating active modules:", err);
      console.log("Using localStorage fallback for active modules");

      // Ensure local state is still updated even if backend fails
      setActiveModules(newModules);
      localStorage.setItem('activeModules', JSON.stringify(newModules));
    }
  }, [baseUrl, token]);

  // Update locations
  const updateLocations = useCallback(async (newLocations) => {
    try {
      // Always update local state first
      setLocations(newLocations);

      // Save to localStorage as fallback
      localStorage.setItem('companyLocations', JSON.stringify(newLocations));
      console.log('Locations saved to localStorage');

      // Try to update backend
      const response = await fetch(
        `${baseUrl}/user/main-locations`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ locations: newLocations }),
        }
      );

      if (response.ok) {
        // Invalidate cache when settings are updated
        deleteCookie(COMPANY_SETTINGS_COOKIE);
        console.log("Locations updated successfully on backend");
      } else {
        console.log("Backend update failed, using localStorage fallback");
      }
    } catch (err) {
      console.error("Error updating user main locations:", err);
      console.log("Using localStorage fallback for locations");

      // Ensure local state is still updated even if backend fails
      setLocations(newLocations);
      localStorage.setItem('companyLocations', JSON.stringify(newLocations));
    }
  }, [baseUrl, token]);

  return (
    <UseTypeContext.Provider
      value={{
        activeModules,
        selectedModule,
        updateSelectedModule,
        formConfig,
        updateFormConfig,
        locations,
        updateLocations,
        updateActiveModules,
      }}
    >
      {children}
    </UseTypeContext.Provider>
  );
}

export function useUseType() {
  return useContext(UseTypeContext);
}
