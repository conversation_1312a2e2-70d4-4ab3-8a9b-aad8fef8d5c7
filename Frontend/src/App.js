import React, { useState, useEffect, useCallback } from "react";
import { Routes, Route, Navigate, Link, useNavigate,useLocation } from "react-router-dom";
import { useJsApiLoader } from "@react-google-maps/api";
import { jwtDecode } from "jwt-decode";
import "./styles.css";
import Login from "./Login";
import Signup from "./signup";
import ResetPassword from "./ResetPassword";
import ResetVerification from "./ResetVerification";
import Home from "./Home";
import Events from "./Events";
import EventForm from "./EventForm";
import Templates from "./Templates";
import Dashboard from "./Dashboard";
import DashboardHome from "./DashboardHome";
import UserManagement from "./UserManagement";
import MapPopup from "./MapPopup";
import Reporting from "./Reporting";
import Settings from "./Settings";
import ChecklistStatus from "./ChecklistStatus";
import { UseTypeProvider } from "./context/UseTypeContext";
import { FontSizeProvider } from "./context/FontSizeContext";
import config from './config';
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { NavLink } from "react-router-dom";


const libraries = ["places"];
const { baseUrl } = config;

function ProtectedRoute({ children, authenticated, role = "", requiredRoles = [] }) {
  const location = useLocation();
  
  // Skip auth check for public routes
  const isPublicRoute = location.pathname === '/' || 
                       location.pathname.includes('/reset-password') || 
                       location.pathname.includes('/reset-verification') ||
                       location.pathname === '/signup';
  
  if (isPublicRoute) return children;
  
  // Check authentication
  if (!authenticated) return <Navigate to="/login" state={{ from: location }} />;
  
  // Check role if required
  if (requiredRoles.length > 0 && !requiredRoles.includes(role)) {
    return <Navigate to="/" />;
  }
  
  return children;
}

function App() {
  const [token, setToken] = useState(localStorage.getItem("token") || "");
  const [role, setRole] = useState(localStorage.getItem("role") || "");
  const navigate = useNavigate();
  const { isLoaded: mapsLoaded } = useJsApiLoader({
    googleMapsApiKey: "AIzaSyBigR0OaTpsLIIFuUJphgtMgWB7PMDDm7k",
    libraries,
  });
  
  useEffect(() => {
    const handleLogout = () => {
      setToken("");
      setRole("");
      localStorage.removeItem("token");
      localStorage.removeItem("role");
      
      // Don't redirect to login if user is on home page or reset password pages
      const currentPath = window.location.pathname;
      const isPublicRoute = currentPath === '/' || currentPath.includes('/reset-password') || currentPath.includes('/reset-verification') || currentPath === '/signup';
      
      if (!isPublicRoute) {
        navigate("/login");
      }
    };
    
    if (token) {
      try {
        const decoded = jwtDecode(token);
        setRole(decoded.role);
        localStorage.setItem("role", decoded.role);
        localStorage.setItem("token", token);
      } catch (error) {
        console.error("Invalid token:", error);
        handleLogout();
      }
    } else {
      // Only logout if not on home page or reset password routes
      const currentPath = window.location.pathname;
      const isPublicRoute = currentPath === '/' || currentPath.includes('/reset-password') || currentPath.includes('/reset-verification') || currentPath === '/signup';
      
      if (!isPublicRoute) {
        handleLogout();
      }
    }
  }, [token, navigate]);

  const handleLogout = () => {
    setToken("");
    setRole("");
    localStorage.removeItem("token");
    localStorage.removeItem("role");
    navigate("/");
  };

  const isAuthenticated = () => {    
    if (!token) return false;
    try {
      jwtDecode(token);
      return true;
    } catch (error) {
      console.error("Token validation failed:", error);
      handleLogout();
      return false;
    }
  };





  //get user_id


  const getUserId = () => {
    if (!token) return null;
    try {
      const decoded = jwtDecode(token);
      return decoded.id; // Replace 'userId' with the actual key in your token payload
    } catch (error) {
      console.error("Error decoding token:", error);
      return null;
    }
  };
  
  const userId = getUserId();


  const authenticated = isAuthenticated();

  const location = useLocation();
  const hideNavbar = location.pathname.startsWith("/map-popup/");
 

  
  

  return (
    <UseTypeProvider token={token}>
      <FontSizeProvider>
        <div
          style={{ display: "flex", flexDirection: "column", minHeight: "100vh" }}
        >

          {!hideNavbar &&       
            <nav
              style={{
                backgroundColor: "#0d47a1",
                padding: "15px 20px",
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
              }}
            >
            <div
              className="logo"
              style={{ display: "flex", alignItems: "center" }}
            >
              <span
                style={{
                  display: "inline-block",
                  width: "10px",
                  height: "10px",
                  backgroundColor: "#00bcd4",
                  borderRadius: "50%",
                  marginRight: "10px",
                }}
              ></span>
              <Link
                to="/"
                style={{
                  color: "#fff",
                  fontSize: "1.5em",
                  fontWeight: "bold",
                  textDecoration: "none",
                }}
              >
                ALERTCOMM1
              </Link>
            </div>
            <div style={{ display: "flex", gap: "10px", alignItems: "center", flexWrap: 'wrap' }}>
              {/* <Link
                to="/"
                style={{
                  color: "#fff",
                  textDecoration: "none",
                  fontSize: "16px",
                }}
                onMouseEnter={(e) => (e.target.style.color = "#e6f0fa")}
                onMouseLeave={(e) => (e.target.style.color = "#fff")}
              >
                Home
              </Link> */}
              <NavLink
                to="/"
                style={({ isActive }) => ({
                  backgroundColor: isActive ? "#1565c0" : "transparent",
                  color: "#fff",
                  textDecoration: "none",
                  fontSize: "var(--font-size-base)",
                  padding: "5px 10px",
                  borderRadius: "5px",
                  marginRight: '0px'
                })}
              >
                Home
              </NavLink>
              
              {!token && (
              <NavLink
                to="/login"
                style={({ isActive }) => ({
                  backgroundColor: isActive ? "#1565c0" : "transparent",
                  color: "#fff",
                  textDecoration: "none",
                  fontSize: "var(--font-size-base)",
                  padding: "5px 10px",
                  borderRadius: "5px",
                  marginRight: '0px'
                })}
                onMouseEnter={(e) => (e.target.style.color = "#e6f0fa")}
                onMouseLeave={(e) => (e.target.style.color = "#fff")}
              >
                Login
              </NavLink>
              )}

              {(role === "commander" || role === "lead" || role === "staff") && (
                <>
                  {/* <Link
                    to="/events"
                    style={{
                      color: "#fff",
                      textDecoration: "none",
                      fontSize: "16px",
                    }}
                  
                  >
                    Events
                  </Link> */}
                  <NavLink
                    to="/events"
                    style={({ isActive }) => ({
                      backgroundColor: isActive ? "#1565c0" : "transparent",
                      color: "#fff",
                      textDecoration: "none",
                      fontSize: "16px",
                      padding: "5px 10px",
                      borderRadius: "5px",
                      marginRight: '0px'
                    })}
                  >
                    Events
                  </NavLink>
                  <NavLink
                    to="/dashboard"
                    style={({ isActive }) => ({
                      backgroundColor: isActive ? "#1565c0" : "transparent",
                      color: "#fff",
                      textDecoration: "none",
                      fontSize: "16px",
                      padding: "5px 10px",
                      borderRadius: "5px",
                      marginRight: '0px'
                    })}
                  
                  >
                    Dashboard
                  </NavLink>
                </>
              )}
              {role === "commander" && (
                <>
                  <NavLink
                    className="launch-color"
                    to="/launch-event"
                    style={({ isActive }) => ({
                      backgroundColor: isActive ? "#1565c0" : "transparent",
                      color: "#fff",
                      textDecoration: "underline",
                      fontSize: "16px",
                      fontWeight: 900,
                      padding: "5px 10px",
                      borderRadius: "5px",
                      marginRight: "0px",
                    })}
                  >
                    Launch Event
                  </NavLink>
                  <NavLink
                    to="/templates"
                    style={({ isActive }) => ({
                      backgroundColor: isActive ? "#1565c0" : "transparent",
                      color: "#fff",
                      textDecoration: "none",
                      fontSize: "16px",
                      padding: "5px 10px",
                      borderRadius: "5px",
                      marginRight: '0px'
                    })}
                  
                  >
                    Templates
                  </NavLink>
                  <NavLink
                    to="/user-management"
                    style={({ isActive }) => ({
                      backgroundColor: isActive ? "#1565c0" : "transparent",
                      color: "#fff",
                      textDecoration: "none",
                      fontSize: "16px",
                      padding: "5px 10px",
                      borderRadius: "5px",
                      marginRight: '0px'
                    })}
                  
                  >
                    User Management
                  </NavLink>
                  <NavLink
                    to="/reporting"
                    style={({ isActive }) => ({
                      backgroundColor: isActive ? "#1565c0" : "transparent",
                      color: "#fff",
                      textDecoration: "none",
                      fontSize: "16px",
                      padding: "5px 10px",
                      borderRadius: "5px",
                      marginRight: '0px'
                    })}
                  
                  >
                    Reporting
                  </NavLink>
                  <NavLink
                    to="/settings"
                    style={({ isActive }) => ({
                      backgroundColor: isActive ? "#1565c0" : "transparent",
                      color: "#fff",
                      textDecoration: "none",
                      fontSize: "16px",
                      padding: "5px 10px",
                      borderRadius: "5px",
                      marginRight: '0px'
                    })}
                  
                  >
                    Settings
                  </NavLink>
                </>
              )}
              {role === "staff" && <ActiveEventsDropdown token={token} onLogout={handleLogout} />}
              {token && (
                <button
                  onClick={handleLogout}
                  style={{
                    background: "none",
                    border: "none",
                    color: "#fff",
                    fontSize: "16px",
                    cursor: "pointer",
                    textTransform: "uppercase",
                  }}
                  onMouseEnter={(e) => (e.target.style.color = "#e6f0fa")}
                  onMouseLeave={(e) => (e.target.style.color = "#fff")}
                >
                  Logout
                </button>
              )}
            </div>
          </nav>
        }

        <div style={{ flex: "1" }}>
          <Routes>
            <Route path="/" element={<Home token={token} />} />
            <Route path="/login" element={<Login setToken={setToken} token={token} />} />
            <Route path="/signup" element={<Signup setToken={setToken} token={token} />} />
            
            {/* Reset password routes - accessible without authentication */}
            <Route path="/reset-password" element={<ResetPassword token={token} />} />
            <Route path="/reset-verification" element={<ResetVerification token={token} />} />
            <Route path="/reset-password/:token" element={<ResetVerification token={token} />} />
            
            {/* Protected routes */}
            <Route path="/events" element={
              <ProtectedRoute authenticated={authenticated} role={role} requiredRoles={["commander", "lead", "staff"]}>
                <Events token={token} onLogout={handleLogout} />
              </ProtectedRoute>
            } />
            <Route path="/launch-event" element={
              <ProtectedRoute authenticated={authenticated} role={role} requiredRoles={["commander", "lead"]}>
                <EventForm token={token} mapsLoaded={mapsLoaded} />
              </ProtectedRoute>
            } />
            <Route
              path="/templates"
              element={
                authenticated && (role === "commander" || role === "lead") ? (
                  <Templates token={token} mapsLoaded={mapsLoaded} />
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/dashboard"
              element={
                authenticated &&
                (role === "commander" ||
                  role === "lead" ||
                  role === "staff") ? (
                  <DashboardHome token={token} mapsLoaded={mapsLoaded} />
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/dashboard/:eventId"
              element={
                authenticated &&
                (role === "commander" ||
                  role === "lead" ||
                  role === "staff") ? (
                  <Dashboard token={token} mapsLoaded={mapsLoaded} />
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/user-management"
              element={
                authenticated && role === "commander" ? (
                  <UserManagement
                    token={token}
                    mapsLoaded={mapsLoaded}
                    role={role}
                  />
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/map-popup/:eventId"
              element={
                authenticated &&
                (role === "commander" ||
                  role === "lead" ||
                  role === "staff") ? (
                  <MapPopup
                    token={token}
                    eventId={window.location.pathname.split("/")[2]}
                  />
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/reporting"
              element={
                authenticated && role === "commander" ? (
                  <Reporting token={token} onLogout={handleLogout} />
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/settings"
              element={
                authenticated && role === "commander" ? (
                  <Settings role={role} token={token}
                   userId={userId} baseUrl={baseUrl} />
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
            <Route
              path="/checklist-status/:eventId"
              element={
                authenticated && (role === "commander" || role === "lead" || role === "staff") ? (
                  <ChecklistStatus token={token} />
                ) : (
                  <Navigate to="/login" />
                )
              }
            />
          </Routes>
        </div>
        <ToastContainer
          position="top-right"
          autoClose={3000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
        />
      </div>
      </FontSizeProvider>
    </UseTypeProvider>
  );
}

function ActiveEventsDropdown({ token, onLogout }) {
  const [activeEvents, setActiveEvents] = useState([]);
  
  useEffect(() => {    

    const fetchActiveEvents = async () => {      
      try {
        const response = await fetch(
          `${baseUrl}/active-events-for-staff`,
          {
            headers: { Authorization: `Bearer ${token}` },
          }
        );
        if (!response.ok) {
          // Handle 403 Forbidden (Invalid token) by logging out
          if (response.status === 403) {
            console.error("Invalid token detected, logging out user");
            if (onLogout) {
              onLogout();
            }
            return;
          }
          throw new Error("Failed to fetch active events");
        }
        const data = await response.json();
        setActiveEvents(data);
      } catch (err) {
        toast.error(`Error fetching active events: ${err.message}`, {
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
        
      }
    };
    fetchActiveEvents();
  }, [token]);

  const handleEventSelect = async (eventId) => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude } = position.coords;
          await fetch(`${baseUrl}/responder/location`, {
            method: "POST",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ latitude, longitude }),
          });
          window.location.href = `${baseUrl}/responder.html?token=${token}&eventId=${eventId}`;
        },
        (err) => {
          console.error("Error getting location:", err.message);
          window.location.href = `${baseUrl}/responder.html?token=${token}&eventId=${eventId}`;
        }
      );
    } else {
      console.error("Geolocation not supported.");
      window.location.href = `${baseUrl}/responder.html?token=${token}&eventId=${eventId}`;
    }
  };

  return (
    <select
      onChange={(e) => e.target.value && handleEventSelect(e.target.value)}
      style={{
        backgroundColor: "#0d47a1",
        color: "#fff",
        border: "1px solid #fff",
        borderRadius: "5px",
        padding: "5px",
        fontSize: "16px",
        cursor: "pointer",
      }}
    >
      <option value="">Select Active Event</option>
      {activeEvents.map((event) => (
        <option key={event.id} value={event.id}>
          {event.title}
        </option>
      ))}
    </select>
  );
}

export default App;


