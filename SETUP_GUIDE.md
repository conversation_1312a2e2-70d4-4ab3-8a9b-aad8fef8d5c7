# AlertComm Emergency Response System - Setup Guide

## 🚀 Quick Start

This guide will help you set up the complete AlertComm emergency response system with all 27 database tables and test data.

## 📋 Prerequisites

- **Node.js** (v14 or higher)
- **PostgreSQL** (v12 or higher)
- **Yarn** package manager
- **Git**

## 🗄️ Database Setup

### 1. Create PostgreSQL Database

```bash
# Connect to PostgreSQL
psql -U postgres

# Create database
CREATE DATABASE alertcomm1;

# Exit psql
\q
```

### 2. Run Database Setup Script

```bash
# Navigate to Admin folder
cd Admin

# Run the complete database setup (creates all 27 tables with test data)
psql -U postgres -d alertcomm1 -f complete_database_setup.sql
```

This single SQL file will:
- Create all 27 tables
- Set up proper relationships and indexes
- Insert comprehensive test data
- Configure all modules and settings

## 🔧 Backend Setup

### 1. Install Dependencies

```bash
cd Admin
npm install
# or
yarn install
```

### 2. Environment Configuration

Create a `.env` file in the `Admin` folder:

```env
# Database Configuration
DB_HOST=localhost
DB_USER=postgres
DB_PASSWORD=your_postgres_password
DB_NAME=alertcomm1
DB_PORT=5432

# JWT Secret
JWT_SECRET=your_super_secret_jwt_key_here

# Email Configuration (Optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# Twilio Configuration (Optional)
TWILIO_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE=+1234567890

# Google Maps API (Optional)
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# OpenAI API (Optional - for AI features)
OPENAI_API_KEY=your_openai_api_key

# Server Configuration
PORT=3000
API_BASE_URL=http://localhost:3000
FRONTEND_BASE_URL=http://localhost:3001
```

### 3. Start Backend Server

```bash
cd Admin
npm start
# or
yarn start
```

The backend will run on `http://localhost:3000`

## 🎨 Frontend Setup

### 1. Install Dependencies

```bash
cd Frontend
yarn install
```

### 2. Start Frontend Development Server

```bash
# For macOS/Linux
yarn start

# For Windows
yarn start:windows
```

The frontend will run on `http://localhost:3001`

## 🔐 Login Credentials

### Test User Accounts

| Role | Username | Password | Description |
|------|----------|----------|-------------|
| **Admin** | `admin` | `admin123` | System administrator |
| **Commander** | `commander1` | `password123` | Chief commander |
| **Paramedic** | `medic1` | `password123` | Paramedic staff |
| **Paramedic** | `medic2` | `password123` | Paramedic staff |
| **EMT** | `emt1` | `password123` | EMT staff |
| **Driver** | `driver1` | `password123` | Driver/EMT |
| **Nurse** | `nurse1` | `password123` | Hospital nurse |
| **Firefighter** | `firefighter1` | `password123` | Fire department |
| **Dispatcher** | `dispatcher1` | `password123` | Emergency dispatcher |
| **Supervisor** | `supervisor1` | `password123` | Field supervisor |

### Default Company
- **Company Name**: AlertComm Emergency Services
- **Company Code**: ALERT001
- **Modules Enabled**: EMS, Fire, Hospital, MCI, Transport, Scheduling, Reporting, AI Features, Chat

## 🏗️ System Architecture

### Database Tables (27 Total)

**Core System:**
- `users` - User accounts and profiles
- `companys` - Organization information
- `company_settings` - System configuration
- `roles_config` - Role-based permissions

**Emergency Management:**
- `events` - Emergency incidents
- `tasks` - Response assignments
- `templates` - Event templates
- `notifications` - Communication logs
- `action_items` - Task checklists

**Personnel & Resources:**
- `responders` - Real-time responder status
- `responder_logs` - Location history
- `resources` - Equipment and vehicles
- `shifts` - Shift scheduling
- `shift_assignments` - Staff assignments

**Communication:**
- `chat_messages` - Event-based chat
- `chat_summaries` - AI chat analysis
- `event_logs` - Activity tracking

**AI & Documents:**
- `document_questions` - AI Q&A
- `document_summaries` - Document analysis
- `checklist_templates` - Reusable checklists

**Transport & Vendors:**
- `vendors` - External service providers
- `transport_requests` - Patient transport
- `transport_assignments` - Transport crews

**Utilities:**
- `common_locations` - Predefined locations
- `user_main_locations` - User locations
- `short_urls` - Mobile URL shortening
- `modules` - Feature management

## 🌟 Key Features

### ✅ Multi-Module Support
- **EMS** - Emergency Medical Services
- **Fire** - Fire Department Operations
- **Hospital** - Hospital Emergency Management
- **MCI** - Mass Casualty Incidents
- **Transport** - Patient Transport Coordination

### ✅ Real-Time Features
- Live chat for events
- Real-time location tracking
- Socket.io communication
- Live dashboard updates

### ✅ AI Integration
- Document analysis and Q&A
- Chat summaries
- Action item generation
- Intelligent recommendations

### ✅ Mobile Responsive
- Short URL generation
- Mobile-optimized interface
- Touch-friendly controls

## 🔧 Development

### Backend API Endpoints
- `http://localhost:3000/api/` - Main API
- `http://localhost:3000/auth/` - Authentication
- `http://localhost:3000/events/` - Event management
- `http://localhost:3000/responders/` - Responder tracking

### Frontend Routes
- `/` - Dashboard
- `/events` - Event management
- `/responders` - Responder tracking
- `/settings` - System configuration
- `/chat` - Communication center

## 🐛 Troubleshooting

### Common Issues

**Frontend won't start:**
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
yarn install
yarn start
```

**Database connection issues:**
- Check PostgreSQL is running
- Verify credentials in `.env`
- Ensure database `alertcomm1` exists

**Port conflicts:**
- Backend: Change `PORT` in `.env`
- Frontend: Change in `package.json` start script

## 📞 Support

For issues or questions:
1. Check the logs in terminal
2. Verify all environment variables
3. Ensure all services are running
4. Check database connectivity

## 🎯 Next Steps

1. **Customize** the system for your organization
2. **Configure** external services (Twilio, Google Maps, OpenAI)
3. **Add** your own users and locations
4. **Test** all functionality with real scenarios
5. **Deploy** to production environment

---

**🚨 Emergency Response System Ready! 🚨**

Your AlertComm system is now fully configured with all 27 database tables, comprehensive test data, and ready for emergency response operations!
