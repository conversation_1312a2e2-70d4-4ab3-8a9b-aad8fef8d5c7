// db.js
require('dotenv').config();
const { Pool } = require("pg");

// console.log('🔧 DB.JS Environment Variables:');
// console.log('DB_HOST:', process.env.DB_HOST);
// console.log('DB_USER:', process.env.DB_USER);
// console.log('DB_NAME:', process.env.DB_NAME);
// console.log('DB_PASSWORD:', process.env.DB_PASSWORD ? '***HIDDEN***' : 'NOT SET');
// console.log('DB_PORT:', process.env.DB_PORT);

const pool = new Pool({
  user: process.env.DB_USER || "postgres",
  host: process.env.DB_HOST || "localhost",
  database: process.env.DB_NAME || "alertcom",
  password: process.env.DB_PASSWORD || "password",
  port: parseInt(process.env.DB_PORT) || 5432,
  ssl: false,
  connectionTimeoutMillis: 10000,
});

// Test the connection
pool.connect((err) => {
  if (err) {
    console.error("Database connection error:", err.stack);
  } else {
    console.log("Connected to PostgreSQL database");
  }
});

const getCompanySettings = async (companyId) => {
  const result = await pool.query(
    "SELECT * FROM company_settings WHERE company_id = $1",
    [companyId]
  );
  return (
    result.rows[0] || {
      active_modules: ["EMS"],
      form_configs: {},
      locations: [],
    }
  );
};

const updateFormConfig = async (companyId, module, formConfig) => {
  await pool.query(
    "INSERT INTO company_settings (company_id, form_configs) VALUES ($1, $2) ON CONFLICT (company_id) DO UPDATE SET form_configs = jsonb_set(company_settings.form_configs, $3, $4)",
    [
      companyId,
      { [module]: formConfig },
      `{${module}}`,
      JSON.stringify(formConfig),
    ]
  );
};

const updateLocations = async (companyId, locations) => {
  await pool.query(
    "INSERT INTO company_settings (company_id, locations) VALUES ($1, $2) ON CONFLICT (company_id) DO UPDATE SET locations = $2",
    [companyId, JSON.stringify(locations)]
  );
};

const createEvent = async (eventData) => {
  const result = await pool.query(
    "INSERT INTO events (company_id, module, title, info, description, scale, urgency, location, included_report_to_locations, assigned_ids, notify_all_if_unassigned, custom_fields, event_type, location_update_interval, notification_channels, event_documents, created_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17) RETURNING *",
    [
      1, // Replace with actual company_id from token
      eventData.module || "mci", // Default to 'mci' if not provided
      eventData.title,
      eventData.info,
      eventData.description,
      eventData.scale,
      eventData.urgency,
      JSON.stringify(eventData.location), // Stringify JSONB field
      JSON.stringify(eventData.included_report_to_locations), // Stringify JSONB field
      eventData.assignedIds,
      eventData.notifyAllIfUnassigned,
      JSON.stringify(eventData.customFields || {}), // Stringify JSONB field
      eventData.event_type || "response", // New field
      eventData.location_update_interval || 60, // New field
      JSON.stringify(eventData.notification_channels || ["web_app"]), // New field
      JSON.stringify(eventData.event_documents || []), // New field
      new Date(),
    ]
  );
  return result.rows[0];
};

const getRespondersByIds = async (ids) => {
  if (!ids || ids.length === 0) return [];
  const result = await pool.query(
    "SELECT * FROM responders WHERE id = ANY($1)",
    [ids]
  );
  return result.rows;
};

const getAllResponders = async () => {
  const result = await pool.query("SELECT * FROM responders");
  return result.rows;
};

const createNotification = async (notificationData) => {
  const result = await pool.query(
    "INSERT INTO notifications (responder_id, event_id, message, type, created_at) VALUES ($1, $2, $3, $4, $5) RETURNING *",
    [
      notificationData.responder_id,
      notificationData.event_id,
      notificationData.message,
      notificationData.type,
      notificationData.created_at,
    ]
  );
  return result.rows[0];
};

const getResponders = async () => {
  const result = await pool.query("SELECT * FROM responders");
  return result.rows;
};

const getTemplates = async () => {
  const result = await pool.query("SELECT * FROM templates");
  return result.rows;
};

const createTemplate = async (templateData) => {
  const result = await pool.query(
    "INSERT INTO templates (name, description, config) VALUES ($1, $2, $3) RETURNING *",
    [templateData.name, templateData.description, templateData.config]
  );
  return result.rows[0];
};

const getActiveEvents = async () => {
  const result = await pool.query("SELECT * FROM events");
  return result.rows;
};

const getActiveEventsForStaff = async () => {
  const result = await pool.query("SELECT * FROM events");
  return result.rows;
};

const updateResponderLocation = async (responderId, location) => {
  console.log(`Updated location for responder ${responderId}:`, location);
};

module.exports = {
  pool,
  getCompanySettings,
  updateFormConfig,
  updateLocations,
  createEvent,
  getRespondersByIds,
  getAllResponders,
  createNotification,
  getResponders,
  getTemplates,
  createTemplate,
  getActiveEvents,
  getActiveEventsForStaff,
  updateResponderLocation,
};
