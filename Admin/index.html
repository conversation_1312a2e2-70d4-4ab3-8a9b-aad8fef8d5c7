<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AlertComm1 - Emergency Response System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a73e8 0%, #0d6efd 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 900px;
            min-height: 600px;
            display: grid;
            grid-template-columns: 1fr 1fr;
        }

        .welcome-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            color: white;
            text-align: center;
        }

        .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
        }

        .logo-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 24px;
        }

        .logo-text {
            font-size: 2rem;
            font-weight: 700;
            color: white;
        }

        .welcome-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .welcome-desc {
            font-size: 1rem;
            opacity: 0.9;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .features-list {
            list-style: none;
            text-align: left;
        }

        .features-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
        }

        .features-list li::before {
            content: '✓';
            color: #4CAF50;
            font-weight: bold;
            margin-right: 10px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .form-section {
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .form-toggle {
            display: flex;
            margin-bottom: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 5px;
        }

        .toggle-btn {
            flex: 1;
            padding: 12px;
            text-align: center;
            border: none;
            background: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .toggle-btn.active {
            background: #1a73e8;
            color: white;
            box-shadow: 0 2px 10px rgba(26, 115, 232, 0.3);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #1a73e8;
        }

        .submit-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #1a73e8, #0d6efd);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(26, 115, 232, 0.4);
        }

        .form-footer {
            text-align: center;
            margin-top: 20px;
            color: #666;
        }

        .form-footer a {
            color: #1a73e8;
            text-decoration: none;
            font-weight: 600;
        }

        .error-message {
            background: #fee;
            color: #c33;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .success-message {
            background: #efe;
            color: #393;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                margin: 20px;
                max-width: none;
            }

            .welcome-section {
                padding: 40px 30px;
            }

            .form-section {
                padding: 40px 30px;
            }

            .logo-text {
                font-size: 1.5rem;
            }

            .welcome-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="welcome-section">
            <div class="logo">
                <div class="logo-icon">🚨</div>
                <h1 class="logo-text">AlertComm1</h1>
            </div>
            
            <h2 class="welcome-title">Emergency Response Communication System</h2>
            <p class="welcome-desc">
                Connecting first responders when it matters most. Fast, reliable, and secure emergency communication.
            </p>
            
            <ul class="features-list">
                <li>Real-time emergency alerts</li>
                <li>GPS location tracking</li>
                <li>Multi-channel notifications</li>
                <li>AI-powered insights</li>
                <li>Secure communications</li>
                <li>Mobile optimized</li>
            </ul>
        </div>

        <div class="form-section">
            <div class="form-toggle">
                <button class="toggle-btn active" onclick="showLogin()">Login</button>
                <button class="toggle-btn" onclick="showSignup()">Sign Up</button>
            </div>

            <div id="error-message" class="error-message"></div>
            <div id="success-message" class="success-message"></div>

            <!-- Login Form -->
            <form id="login-form" onsubmit="handleLogin(event)">
                <div class="form-group">
                    <label for="login-username">Username</label>
                    <input type="text" id="login-username" name="username" required>
                </div>
                
                <div class="form-group">
                    <label for="login-password">Password</label>
                    <input type="password" id="login-password" name="password" required>
                </div>
                
                <button type="submit" class="submit-btn">Login</button>
                
                <div class="form-footer">
                    <a href="#" onclick="showForgotPassword()">Forgot Password?</a>
                </div>
            </form>

            <!-- Signup Form -->
            <form id="signup-form" style="display: none;" onsubmit="handleSignup(event)">
                <div class="form-group">
                    <label for="signup-username">Username</label>
                    <input type="text" id="signup-username" name="username" required>
                </div>
                
                <div class="form-group">
                    <label for="signup-email">Email</label>
                    <input type="email" id="signup-email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="signup-password">Password</label>
                    <input type="password" id="signup-password" name="password" required>
                </div>
                
                <div class="form-group">
                    <label for="signup-location">Main Location</label>
                    <input type="text" id="signup-location" name="main_location" placeholder="e.g., Station 5" required>
                </div>
                
                <div class="form-group">
                    <label for="signup-phone">Phone (optional)</label>
                    <input type="tel" id="signup-phone" name="phone" placeholder="+**********">
                </div>
                
                <button type="submit" class="submit-btn">Create Account</button>
                
                <div class="form-footer">
                    Already have an account? <a href="#" onclick="showLogin()">Login here</a>
                </div>
            </form>

            <!-- Forgot Password Form -->
            <form id="forgot-form" style="display: none;" onsubmit="handleForgotPassword(event)">
                <div class="form-group">
                    <label for="forgot-email">Email Address</label>
                    <input type="email" id="forgot-email" name="email" required>
                </div>
                
                <button type="submit" class="submit-btn">Reset Password</button>
                
                <div class="form-footer">
                    <a href="#" onclick="showLogin()">Back to Login</a>
                </div>
            </form>
        </div>
    </div>

    <script>
        function showLogin() {
            document.getElementById('login-form').style.display = 'block';
            document.getElementById('signup-form').style.display = 'none';
            document.getElementById('forgot-form').style.display = 'none';
            
            document.querySelectorAll('.toggle-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.toggle-btn')[0].classList.add('active');
            clearMessages();
        }

        function showSignup() {
            document.getElementById('login-form').style.display = 'none';
            document.getElementById('signup-form').style.display = 'block';
            document.getElementById('forgot-form').style.display = 'none';
            
            document.querySelectorAll('.toggle-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.toggle-btn')[1].classList.add('active');
            clearMessages();
        }

        function showForgotPassword() {
            document.getElementById('login-form').style.display = 'none';
            document.getElementById('signup-form').style.display = 'none';
            document.getElementById('forgot-form').style.display = 'block';
            
            document.querySelectorAll('.toggle-btn').forEach(btn => btn.classList.remove('active'));
            clearMessages();
        }

        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            document.getElementById('success-message').style.display = 'none';
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('success-message');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            document.getElementById('error-message').style.display = 'none';
        }

        function clearMessages() {
            document.getElementById('error-message').style.display = 'none';
            document.getElementById('success-message').style.display = 'none';
        }

        async function handleLogin(event) {
            event.preventDefault();
            clearMessages();
            
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData);
            
            try {
                const response = await fetch('/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    localStorage.setItem('authToken', result.token);
                    showSuccess('Login successful! Redirecting...');
                    setTimeout(() => {
                        window.location.href = '/dashboard.html';
                    }, 1000);
                } else {
                    showError(result.error || 'Login failed');
                }
            } catch (error) {
                showError('Network error. Please try again.');
            }
        }

        async function handleSignup(event) {
            event.preventDefault();
            clearMessages();
            
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData);
            
            try {
                const response = await fetch('/signup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showSuccess('Account created successfully! You can now login.');
                    setTimeout(() => {
                        showLogin();
                    }, 2000);
                } else {
                    showError(result.error || 'Signup failed');
                }
            } catch (error) {
                showError('Network error. Please try again.');
            }
        }

        async function handleForgotPassword(event) {
            event.preventDefault();
            clearMessages();
            
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData);
            
            try {
                const response = await fetch('/forgot-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showSuccess('Password reset instructions sent to your email.');
                } else {
                    showError(result.error || 'Password reset failed');
                }
            } catch (error) {
                showError('Network error. Please try again.');
            }
        }

        // Auto-focus first input and check authentication
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is already logged in
            const token = localStorage.getItem('authToken');
            if (token) {
                // Verify token is still valid
                fetch(`${window.location.origin}/dashboard`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                })
                .then(response => {
                    if (response.ok) {
                        // Redirect to dashboard if already logged in
                        window.location.href = '/dashboard.html';
                        return;
                    }
                })
                .catch(() => {
                    // Token is invalid, remove it
                    localStorage.removeItem('authToken');
                });
            }

            document.getElementById('login-username').focus();
        });
    </script>
</body>
</html>
