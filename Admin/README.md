# AlertComm1 - Emergency Response System

A comprehensive emergency response communication system with real-time alerts, location tracking, and AI-powered insights.

## Quick Start

### 1. Database Setup
```bash
# Create PostgreSQL database
createdb alertcomm1

# Run setup script
psql -d alertcomm1 -f setup-db.sql
```

### 2. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your credentials
```

### 3. Install Dependencies & Start
```bash
# Install packages
npm install

# Start development server
npm run dev

# Or start production server
npm start
```

### 4. Access the Application
- **Landing Page**: http://localhost:3000
- **Default Login**: 
  - Username: `admin`
  - Password: `admin123`

## Key Features

- ✅ **Real-time Alerts**: SMS, voice calls, and mobile notifications
- ✅ **GPS Tracking**: Location tracking for responders
- ✅ **AI Integration**: Chat summarization and action recommendations
- ✅ **Role-based Access**: Commander, Lead, Staff roles
- ✅ **Multi-channel**: Email, SMS, voice notifications
- ✅ **Mobile Optimized**: Responsive design for all devices
- ✅ **Secure**: JWT authentication and encrypted communications

## API Endpoints

### Authentication
- `POST /login` - User login
- `POST /signup` - User registration
- `POST /forgot-password` - Password reset
- `POST /verify-otp` - OTP verification

### User Management
- `GET /users` - Get all users (Commander only)
- `POST /add-user` - Add new user (Commander only)
- `PUT /edit-user/:id` - Edit user (Commander only)
- `DELETE /delete-user/:id` - Delete user (Commander/Lead only)

### Event Management
- `GET /all-events` - Get all events
- `GET /all-active-events` - Get active events
- `POST /events` - Create new event (Commander/Lead only)
- `PUT /event/:id` - Update event status (Commander/Lead only)
- `DELETE /events/:id` - Delete event (Commander/Lead only)

### AI Features
- `POST /ai/summarize-chat` - Generate chat summary
- `POST /ai/generate-actions` - Generate role-based actions

### Responder Management
- `GET /responders` - Get responders list
- `POST /responder/location` - Update responder location
- `GET /active-events-for-staff` - Get staff's active events

## Database Tables

- `users` - User accounts and profiles
- `events` - Emergency events
- `tasks` - Assigned tasks for events
- `responders` - Responder location data
- `templates` - Event templates
- `notifications` - System notifications
- `company_settings` - Company configurations

## Environment Variables

```env
DB_HOST=localhost
DB_USER=postgres
DB_PASSWORD=your_password
DB_NAME=alertcomm1
DB_PORT=5432

JWT_SECRET=your_jwt_secret

EMAIL_HOST=smtp.your-provider.com
EMAIL_PORT=587
EMAIL_USER=your_email
EMAIL_PASS=your_password

TWILIO_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
TWILIO_PHONE_NUMBER=+**********

BASE_URL=http://localhost:3000
PORT=3000
```

## Troubleshooting

### Database Connection Issues
1. Ensure PostgreSQL is running
2. Check database credentials in `.env`
3. Verify database exists: `psql -l | grep alertcomm1`

### Login Issues
1. Use default admin credentials: `admin` / `admin123`
2. Check JWT_SECRET is set in `.env`
3. Verify user exists in database

### Module Not Found Errors
```bash
# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

## Development

```bash
# Run in development mode
npm run dev

# Check syntax
node -c server.js

# View logs
tail -f logs/app.log
```

## Production Deployment

1. Set NODE_ENV=production
2. Use SSL certificates
3. Configure reverse proxy (nginx)
4. Set up database backups
5. Monitor logs and performance

## Support

For issues and questions:
- Check the troubleshooting section above
- Review server logs for errors
- Ensure all environment variables are set correctly
