-- AlertComm Emergency Response System - Complete Database Setup
-- This script creates all tables and populates them with comprehensive test data
-- Run this script to set up the entire database from scratch

-- Start transaction to ensure atomicity
BEGIN;

-- Drop existing tables if they exist (for clean setup)
DROP TABLE IF EXISTS document_summaries CASCADE;
DROP TABLE IF EXISTS document_questions CASCADE;
DROP TABLE IF EXISTS action_items CASCADE;
DROP TABLE IF EXISTS checklist_templates CASCADE;
DROP TABLE IF EXISTS short_urls CASCADE;
DROP TABLE IF EXISTS user_main_locations CASCADE;
DROP TABLE IF EXISTS chat_summaries CASCADE;
DROP TABLE IF EXISTS chat_messages CASCADE;
DROP TABLE IF EXISTS transport_assignments CASCADE;
DROP TABLE IF EXISTS transport_requests CASCADE;
DROP TABLE IF EXISTS vendors CASCADE;
DROP TABLE IF EXISTS shift_assignments CASCADE;
DROP TABLE IF EXISTS shifts CASCADE;
DROP TABLE IF EXISTS responder_logs CASCADE;
DROP TABLE IF EXISTS event_logs CASCADE;
DROP TABLE IF EXISTS modules CASCADE;
DROP TABLE IF EXISTS resources CASCADE;
DROP TABLE IF EXISTS notifications CASCADE;
DROP TABLE IF EXISTS tasks CASCADE;
DROP TABLE IF EXISTS responders CASCADE;
DROP TABLE IF EXISTS templates CASCADE;
DROP TABLE IF EXISTS roles_config CASCADE;
DROP TABLE IF EXISTS company_settings CASCADE;
DROP TABLE IF EXISTS events CASCADE;
DROP TABLE IF EXISTS companys CASCADE;
DROP TABLE IF EXISTS common_locations CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Create users table
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    role VARCHAR(20) DEFAULT 'staff',
    main_location VARCHAR(255),
    phone VARCHAR(20),
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    job_role VARCHAR(100),
    home_address TEXT,
    city VARCHAR(50),
    state VARCHAR(50),
    zip VARCHAR(10),
    status INTEGER DEFAULT 1,
    otp_code VARCHAR(10),
    company_id INTEGER,
    reset_token TEXT,
    reset_token_expires TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create companys table (note: keeping original table name for compatibility)
CREATE TABLE companys (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(15) UNIQUE NOT NULL,
    description TEXT,
    code VARCHAR(50) UNIQUE NOT NULL,
    user_id INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create events table
CREATE TABLE events (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    info TEXT,
    scale VARCHAR(50),
    urgency VARCHAR(50),
    location JSONB,
    description TEXT,
    status VARCHAR(50) DEFAULT 'open',
    created_by INTEGER REFERENCES users(id),
    escalation_level INTEGER DEFAULT 0,
    included_report_to_locations JSONB,
    assigned_ids INTEGER[],
    notify_all_if_unassigned BOOLEAN DEFAULT false,
    module VARCHAR(50),
    custom_fields JSONB,
    event_type VARCHAR(20) DEFAULT 'response',
    location_update_interval INTEGER DEFAULT 60,
    notification_channels JSONB DEFAULT '["web_app"]'::jsonb,
    event_documents JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create tasks table
CREATE TABLE tasks (
    id SERIAL PRIMARY KEY,
    event_id INTEGER REFERENCES events(id) ON DELETE CASCADE,
    assigned_to INTEGER REFERENCES users(id) ON DELETE SET NULL,
    status VARCHAR(50) DEFAULT 'pending',
    report_to_location VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create responders table
CREATE TABLE responders (
    id INTEGER PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    status VARCHAR(50) DEFAULT 'available',
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create templates table
CREATE TABLE templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    config JSONB,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create notifications table
CREATE TABLE notifications (
    id SERIAL PRIMARY KEY,
    responder_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    event_id INTEGER REFERENCES events(id) ON DELETE CASCADE,
    message TEXT,
    type VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create company_settings table
CREATE TABLE company_settings (
    id SERIAL PRIMARY KEY,
    company_id INTEGER UNIQUE REFERENCES companys(id) ON DELETE CASCADE,
    active_modules JSONB DEFAULT '["EMS"]',
    form_configs JSONB DEFAULT '{}',
    locations JSONB DEFAULT '[]',
    ai_prompts JSONB DEFAULT '{}',
    ai_preconditions JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create roles_config table
CREATE TABLE roles_config (
    id SERIAL PRIMARY KEY,
    role VARCHAR(50) UNIQUE NOT NULL,
    report_to_locations JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create common_locations table
CREATE TABLE common_locations (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    address TEXT,
    city VARCHAR(50),
    state VARCHAR(50),
    zip VARCHAR(10),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create user_main_locations table
CREATE TABLE user_main_locations (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    common_name VARCHAR(255) NOT NULL,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(50),
    zip VARCHAR(20),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create short_urls table
CREATE TABLE short_urls (
    id SERIAL PRIMARY KEY,
    short_code VARCHAR(10) UNIQUE NOT NULL,
    original_url TEXT NOT NULL,
    click_count INTEGER DEFAULT 0,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create action_items table
CREATE TABLE action_items (
    id SERIAL PRIMARY KEY,
    event_id INTEGER REFERENCES events(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    action_text TEXT NOT NULL,
    is_completed BOOLEAN DEFAULT FALSE,
    user_role VARCHAR(50),
    action_index INTEGER,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(event_id, user_id, action_index)
);

-- Create checklist_templates table
CREATE TABLE checklist_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    template_data JSONB NOT NULL,
    created_by INTEGER REFERENCES users(id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create document_questions table
CREATE TABLE document_questions (
    id SERIAL PRIMARY KEY,
    event_id INTEGER REFERENCES events(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    question TEXT NOT NULL,
    answer TEXT,
    document_context JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create document_summaries table
CREATE TABLE document_summaries (
    id SERIAL PRIMARY KEY,
    event_id INTEGER REFERENCES events(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    document_name VARCHAR(255) NOT NULL,
    summary TEXT NOT NULL,
    checklist_items JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(event_id, user_id, document_name)
);

-- Create resources table
CREATE TABLE resources (
    id SERIAL PRIMARY KEY,
    company_id INTEGER DEFAULT 1,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    UNIQUE(company_id, name)
);

-- Create chat_messages table for event communication
CREATE TABLE chat_messages (
    id SERIAL PRIMARY KEY,
    event_id INTEGER REFERENCES events(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    username VARCHAR(255) NOT NULL,
    text TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create chat_summaries table for AI chat analysis
CREATE TABLE chat_summaries (
    id SERIAL PRIMARY KEY,
    event_id INTEGER REFERENCES events(id) ON DELETE CASCADE,
    summary_text TEXT NOT NULL,
    generated_by INTEGER REFERENCES users(id),
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create event_logs table for activity tracking
CREATE TABLE event_logs (
    id SERIAL PRIMARY KEY,
    event_id INTEGER REFERENCES events(id) ON DELETE CASCADE,
    action VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    details JSONB,
    user_id INTEGER REFERENCES users(id)
);

-- Create modules table for customer module management
CREATE TABLE modules (
    customer_id INTEGER NOT NULL,
    module_name VARCHAR(50) NOT NULL,
    enabled BOOLEAN DEFAULT false,
    PRIMARY KEY (customer_id, module_name)
);

-- Create responder_logs table for location history
CREATE TABLE responder_logs (
    id SERIAL PRIMARY KEY,
    responder_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    status VARCHAR(50),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create shifts table for scheduling
CREATE TABLE shifts (
    id SERIAL PRIMARY KEY,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    location VARCHAR(255) NOT NULL,
    staff_needed INTEGER NOT NULL,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create shift_assignments table
CREATE TABLE shift_assignments (
    id SERIAL PRIMARY KEY,
    shift_id INTEGER REFERENCES shifts(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create vendors table for external service providers
CREATE TABLE vendors (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    transport_modes TEXT[] NOT NULL,
    capabilities TEXT[] NOT NULL,
    base_location VARCHAR(255) NOT NULL,
    availability_status VARCHAR(50) DEFAULT 'available',
    contact_methods TEXT[] NOT NULL,
    api_endpoint VARCHAR(255),
    api_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create transport_requests table
CREATE TABLE transport_requests (
    id SERIAL PRIMARY KEY,
    pickup_location VARCHAR(255) NOT NULL,
    destination VARCHAR(255) NOT NULL,
    vehicle_type VARCHAR(50) NOT NULL,
    urgency VARCHAR(50) NOT NULL,
    event_id INTEGER REFERENCES events(id),
    patient_name VARCHAR(100),
    patient_condition VARCHAR(255),
    special_needs VARCHAR(255),
    vendor_id INTEGER REFERENCES vendors(id),
    status VARCHAR(50) DEFAULT 'requested',
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create transport_assignments table
CREATE TABLE transport_assignments (
    id SERIAL PRIMARY KEY,
    request_id INTEGER REFERENCES transport_requests(id) ON DELETE CASCADE,
    driver_id INTEGER REFERENCES users(id),
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_events_created_by ON events(created_by);
CREATE INDEX idx_events_status ON events(status);
CREATE INDEX idx_events_module ON events(module);
CREATE INDEX idx_tasks_event_id ON tasks(event_id);
CREATE INDEX idx_tasks_assigned_to ON tasks(assigned_to);
CREATE INDEX idx_responders_status ON responders(status);
CREATE INDEX idx_notifications_responder_id ON notifications(responder_id);
CREATE INDEX idx_notifications_event_id ON notifications(event_id);
CREATE INDEX idx_action_items_event_user ON action_items(event_id, user_id);
CREATE INDEX idx_document_questions_event_user ON document_questions(event_id, user_id);
CREATE INDEX idx_document_summaries_event_user ON document_summaries(event_id, user_id);
CREATE INDEX idx_checklist_templates_active ON checklist_templates(is_active);
CREATE INDEX idx_resources_company_id ON resources(company_id);
CREATE INDEX idx_resources_name ON resources(name);
CREATE INDEX idx_short_urls_short_code ON short_urls(short_code);
CREATE INDEX idx_users_company_id ON users(company_id);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_chat_messages_event_id ON chat_messages(event_id);
CREATE INDEX idx_chat_messages_timestamp ON chat_messages(timestamp);
CREATE INDEX idx_chat_summaries_event_id ON chat_summaries(event_id);
CREATE INDEX idx_event_logs_event_id ON event_logs(event_id);
CREATE INDEX idx_event_logs_timestamp ON event_logs(timestamp);
CREATE INDEX idx_responder_logs_responder_id ON responder_logs(responder_id);
CREATE INDEX idx_responder_logs_timestamp ON responder_logs(timestamp);
CREATE INDEX idx_shifts_start_time ON shifts(start_time);
CREATE INDEX idx_shift_assignments_shift_id ON shift_assignments(shift_id);
CREATE INDEX idx_shift_assignments_user_id ON shift_assignments(user_id);
CREATE INDEX idx_transport_requests_event_id ON transport_requests(event_id);
CREATE INDEX idx_transport_requests_status ON transport_requests(status);
CREATE INDEX idx_transport_assignments_request_id ON transport_assignments(request_id);
CREATE INDEX idx_vendors_availability_status ON vendors(availability_status);

-- ========================================
-- INSERT TEST DATA
-- ========================================

-- Insert default company
INSERT INTO companys (id, name, slug, description, code, user_id) VALUES
(1, 'AlertComm Emergency Services', 'alertcomm', 'Primary emergency response organization', 'ALERT001', NULL);

-- Insert test users with various roles
-- Note: All passwords are hashed using bcrypt with 10 rounds
-- Password for ALL users: password123
INSERT INTO users (id, username, password, email, role, main_location, phone, first_name, last_name, job_role, home_address, city, state, zip, status, company_id) VALUES
(1, 'admin', '$2a$10$4hLxIXOE.17zf8gvcGbqceVUUULPpjutJa/RkG2x.D7hMyginZyji', '<EMAIL>', 'commander', 'Headquarters', '******-0001', 'System', 'Administrator', 'System Admin', '100 Admin St', 'Springfield', 'IL', '62701', 1, 1),
(2, 'commander1', '$2a$10$4hLxIXOE.17zf8gvcGbqceVUUULPpjutJa/RkG2x.D7hMyginZyji', '<EMAIL>', 'commander', 'Headquarters', '******-406-0390', 'Thomas', 'Tornstrom', 'Chief Commander', '1126 Sandy Cir', 'La Crescent', 'MN', '55947', 1, 1),
(3, 'medic1', '$2a$10$4hLxIXOE.17zf8gvcGbqceVUUULPpjutJa/RkG2x.D7hMyginZyji', '<EMAIL>', 'staff', 'Station 1', '******-0003', 'John', 'Doe', 'Paramedic', '713 E Grove St', 'Caledonia', 'MN', '55921', 1, 1),
(4, 'medic2', '$2a$10$4hLxIXOE.17zf8gvcGbqceVUUULPpjutJa/RkG2x.D7hMyginZyji', '<EMAIL>', 'staff', 'Station 2', '******-406-0390', 'Lori', 'Johnson', 'Paramedic', '3210 N Kinney Coulee Rd', 'Onalaska', 'WI', '54650', 1, 1),
(5, 'emt1', '$2a$10$4hLxIXOE.17zf8gvcGbqceVUUULPpjutJa/RkG2x.D7hMyginZyji', '<EMAIL>', 'staff', 'Station 1', '+1-507-730-7003', 'Evan', 'Tornstrom', 'EMT', '1126 Sandy Cir', 'La Crescent', 'MN', '55947', 1, 1),
(6, 'driver1', '$2a$10$4hLxIXOE.17zf8gvcGbqceVUUULPpjutJa/RkG2x.D7hMyginZyji', '<EMAIL>', 'staff', 'Station 3', '******-0006', 'Mike', 'Wilson', 'Driver/EMT', '456 Oak Ave', 'Springfield', 'IL', '62702', 1, 1),
(7, 'nurse1', '$2a$10$4hLxIXOE.17zf8gvcGbqceVUUULPpjutJa/RkG2x.D7hMyginZyji', '<EMAIL>', 'staff', 'Hospital Center', '******-0007', 'Sarah', 'Davis', 'Registered Nurse', '789 Pine St', 'Springfield', 'IL', '62703', 1, 1),
(8, 'firefighter1', '$2a$10$4hLxIXOE.17zf8gvcGbqceVUUULPpjutJa/RkG2x.D7hMyginZyji', '<EMAIL>', 'staff', 'Station 1', '******-0008', 'Robert', 'Brown', 'Firefighter', '321 Fire Lane', 'Springfield', 'IL', '62701', 1, 1),
(9, 'dispatcher1', '$2a$10$4hLxIXOE.17zf8gvcGbqceVUUULPpjutJa/RkG2x.D7hMyginZyji', '<EMAIL>', 'dispatcher', 'Headquarters', '******-0009', 'Lisa', 'Garcia', 'Emergency Dispatcher', '654 Dispatch Dr', 'Springfield', 'IL', '62704', 1, 1),
(10, 'supervisor1', '$2a$10$4hLxIXOE.17zf8gvcGbqceVUUULPpjutJa/RkG2x.D7hMyginZyji', '<EMAIL>', 'supervisor', 'Station 2', '******-0010', 'David', 'Martinez', 'Field Supervisor', '987 Supervisor St', 'Springfield', 'IL', '62705', 1, 1);

-- Update company user_id to reference admin user
UPDATE companys SET user_id = 1 WHERE id = 1;

-- Insert common locations
INSERT INTO common_locations (id, name, address, city, state, zip, latitude, longitude) VALUES
(1, 'Station 1', '123 Main St', 'Springfield', 'IL', '62701', 39.7817, -89.6501),
(2, 'Station 2', '456 Oak Ave', 'Springfield', 'IL', '62702', 39.7900, -89.6400),
(3, 'Station 3', '789 Pine St', 'Springfield', 'IL', '62703', 39.7750, -89.6600),
(4, 'Hospital Center', '100 Medical Way', 'Springfield', 'IL', '62704', 39.7850, -89.6550),
(5, 'Police HQ', '200 Safety Blvd', 'Springfield', 'IL', '62705', 39.7800, -89.6450),
(6, 'Headquarters', '300 Command Center Dr', 'Springfield', 'IL', '62706', 39.7820, -89.6520),
(7, 'Airport', '2850 Airport Rd', 'Springfield', 'IL', '62707', 39.8440, -89.6780),
(8, 'Shopping Mall', '1211 Crossing Meadows Dr', 'Springfield', 'IL', '62708', 39.7650, -89.6350);

-- Insert company settings with comprehensive configurations
INSERT INTO company_settings (company_id, active_modules, form_configs, locations, ai_prompts, ai_preconditions) VALUES
(1,
'["EMS", "Fire", "Hospital", "MCI", "Transport"]'::jsonb,
'{
  "EMS": [
    {"name": "title", "type": "text", "label": "Incident Title", "required": true},
    {"name": "info", "type": "textarea", "label": "Incident Information", "required": false},
    {"name": "description", "type": "textarea", "label": "Detailed Description", "required": false},
    {"name": "scale", "type": "select", "label": "Incident Scale", "options": ["Small", "Medium", "Large"], "required": true},
    {"name": "urgency", "type": "select", "label": "Urgency Level", "options": ["Low", "Medium", "High", "Immediate"], "required": true},
    {"name": "location.commonName", "type": "text", "label": "Location Name", "nested": true, "required": false},
    {"name": "location.address", "type": "autocomplete", "label": "Address", "nested": true, "required": false},
    {"name": "location.city", "type": "text", "label": "City", "nested": true, "required": false},
    {"name": "location.state", "type": "text", "label": "State", "nested": true, "required": false},
    {"name": "location.zip", "type": "text", "label": "ZIP Code", "nested": true, "required": false}
  ],
  "Fire": [
    {"name": "title", "type": "text", "label": "Fire Incident Title", "required": true},
    {"name": "info", "type": "textarea", "label": "Fire Incident Info", "required": false},
    {"name": "description", "type": "textarea", "label": "Fire Description", "required": false},
    {"name": "scale", "type": "select", "label": "Fire Scale", "options": ["Small", "Medium", "Large", "Multi-Alarm"], "required": true},
    {"name": "urgency", "type": "select", "label": "Fire Urgency", "options": ["Low", "Medium", "High", "Immediate"], "required": true}
  ],
  "Hospital": [
    {"name": "title", "type": "text", "label": "Hospital Event Title", "required": true},
    {"name": "info", "type": "textarea", "label": "Hospital Event Info", "required": false},
    {"name": "scale", "type": "select", "label": "Event Scale", "options": ["Small", "Medium", "Large"], "required": true},
    {"name": "urgency", "type": "select", "label": "Event Urgency", "options": ["Low", "Medium", "High", "Critical"], "required": true}
  ]
}'::jsonb,
'[
  {"address": "123 Main St, Springfield, IL 62701", "commonName": "Station 1"},
  {"address": "456 Oak Ave, Springfield, IL 62702", "commonName": "Station 2"},
  {"address": "789 Pine St, Springfield, IL 62703", "commonName": "Station 3"},
  {"address": "100 Medical Way, Springfield, IL 62704", "commonName": "Hospital Center"},
  {"address": "300 Command Center Dr, Springfield, IL 62706", "commonName": "Headquarters"}
]'::jsonb,
'{
  "document_summary": "You are an AI assistant helping emergency responders analyze documents. Provide a concise summary of the document focusing on key information relevant to emergency response, including procedures, contacts, resources, and safety guidelines. Format the summary with bullet points for easy reading.",
  "action_generation": "Generate specific, actionable tasks based on the user role and event type. Focus on immediate response actions, safety protocols, and coordination requirements.",
  "checklist_generation": "Create comprehensive checklists for emergency response procedures based on the incident type and user role."
}'::jsonb,
'{
  "document_qa_prompt": "You are an AI assistant helping emergency responders analyze documents. Provide accurate, concise answers based on the uploaded documents. Focus on actionable information.",
  "action_generation_prompt": "You are an AI assistant helping emergency responders. Generate specific, actionable tasks based on the user role and event type.",
  "safety_protocols": "Always prioritize safety in all recommendations. Include appropriate PPE and safety considerations."
}'::jsonb
);

-- Insert roles configuration
INSERT INTO roles_config (role, report_to_locations) VALUES
('commander', '["Headquarters", "Station 1", "Station 2"]'::jsonb),
('supervisor', '["Station 1", "Station 2", "Station 3"]'::jsonb),
('staff', '["Station 1", "Station 2", "Station 3", "Hospital Center"]'::jsonb),
('dispatcher', '["Headquarters"]'::jsonb);

-- Insert resources
INSERT INTO resources (company_id, name, description, category, created_by) VALUES
(1, 'Ambulance Unit 1', 'Advanced Life Support ambulance', 'Medical', 1),
(1, 'Ambulance Unit 2', 'Basic Life Support ambulance', 'Medical', 1),
(1, 'Ambulance Unit 3', 'Critical Care Transport', 'Medical', 1),
(1, 'Fire Engine 1', 'Primary fire suppression vehicle', 'Fire', 1),
(1, 'Fire Engine 2', 'Secondary fire suppression vehicle', 'Fire', 1),
(1, 'Ladder Truck 1', 'Aerial ladder and rescue vehicle', 'Fire', 1),
(1, 'Police Unit 1', 'Law enforcement patrol vehicle', 'Law Enforcement', 1),
(1, 'Police Unit 2', 'Law enforcement patrol vehicle', 'Law Enforcement', 1),
(1, 'Search and Rescue Team', 'Specialized rescue personnel', 'Emergency Response', 1),
(1, 'Hazmat Team', 'Hazardous materials response team', 'Specialized', 1),
(1, 'Water Tanker', 'Water supply vehicle', 'Fire', 1),
(1, 'Command Vehicle', 'Mobile command center', 'Command', 1),
(1, 'K9 Unit', 'Police canine unit', 'Law Enforcement', 1),
(1, 'Emergency Shelter', 'Temporary housing facility', 'Emergency Response', 1),
(1, 'Medical Helicopter', 'Air medical transport', 'Medical', 1);

-- Insert responders (linking users to responder status)
INSERT INTO responders (id, latitude, longitude, status) VALUES
(2, 43.8342333, -91.3185684, 'available'),
(3, 39.7817, -89.6501, 'available'),
(4, 39.7900, -89.6400, 'available'),
(5, 43.8342373, -91.3185703, 'available'),
(6, 39.7750, -89.6600, 'available'),
(7, 39.7850, -89.6550, 'available'),
(8, 39.7817, -89.6501, 'available'),
(9, 39.7820, -89.6520, 'available'),
(10, 39.7900, -89.6400, 'available');

-- Insert user main locations
INSERT INTO user_main_locations (user_id, common_name, address, city, state, zip, latitude, longitude) VALUES
(1, 'Admin Office', '100 Admin St', 'Springfield', 'IL', '62701', 39.7820, -89.6520),
(2, 'Command Center', '300 Command Center Dr', 'Springfield', 'IL', '62706', 39.7820, -89.6520),
(3, 'Station 1 Base', '123 Main St', 'Springfield', 'IL', '62701', 39.7817, -89.6501),
(4, 'Station 2 Base', '456 Oak Ave', 'Springfield', 'IL', '62702', 39.7900, -89.6400),
(5, 'Station 1 Base', '123 Main St', 'Springfield', 'IL', '62701', 39.7817, -89.6501),
(6, 'Station 3 Base', '789 Pine St', 'Springfield', 'IL', '62703', 39.7750, -89.6600),
(7, 'Hospital Center', '100 Medical Way', 'Springfield', 'IL', '62704', 39.7850, -89.6550),
(8, 'Station 1 Base', '123 Main St', 'Springfield', 'IL', '62701', 39.7817, -89.6501),
(9, 'Dispatch Center', '300 Command Center Dr', 'Springfield', 'IL', '62706', 39.7820, -89.6520),
(10, 'Station 2 Base', '456 Oak Ave', 'Springfield', 'IL', '62702', 39.7900, -89.6400);

-- Insert checklist templates
INSERT INTO checklist_templates (name, description, template_data, created_by, is_active) VALUES
('EMS Response Checklist', 'Standard EMS response procedures',
'[
  {"text": "Don PPE and safety equipment", "required": true, "role": "all"},
  {"text": "Assess scene safety", "required": true, "role": "all"},
  {"text": "Establish patient contact", "required": true, "role": "medic"},
  {"text": "Perform primary assessment", "required": true, "role": "medic"},
  {"text": "Establish IV access if needed", "required": false, "role": "paramedic"},
  {"text": "Monitor vital signs", "required": true, "role": "medic"},
  {"text": "Prepare for transport", "required": true, "role": "all"},
  {"text": "Contact receiving facility", "required": true, "role": "medic"},
  {"text": "Complete documentation", "required": true, "role": "all"}
]'::jsonb, 1, true),

('Fire Response Checklist', 'Standard fire response procedures',
'[
  {"text": "Don full PPE including SCBA", "required": true, "role": "all"},
  {"text": "Assess fire conditions and hazards", "required": true, "role": "all"},
  {"text": "Establish water supply", "required": true, "role": "engineer"},
  {"text": "Deploy attack lines", "required": true, "role": "firefighter"},
  {"text": "Conduct primary search", "required": true, "role": "firefighter"},
  {"text": "Ventilation operations", "required": false, "role": "firefighter"},
  {"text": "Overhaul and salvage", "required": false, "role": "firefighter"},
  {"text": "Complete incident report", "required": true, "role": "officer"}
]'::jsonb, 1, true),

('MCI Response Checklist', 'Mass Casualty Incident procedures',
'[
  {"text": "Establish incident command", "required": true, "role": "commander"},
  {"text": "Request additional resources", "required": true, "role": "commander"},
  {"text": "Set up triage area", "required": true, "role": "medic"},
  {"text": "Begin patient triage", "required": true, "role": "medic"},
  {"text": "Establish treatment areas", "required": true, "role": "medic"},
  {"text": "Coordinate transport", "required": true, "role": "supervisor"},
  {"text": "Maintain patient tracking", "required": true, "role": "all"},
  {"text": "Decontamination if needed", "required": false, "role": "hazmat"},
  {"text": "Family notification area", "required": false, "role": "supervisor"}
]'::jsonb, 1, true);

-- Insert event templates
INSERT INTO templates (name, description, config, created_by) VALUES
('Standard EMS Call', 'Basic EMS response template',
'{
  "title": "EMS Response",
  "scale": "Small",
  "urgency": "Medium",
  "module": "EMS",
  "event_type": "response",
  "location_update_interval": 60,
  "notification_channels": ["web_app", "sms"],
  "included_report_to_locations": [
    {
      "commonName": "Station 1",
      "address": "123 Main St, Springfield, IL 62701",
      "primary": true,
      "staffNeeded": 2,
      "resources": [
        {"name": "Ambulance Unit 1", "responderCount": 2}
      ]
    }
  ]
}'::jsonb, 1),

('Multi-Vehicle Accident', 'Template for vehicle accidents',
'{
  "title": "Multi-Vehicle Accident",
  "scale": "Medium",
  "urgency": "High",
  "module": "MCI",
  "event_type": "response",
  "location_update_interval": 30,
  "notification_channels": ["web_app", "sms", "voice_call"],
  "included_report_to_locations": [
    {
      "commonName": "Station 1",
      "address": "123 Main St, Springfield, IL 62701",
      "primary": true,
      "staffNeeded": 4,
      "resources": [
        {"name": "Ambulance Unit 1", "responderCount": 2},
        {"name": "Ambulance Unit 2", "responderCount": 2}
      ]
    },
    {
      "commonName": "Station 2",
      "address": "456 Oak Ave, Springfield, IL 62702",
      "primary": false,
      "staffNeeded": 2,
      "resources": [
        {"name": "Fire Engine 1", "responderCount": 2}
      ]
    }
  ]
}'::jsonb, 1),

('Hospital Emergency', 'Hospital emergency response template',
'{
  "title": "Hospital Emergency",
  "scale": "Large",
  "urgency": "Immediate",
  "module": "Hospital",
  "event_type": "response",
  "location_update_interval": 15,
  "notification_channels": ["web_app", "sms", "voice_call", "email"],
  "included_report_to_locations": [
    {
      "commonName": "Hospital Center",
      "address": "100 Medical Way, Springfield, IL 62704",
      "primary": true,
      "staffNeeded": 6,
      "resources": [
        {"name": "Medical Team", "responderCount": 4},
        {"name": "Security Team", "responderCount": 2}
      ]
    }
  ]
}'::jsonb, 1);

-- Insert sample events
INSERT INTO events (id, title, info, scale, urgency, location, description, status, created_by, escalation_level, included_report_to_locations, assigned_ids, notify_all_if_unassigned, module, custom_fields, event_type, location_update_interval, notification_channels, event_documents) VALUES
(1, 'Medical Emergency - Chest Pain', 'Patient experiencing severe chest pain', 'Small', 'High',
'{"zip": "62701", "city": "Springfield", "state": "IL", "address": "123 Main St", "commonName": "Downtown Office Building"}'::jsonb,
'65-year-old male experiencing severe chest pain and shortness of breath', 'open', 2, 0,
'[{"zip": "62701", "city": "Springfield", "state": "IL", "address": "123 Main St", "primary": true, "resources": [{"name": "Ambulance Unit 1", "responderCount": 2}], "commonName": "Station 1", "staffNeeded": 2}]'::jsonb,
'{3,4}', true, 'EMS', '{}'::jsonb, 'response', 60, '["web_app", "sms"]'::jsonb, '[]'::jsonb),

(2, 'Structure Fire - Residential', 'House fire with possible entrapment', 'Medium', 'Immediate',
'{"zip": "62702", "city": "Springfield", "state": "IL", "address": "456 Oak Ave", "commonName": "Residential Home"}'::jsonb,
'Two-story residential structure with heavy smoke showing, possible occupants trapped', 'open', 2, 0,
'[{"zip": "62701", "city": "Springfield", "state": "IL", "address": "123 Main St", "primary": true, "resources": [{"name": "Fire Engine 1", "responderCount": 4}, {"name": "Ladder Truck 1", "responderCount": 3}], "commonName": "Station 1", "staffNeeded": 7}, {"zip": "62702", "city": "Springfield", "state": "IL", "address": "456 Oak Ave", "primary": false, "resources": [{"name": "Fire Engine 2", "responderCount": 4}], "commonName": "Station 2", "staffNeeded": 4}]'::jsonb,
'{3,4,8}', true, 'Fire', '{}'::jsonb, 'response', 30, '["web_app", "sms", "voice_call"]'::jsonb, '[]'::jsonb),

(3, 'MCI - Highway Accident', 'Multi-vehicle accident on I-55', 'Large', 'Immediate',
'{"zip": "62703", "city": "Springfield", "state": "IL", "address": "I-55 Mile Marker 98", "commonName": "Interstate 55"}'::jsonb,
'Multiple vehicle accident involving 5 cars and 1 semi-truck, multiple casualties reported', 'open', 2, 1,
'[{"zip": "62701", "city": "Springfield", "state": "IL", "address": "123 Main St", "primary": true, "resources": [{"name": "Ambulance Unit 1", "responderCount": 2}, {"name": "Ambulance Unit 2", "responderCount": 2}, {"name": "Command Vehicle", "responderCount": 1}], "commonName": "Station 1", "staffNeeded": 5}, {"zip": "62702", "city": "Springfield", "state": "IL", "address": "456 Oak Ave", "primary": false, "resources": [{"name": "Ambulance Unit 3", "responderCount": 2}, {"name": "Fire Engine 2", "responderCount": 4}], "commonName": "Station 2", "staffNeeded": 6}]'::jsonb,
'{2,3,4,5,6}', true, 'MCI', '{}'::jsonb, 'response', 15, '["web_app", "sms", "voice_call"]'::jsonb, '[]'::jsonb),

(4, 'Hospital Code Blue', 'Cardiac arrest in Emergency Department', 'Medium', 'Immediate',
'{"zip": "62704", "city": "Springfield", "state": "IL", "address": "100 Medical Way", "commonName": "Hospital Center Emergency Department"}'::jsonb,
'Patient in cardiac arrest, CPR in progress, need additional medical personnel', 'resolved', 7, 0,
'[{"zip": "62704", "city": "Springfield", "state": "IL", "address": "100 Medical Way", "primary": true, "resources": [{"name": "Medical Team", "responderCount": 3}], "commonName": "Hospital Center", "staffNeeded": 3}]'::jsonb,
'{7}', false, 'Hospital', '{}'::jsonb, 'response', 30, '["web_app"]'::jsonb, '[]'::jsonb),

(5, 'Weather Alert - Severe Thunderstorm', 'Severe weather warning for county', 'Medium', 'Medium',
'{"zip": "62700", "city": "Springfield", "state": "IL", "address": "Countywide", "commonName": "Sangamon County"}'::jsonb,
'Severe thunderstorm warning with possible tornadoes, high winds, and hail', 'open', 9, 0,
'[{"zip": "62706", "city": "Springfield", "state": "IL", "address": "300 Command Center Dr", "primary": true, "resources": [{"name": "Command Vehicle", "responderCount": 2}], "commonName": "Headquarters", "staffNeeded": 2}]'::jsonb,
'{}', true, 'Emergency', '{}'::jsonb, 'notification_only', 300, '["web_app", "sms", "email"]'::jsonb, '[]'::jsonb);

-- Insert tasks for the events
INSERT INTO tasks (event_id, assigned_to, status, report_to_location) VALUES
(1, 3, 'enroute', '123 Main St, Springfield, IL 62701'),
(1, 4, 'enroute', '123 Main St, Springfield, IL 62701'),
(2, 3, 'acknowledged', '456 Oak Ave, Springfield, IL 62702'),
(2, 4, 'acknowledged', '456 Oak Ave, Springfield, IL 62702'),
(2, 8, 'pending', '456 Oak Ave, Springfield, IL 62702'),
(3, 2, 'acknowledged', 'I-55 Mile Marker 98, Springfield, IL 62703'),
(3, 3, 'enroute', 'I-55 Mile Marker 98, Springfield, IL 62703'),
(3, 4, 'enroute', 'I-55 Mile Marker 98, Springfield, IL 62703'),
(3, 5, 'pending', 'I-55 Mile Marker 98, Springfield, IL 62703'),
(3, 6, 'pending', 'I-55 Mile Marker 98, Springfield, IL 62703'),
(4, 7, 'completed', '100 Medical Way, Springfield, IL 62704');

-- Insert notifications
INSERT INTO notifications (responder_id, event_id, message, type) VALUES
(3, 1, 'Medical emergency dispatch - chest pain patient', 'sms'),
(4, 1, 'Medical emergency dispatch - chest pain patient', 'sms'),
(3, 2, 'Structure fire dispatch - possible entrapment', 'voice_call'),
(4, 2, 'Structure fire dispatch - possible entrapment', 'voice_call'),
(8, 2, 'Structure fire dispatch - possible entrapment', 'voice_call'),
(2, 3, 'MCI activation - highway accident', 'voice_call'),
(3, 3, 'MCI activation - highway accident', 'voice_call'),
(4, 3, 'MCI activation - highway accident', 'voice_call'),
(5, 3, 'MCI activation - highway accident', 'sms'),
(6, 3, 'MCI activation - highway accident', 'sms'),
(7, 4, 'Code Blue - Emergency Department', 'web_app');

-- Insert action items for events
INSERT INTO action_items (event_id, user_id, action_text, is_completed, user_role, action_index) VALUES
(1, 3, 'Assess patient vital signs', false, 'staff', 1),
(1, 3, 'Establish IV access', false, 'staff', 2),
(1, 4, 'Prepare transport equipment', true, 'staff', 1),
(1, 4, 'Contact receiving hospital', false, 'staff', 2),
(2, 3, 'Don full PPE including SCBA', true, 'staff', 1),
(2, 3, 'Conduct primary search', false, 'staff', 2),
(2, 8, 'Establish water supply', true, 'staff', 1),
(2, 8, 'Deploy attack lines', false, 'staff', 2),
(3, 2, 'Establish incident command', true, 'commander', 1),
(3, 2, 'Request additional resources', true, 'commander', 2),
(3, 3, 'Set up triage area', false, 'staff', 1),
(3, 4, 'Begin patient assessment', false, 'staff', 1);

-- Insert document questions (sample AI Q&A)
INSERT INTO document_questions (event_id, user_id, question, answer, document_context) VALUES
(1, 3, 'What are the protocols for chest pain patients?', 'Follow ACS protocols: 12-lead ECG, IV access, oxygen if SpO2 <94%, aspirin if no contraindications, nitroglycerin if systolic BP >100mmHg', '{"document_type": "protocol", "section": "cardiac_emergencies"}'::jsonb),
(2, 8, 'What is the water supply requirement for structure fires?', 'Minimum 500 GPM for residential structures, establish continuous water supply, consider tanker operations if hydrants unavailable', '{"document_type": "sop", "section": "fire_suppression"}'::jsonb),
(3, 2, 'How many ambulances needed for MCI with 10+ patients?', 'Initial response: 1 ambulance per 5 patients, request additional resources early, establish transport officer for coordination', '{"document_type": "mci_plan", "section": "resource_allocation"}'::jsonb);

-- Insert document summaries
INSERT INTO document_summaries (event_id, user_id, document_name, summary, checklist_items) VALUES
(1, 3, 'Cardiac Emergency Protocol', 'Comprehensive protocol for cardiac emergencies including assessment, treatment, and transport procedures. Key points: early recognition, 12-lead ECG, medication administration, and rapid transport to appropriate facility.',
'["Obtain 12-lead ECG", "Establish IV access", "Administer oxygen if indicated", "Consider aspirin and nitroglycerin", "Monitor vital signs continuously", "Transport to cardiac center"]'::jsonb),
(2, 8, 'Structure Fire SOP', 'Standard operating procedures for structure fire response including size-up, attack strategies, ventilation, and safety protocols. Emphasizes crew safety and coordinated attack.',
'["Complete 360-degree size-up", "Establish water supply", "Deploy appropriate attack lines", "Coordinate ventilation", "Maintain crew accountability", "Conduct primary search"]'::jsonb),
(3, 2, 'MCI Response Plan', 'Mass casualty incident response procedures covering command structure, triage, treatment, and transport. Includes resource allocation and communication protocols.',
'["Establish incident command", "Request additional resources", "Set up triage area", "Implement START triage", "Establish treatment areas", "Coordinate transport", "Maintain patient tracking"]'::jsonb);

-- Insert short URLs for mobile access
INSERT INTO short_urls (short_code, original_url, click_count, expires_at) VALUES
('evt001', 'http://localhost:3001/event/1', 5, NOW() + INTERVAL '24 hours'),
('evt002', 'http://localhost:3001/event/2', 8, NOW() + INTERVAL '24 hours'),
('evt003', 'http://localhost:3001/event/3', 12, NOW() + INTERVAL '24 hours'),
('dash01', 'http://localhost:3001/dashboard', 25, NOW() + INTERVAL '7 days'),
('resp01', 'http://localhost:3001/responder', 15, NOW() + INTERVAL '7 days');

-- Insert chat messages for events
INSERT INTO chat_messages (event_id, user_id, username, text, timestamp) VALUES
(1, 3, 'John Doe', 'Responding to chest pain call, ETA 5 minutes', NOW() - INTERVAL '10 minutes'),
(1, 4, 'Lori Johnson', 'Copy that, preparing equipment', NOW() - INTERVAL '8 minutes'),
(1, 2, 'Thomas Tornstrom', 'Hospital notified, they are ready to receive', NOW() - INTERVAL '5 minutes'),
(2, 3, 'John Doe', 'On scene, heavy smoke showing from second floor', NOW() - INTERVAL '15 minutes'),
(2, 8, 'Robert Brown', 'Establishing water supply, deploying attack lines', NOW() - INTERVAL '12 minutes'),
(2, 2, 'Thomas Tornstrom', 'All occupants accounted for, no entrapment', NOW() - INTERVAL '8 minutes'),
(3, 2, 'Thomas Tornstrom', 'MCI command established, requesting additional ambulances', NOW() - INTERVAL '20 minutes'),
(3, 3, 'John Doe', 'Triage area set up, 3 red, 5 yellow, 2 green patients', NOW() - INTERVAL '15 minutes'),
(3, 4, 'Lori Johnson', 'First transport departing to trauma center', NOW() - INTERVAL '10 minutes');

-- Insert chat summaries
INSERT INTO chat_summaries (event_id, summary_text, generated_by) VALUES
(1, 'Medical emergency response: Crew responding to chest pain patient, ETA 5 minutes. Hospital has been notified and is prepared to receive patient. Equipment being prepared for immediate care.', 1),
(2, 'Structure fire response: Heavy smoke from second floor, water supply established, attack lines deployed. All occupants accounted for with no entrapment reported.', 1),
(3, 'MCI response: Command established, additional resources requested. Triage completed with 3 critical, 5 moderate, 2 minor patients. First transport to trauma center initiated.', 1);

-- Insert event logs
INSERT INTO event_logs (event_id, action, timestamp, details, user_id) VALUES
(1, 'event_created', NOW() - INTERVAL '30 minutes', '{"created_by": "Thomas Tornstrom", "priority": "High"}', 2),
(1, 'responder_assigned', NOW() - INTERVAL '25 minutes', '{"responder": "John Doe", "role": "Paramedic"}', 2),
(1, 'responder_assigned', NOW() - INTERVAL '25 minutes', '{"responder": "Lori Johnson", "role": "Paramedic"}', 2),
(1, 'status_updated', NOW() - INTERVAL '20 minutes', '{"status": "enroute", "responder": "John Doe"}', 3),
(2, 'event_created', NOW() - INTERVAL '45 minutes', '{"created_by": "Thomas Tornstrom", "priority": "Immediate"}', 2),
(2, 'escalation_level_increased', NOW() - INTERVAL '40 minutes', '{"from": 0, "to": 1, "reason": "Structure fire with entrapment"}', 2),
(2, 'additional_resources_requested', NOW() - INTERVAL '35 minutes', '{"resources": ["Fire Engine 2", "Ladder Truck 1"]}', 2),
(3, 'event_created', NOW() - INTERVAL '60 minutes', '{"created_by": "Thomas Tornstrom", "priority": "Immediate"}', 2),
(3, 'mci_declared', NOW() - INTERVAL '55 minutes', '{"patient_count": 10, "severity": "Multiple casualties"}', 2),
(3, 'command_established', NOW() - INTERVAL '50 minutes', '{"commander": "Thomas Tornstrom", "location": "I-55 Mile Marker 98"}', 2);

-- Insert modules configuration
INSERT INTO modules (customer_id, module_name, enabled) VALUES
(1, 'EMS', true),
(1, 'Fire', true),
(1, 'Hospital', true),
(1, 'MCI', true),
(1, 'Transport', true),
(1, 'Scheduling', true),
(1, 'Reporting', true),
(1, 'AI_Features', true),
(1, 'Chat', true),
(1, 'Weather', false);

-- Insert responder location logs
INSERT INTO responder_logs (responder_id, latitude, longitude, status, timestamp) VALUES
(3, 39.7817, -89.6501, 'available', NOW() - INTERVAL '2 hours'),
(3, 39.7820, -89.6505, 'enroute', NOW() - INTERVAL '1 hour'),
(3, 39.7825, -89.6510, 'on_scene', NOW() - INTERVAL '30 minutes'),
(4, 39.7900, -89.6400, 'available', NOW() - INTERVAL '2 hours'),
(4, 39.7905, -89.6405, 'enroute', NOW() - INTERVAL '1 hour'),
(4, 39.7910, -89.6410, 'on_scene', NOW() - INTERVAL '30 minutes'),
(8, 39.7817, -89.6501, 'available', NOW() - INTERVAL '3 hours'),
(8, 39.7820, -89.6505, 'enroute', NOW() - INTERVAL '2 hours'),
(8, 39.7825, -89.6510, 'on_scene', NOW() - INTERVAL '1 hour');

-- Insert shifts
INSERT INTO shifts (start_time, end_time, location, staff_needed, created_by) VALUES
(NOW() + INTERVAL '1 day', NOW() + INTERVAL '1 day 12 hours', 'Station 1', 4, 2),
(NOW() + INTERVAL '1 day 12 hours', NOW() + INTERVAL '2 days', 'Station 1', 4, 2),
(NOW() + INTERVAL '1 day', NOW() + INTERVAL '1 day 12 hours', 'Station 2', 3, 2),
(NOW() + INTERVAL '1 day 12 hours', NOW() + INTERVAL '2 days', 'Station 2', 3, 2),
(NOW() + INTERVAL '1 day', NOW() + INTERVAL '1 day 8 hours', 'Hospital Center', 2, 2),
(NOW() + INTERVAL '1 day 8 hours', NOW() + INTERVAL '1 day 16 hours', 'Hospital Center', 2, 2),
(NOW() + INTERVAL '1 day 16 hours', NOW() + INTERVAL '2 days', 'Hospital Center', 2, 2);

-- Insert shift assignments
INSERT INTO shift_assignments (shift_id, user_id) VALUES
(1, 3), (1, 4), (1, 8), (1, 6),
(2, 5), (2, 7), (2, 9), (2, 10),
(3, 4), (3, 6), (3, 10),
(4, 3), (4, 5), (4, 8),
(5, 7), (5, 9),
(6, 7), (6, 10),
(7, 9), (7, 3);

-- Insert vendors
INSERT INTO vendors (name, email, phone, transport_modes, capabilities, base_location, availability_status, contact_methods, api_endpoint, api_type) VALUES
('City Ambulance Service', '<EMAIL>', '******-CITY-EMS',
 ARRAY['Ambulance'], ARRAY['ALS', 'BLS', 'Oxygen', 'Stretcher'],
 '123 Main St, Springfield, IL', 'available', ARRAY['email', 'sms', 'phone'],
 'https://api.cityambulance.com/dispatch', 'REST'),
('Wheelchair Transport Co', '<EMAIL>', '******-WHEEL-01',
 ARRAY['Wheelchair Van'], ARRAY['Wheelchair Access', 'Non-Emergency'],
 '456 Oak St, Springfield, IL', 'available', ARRAY['email', 'sms'],
 NULL, NULL),
('Local Taxi Service', '<EMAIL>', '******-TAXI-NOW',
 ARRAY['Taxi Cab'], ARRAY['Non-Emergency', 'Basic Transport'],
 '789 Pine St, Springfield, IL', 'available', ARRAY['email', 'phone'],
 NULL, NULL),
('Air Medical Services', '<EMAIL>', '******-FLY-LIFE',
 ARRAY['Air Medical'], ARRAY['ALS', 'Critical Care', 'Long Distance'],
 '101 Airport Rd, Springfield, IL', 'available', ARRAY['email', 'sms'],
 'https://api.airmedical.com/dispatch', 'REST'),
('Regional Transport', '<EMAIL>', '******-REGION-1',
 ARRAY['Ambulance', 'Wheelchair Van'], ARRAY['ALS', 'BLS', 'Wheelchair Access'],
 '200 Regional Dr, Springfield, IL', 'busy', ARRAY['email', 'sms', 'phone'],
 'https://api.regionaltransport.com/v1', 'REST');

-- Insert transport requests
INSERT INTO transport_requests (pickup_location, destination, vehicle_type, urgency, event_id, patient_name, patient_condition, special_needs, vendor_id, status, created_by) VALUES
('100 Medical Way, Springfield, IL', 'Regional Medical Center, 500 Health Dr, Springfield, IL', 'Ambulance', 'High', 1, 'John Smith', 'Chest Pain', 'Cardiac Monitor Required', 1, 'assigned', 3),
('456 Oak Ave, Springfield, IL', 'Burn Center, 300 Specialty Rd, Springfield, IL', 'Ambulance', 'Immediate', 2, 'Jane Doe', 'Burn Injuries', 'Critical Care', 4, 'in_progress', 8),
('I-55 Mile Marker 98, Springfield, IL', 'Trauma Center, 400 Emergency Blvd, Springfield, IL', 'Ambulance', 'Immediate', 3, 'Multiple Patients', 'Trauma', 'Multiple Transports Needed', 1, 'completed', 2),
('789 Pine St, Springfield, IL', 'Rehabilitation Center, 600 Recovery Way, Springfield, IL', 'Wheelchair Van', 'Low', NULL, 'Mary Johnson', 'Post-Surgery', 'Wheelchair Required', 2, 'requested', 7),
('Hospital Center, Springfield, IL', 'Nursing Home, 700 Care Lane, Springfield, IL', 'Wheelchair Van', 'Medium', NULL, 'Robert Wilson', 'Discharge Transport', 'Oxygen Required', 2, 'assigned', 7);

-- Insert transport assignments
INSERT INTO transport_assignments (request_id, driver_id) VALUES
(1, 3),
(2, 4),
(3, 5),
(4, 6),
(5, 10);

-- Reset sequences to ensure proper auto-increment
SELECT setval('users_id_seq', (SELECT MAX(id) FROM users));
SELECT setval('companys_id_seq', (SELECT MAX(id) FROM companys));
SELECT setval('events_id_seq', (SELECT MAX(id) FROM events));
SELECT setval('tasks_id_seq', (SELECT MAX(id) FROM tasks));
SELECT setval('templates_id_seq', (SELECT MAX(id) FROM templates));
SELECT setval('notifications_id_seq', (SELECT MAX(id) FROM notifications));
SELECT setval('common_locations_id_seq', (SELECT MAX(id) FROM common_locations));
SELECT setval('resources_id_seq', (SELECT MAX(id) FROM resources));
SELECT setval('user_main_locations_id_seq', (SELECT MAX(id) FROM user_main_locations));
SELECT setval('short_urls_id_seq', (SELECT MAX(id) FROM short_urls));
SELECT setval('action_items_id_seq', (SELECT MAX(id) FROM action_items));
SELECT setval('checklist_templates_id_seq', (SELECT MAX(id) FROM checklist_templates));
SELECT setval('document_questions_id_seq', (SELECT MAX(id) FROM document_questions));
SELECT setval('document_summaries_id_seq', (SELECT MAX(id) FROM document_summaries));
SELECT setval('company_settings_id_seq', (SELECT MAX(id) FROM company_settings));
SELECT setval('roles_config_id_seq', (SELECT MAX(id) FROM roles_config));
SELECT setval('chat_messages_id_seq', (SELECT MAX(id) FROM chat_messages));
SELECT setval('chat_summaries_id_seq', (SELECT MAX(id) FROM chat_summaries));
SELECT setval('event_logs_id_seq', (SELECT MAX(id) FROM event_logs));
SELECT setval('responder_logs_id_seq', (SELECT MAX(id) FROM responder_logs));
SELECT setval('shifts_id_seq', (SELECT MAX(id) FROM shifts));
SELECT setval('shift_assignments_id_seq', (SELECT MAX(id) FROM shift_assignments));
SELECT setval('vendors_id_seq', (SELECT MAX(id) FROM vendors));
SELECT setval('transport_requests_id_seq', (SELECT MAX(id) FROM transport_requests));
SELECT setval('transport_assignments_id_seq', (SELECT MAX(id) FROM transport_assignments));

-- Commit the transaction
COMMIT;

-- Display success message and summary
SELECT 'AlertComm Database Setup Completed Successfully!' AS status;

-- Show summary of created data
SELECT
    'Users: ' || COUNT(*) AS summary FROM users
UNION ALL
SELECT
    'Events: ' || COUNT(*) FROM events
UNION ALL
SELECT
    'Tasks: ' || COUNT(*) FROM tasks
UNION ALL
SELECT
    'Resources: ' || COUNT(*) FROM resources
UNION ALL
SELECT
    'Templates: ' || COUNT(*) FROM templates
UNION ALL
SELECT
    'Locations: ' || COUNT(*) FROM common_locations
UNION ALL
SELECT
    'Chat Messages: ' || COUNT(*) FROM chat_messages
UNION ALL
SELECT
    'Vendors: ' || COUNT(*) FROM vendors
UNION ALL
SELECT
    'Shifts: ' || COUNT(*) FROM shifts
UNION ALL
SELECT
    'Transport Requests: ' || COUNT(*) FROM transport_requests
UNION ALL
SELECT
    'Total Tables: 27' AS summary;

-- Show login credentials for testing
SELECT
    'Login Credentials for Testing:' AS info
UNION ALL
SELECT
    'ALL USERS PASSWORD: password123'
UNION ALL
SELECT
    'Admin: admin / password123'
UNION ALL
SELECT
    'Commander: commander1 / password123'
UNION ALL
SELECT
    'Medic: medic1 / password123'
UNION ALL
SELECT
    'Staff: medic2 / password123';

-- Final verification queries
SELECT 'Database verification complete. All tables created and populated with test data.' AS final_status;
