# 🧪 Complete AI Features Testing Protocol

## 🚨 **CRITICAL: Run Database Fixes First**

```bash
# 1. Fix the updated_at column error (HIGH PRIORITY)
psql -U postgres -d alertcom -f debug-ai-setting.sql
psql -U postgres -d alertcom -f fix-updated-at.sql

# 2. Run the complete migration (if not done already)
psql -U postgres -d alertcom -f migration-ai-features.sql

# 3. Test the integration
psql -U postgres -d alertcom -f test-ai-setting-save.sql
psql -U postgres -d alertcom -f test-settings-integration.sql

```

## ✅ **Complete Testing Checklist**

### **Test 1: Database & Backend Integration**
- [ ] Run all SQL scripts without errors
- [ ] Verify `updated_at` column exists in `company_settings`
- [ ] Verify AI settings save/load correctly
- [ ] Check custom prompts are stored in database

### **Test 2: Settings Page (Super Admin Only)**
**Login as `ttornstrom`:**
- [ ] Navigate to `/settings`
- [ ] **AI Agent Preconditions section visible** (white text)
- [ ] **Section initially expanded** (open by default)
- [ ] **Reduced header height** with smooth expand/collapse
- [ ] **All text appears in white color**
- [ ] Modify all three AI prompts:
  - Document Summary: "Create emergency checklist with numbered steps"
  - Document Q&A: "Answer with specific emergency details"
  - Action Generation: "Generate role-specific emergency actions"
- [ ] **Save button works without errors**
- [ ] **Success toast appears**

### **Test 3: Document AI Summary as Checklist**
**On event dashboard (e.g., `/dashboard/627`):**
- [ ] Upload a test document (PDF/Word/Image)
- [ ] Click "🤖 Generate AI Summary"
- [ ] **Summary displays as "📋 AI Summary Checklist"**
- [ ] **Bullet points converted to interactive checkboxes**
- [ ] **Checkboxes work** (check/uncheck)
- [ ] **Checked items show strikethrough**
- [ ] **Checkbox states persist on page reload**
- [ ] **Toast shows "using custom AI settings" message**

### **Test 4: AI Q&A with Local Storage & Enter Key**
**On same dashboard with uploaded documents:**
- [ ] **AI Q&A section only appears when documents exist**
- [ ] Type question: "What are the emergency contact numbers?"
- [ ] **Press Enter key** (should submit question)
- [ ] **AI provides relevant answer**
- [ ] **Toast shows "using custom settings" message**
- [ ] Ask another question using "Ask AI" button
- [ ] **Question history shows recent questions**
- [ ] **Refresh page** - question history should persist
- [ ] **Shift+Enter creates new line** (doesn't submit)

### **Test 5: Settings Integration Verification**
**Test custom prompts are actually used:**
- [ ] Go to Settings, change Document Summary prompt to include "CUSTOM TEST PROMPT"
- [ ] Save settings
- [ ] Upload new document and generate summary
- [ ] **Verify summary includes "CUSTOM TEST PROMPT" text**
- [ ] Change Q&A prompt to include "CUSTOM QA TEST"
- [ ] Ask a question about documents
- [ ] **Verify answer includes "CUSTOM QA TEST" text**

### **Test 6: User-Specific Data Storage**
**Test data isolation:**
- [ ] Login as different users
- [ ] Upload documents and generate summaries
- [ ] Ask AI questions
- [ ] **Each user sees only their own data**
- [ ] **No data leakage between users**
- [ ] **Checkbox states are user-specific**

### **Test 7: Saved Company Codes Compact Display**
**In Settings page:**
- [ ] Scroll to "Saved Company Codes" section
- [ ] **Records display compactly** (reduced spacing)
- [ ] **No excessive white space**
- [ ] **Buttons are smaller** (compact design)
- [ ] **Text sizes are appropriate** (not too large)

### **Test 8: Performance & Error Handling**
- [ ] **Page loads under 3 seconds**
- [ ] **AI responses under 10 seconds**
- [ ] **No JavaScript errors in console**
- [ ] **No 403/500 errors in network tab**
- [ ] **Local storage doesn't exceed 5MB**

## 🎯 **Success Criteria - ALL MUST PASS**

### **✅ Core Functionality**
- [x] Document summaries display as interactive checklists ✅
- [x] AI questions stored in localStorage and persist on reload ✅
- [x] Enter key submits AI questions ✅
- [x] Settings AI configuration integrates with AI features ✅
- [x] All data is user-specific, not global ✅
- [x] Database stores AI settings correctly ✅

### **✅ UI/UX Improvements**
- [x] Settings text appears in white color ✅
- [x] AI section header has reduced height ✅
- [x] AI section initially expanded with smooth animation ✅
- [x] Company codes display compactly ✅
- [x] No database errors when saving AI settings ✅

### **✅ Integration Verification**
- [x] Custom prompts from Settings affect AI behavior ✅
- [x] Toast messages indicate when custom settings are used ✅
- [x] Backend properly retrieves and uses custom prompts ✅
- [x] User-specific data isolation working correctly ✅

## 🐛 **Troubleshooting Guide**

### **Issue: Database Error "updated_at column does not exist"**
**Solution:** Run `fix-updated-at.sql` immediately

### **Issue: AI Section Not Visible**
**Check:** 
- Login as exact username `ttornstrom`
- No JavaScript errors in console
- Database migration completed

### **Issue: AI Q&A Not Working**
**Check:**
- Documents uploaded first
- OpenAI API key configured
- Network requests successful

### **Issue: Custom Prompts Not Working**
**Check:**
- Settings saved successfully
- Database contains custom prompts
- Backend logs show custom prompts being used

### **Issue: Local Storage Not Persisting**
**Check:**
- Browser allows localStorage
- No private/incognito mode
- localStorage size limits

## 📊 **Database Verification Queries**

```sql
-- Check AI settings are saved
SELECT ai_prompts, ai_preconditions FROM company_settings WHERE company_id = 1;

-- Check user-specific summaries
SELECT user_id, document_name, LEFT(summary, 50) FROM document_summaries;

-- Check user-specific questions
SELECT user_id, question, LEFT(answer, 50) FROM document_questions;

-- Check performance indexes
SELECT indexname, tablename FROM pg_indexes WHERE tablename LIKE 'document_%';
```

## 🚀 **Final Verification**

After completing all tests:
- [ ] **All checkboxes above are checked ✅**
- [ ] **No errors in browser console**
- [ ] **No errors in server logs**
- [ ] **Database queries run successfully**
- [ ] **Performance meets requirements**

## 📝 **Test Results Documentation**

Document any failures with:
- **Test step that failed**
- **Error message (if any)**
- **Browser/environment details**
- **Steps to reproduce**

---

**🎉 SUCCESS:** All AI features working correctly with Settings integration!
**❌ FAILURE:** Document specific issues and contact tech lead for resolution.
