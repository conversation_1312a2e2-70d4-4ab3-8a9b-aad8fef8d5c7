# 🔧 Complete AI Settings Fix Guide

## 🚨 **Issues Identified**

1. **Backend Permission Mismatch**: API checks `username === 'ttornstrom'` but frontend uses `userId === 2`
2. **Database Column Missing**: `updated_at` column may not exist
3. **Data Persistence**: Settings save but don't load on page refresh
4. **Error Handling**: Insufficient logging and error reporting

## ✅ **Fixes Applied**

### **1. Backend API Updates**
- ✅ Updated `/ai-settings` GET endpoint to support both username and user ID
- ✅ Updated `/ai-settings` PUT endpoint to support both username and user ID  
- ✅ Added comprehensive logging for debugging
- ✅ Enhanced error handling and database operations

### **2. Permission Check Logic**
```javascript
// OLD (only username)
if (req.user.username !== 'ttornstrom') {

// NEW (username OR user ID)
const isSupeAdmin = req.user.username === 'ttornstrom' || req.user.id === 2;
if (!isSupeAdmin) {
```

### **3. Enhanced Logging**
- ✅ User authentication details logged
- ✅ Database query results logged
- ✅ Request/response data logged
- ✅ Error details captured

## 🔧 **Required Actions**

### **Step 1: Fix Database Schema**
```bash
# Run this to ensure updated_at column exists
psql -U postgres -d alertcom -f fix-updated-at.sql
```

### **Step 2: Debug Current State**
```bash
# Run this to diagnose issues
psql -U postgres -d alertcom -f debug-ai-settings.sql
```

### **Step 3: Restart Backend Server**
```bash
# Restart to load the updated API endpoints
npm restart
# or
node server.js
```

### **Step 4: Test the Fix**

#### **Frontend Test:**
1. Login as user with ID 2 (or username 'ttornstrom')
2. Go to Settings page
3. Scroll to AI Agent Preconditions section
4. Modify any prompt and click Save
5. **Check browser console** for detailed logs
6. **Refresh page** and verify data persists

#### **Backend Test:**
1. **Check server logs** for detailed debugging info
2. **Monitor database** for successful saves
3. **Verify API responses** in browser Network tab

## 🔍 **Debugging Steps**

### **Check Browser Console**
```javascript
// Should see these logs in console:
"Loading AI settings for user: [username]"
"AI settings loaded successfully: [data]"
"Saving AI settings: [data]"
"Save result: [response]"
```

### **Check Server Logs**
```
AI Settings GET - User: [username] ID: [id] Is Super Admin: true
Fetching AI settings for company ID: 1
Database query result: 1 rows found
Raw data from DB: {...}

AI Settings PUT - User: [username] ID: [id] Is Super Admin: true
Request body: {...}
Saving AI settings for company ID: 1
Update result: 1 rows affected
```

### **Check Database**
```sql
-- Verify data is saved
SELECT ai_prompts, ai_preconditions, updated_at 
FROM company_settings 
WHERE company_id = 1;

-- Check user permissions
SELECT id, username, role 
FROM users 
WHERE id = 2 OR username = 'ttornstrom';
```

## 🐛 **Common Issues & Solutions**

### **Issue: 403 Access Denied**
**Cause:** User not recognized as Super Admin
**Solution:** 
- Verify user ID is 2 or username is 'ttornstrom'
- Check authentication token is valid
- Restart backend server

### **Issue: Data Saves But Doesn't Load**
**Cause:** Database schema or query issues
**Solution:**
- Run `debug-ai-settings.sql` to check database
- Verify `updated_at` column exists
- Check for JSON parsing errors

### **Issue: Network Errors**
**Cause:** API endpoint not responding
**Solution:**
- Check backend server is running
- Verify API endpoints are loaded
- Check for CORS issues

### **Issue: Frontend Not Updating**
**Cause:** State management or loading issues
**Solution:**
- Check browser console for errors
- Verify API responses in Network tab
- Clear browser cache and reload

## 📊 **Verification Checklist**

### **Database Verification**
- [ ] `updated_at` column exists in `company_settings`
- [ ] Company settings record exists for company_id = 1
- [ ] AI prompts and preconditions are properly stored as JSON
- [ ] User with ID 2 exists and has proper permissions

### **Backend Verification**
- [ ] `/ai-settings` GET endpoint responds with 200
- [ ] `/ai-settings` PUT endpoint accepts data and returns success
- [ ] Server logs show detailed debugging information
- [ ] Permission checks work for both username and user ID

### **Frontend Verification**
- [ ] AI Agent Preconditions section visible for authorized user
- [ ] Form fields populate with existing data on page load
- [ ] Save button works without errors
- [ ] Data persists after page refresh
- [ ] Success/error messages display correctly

## 🎯 **Expected Behavior**

### **Successful Save Flow:**
1. User modifies AI prompts in Settings
2. Clicks "Save AI Settings"
3. Frontend sends PUT request to `/ai-settings`
4. Backend validates user permissions (ID 2 or username 'ttornstrom')
5. Data saved to `company_settings` table
6. Success response returned
7. Frontend shows success message
8. Page refresh loads saved data correctly

### **Successful Load Flow:**
1. User navigates to Settings page
2. Frontend sends GET request to `/ai-settings`
3. Backend validates user permissions
4. Data retrieved from `company_settings` table
5. Response sent with current AI settings
6. Frontend populates form fields
7. User sees previously saved data

## 🚀 **Testing Protocol**

### **Manual Test Steps:**
1. **Database Setup:** Run SQL scripts to ensure schema is correct
2. **Backend Test:** Check API endpoints with curl or Postman
3. **Frontend Test:** Use browser to test complete user flow
4. **Integration Test:** Verify end-to-end functionality
5. **Persistence Test:** Save, refresh, verify data remains

### **Automated Verification:**
```bash
# Test API endpoints
curl -H "Authorization: Bearer [token]" http://localhost:5000/ai-settings
curl -X PUT -H "Authorization: Bearer [token]" -H "Content-Type: application/json" \
  -d '{"ai_prompts":{"document_summary":"test"},"ai_preconditions":{}}' \
  http://localhost:5000/ai-settings
```

## 📞 **Support**

If issues persist after following this guide:
1. **Check all logs** (browser console, server logs, database logs)
2. **Run debug scripts** to identify specific problems
3. **Verify environment** (database connection, API server status)
4. **Contact system administrator** with specific error messages

---

**🎉 Success Criteria:** AI Agent Preconditions should save successfully and persist after page reload with no errors in console or server logs.
