const express = require("express");
const http = require("http");
const { Server } = require("socket.io");
const dotenv = require("dotenv");
const { Pool } = require("pg");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");
const nodemailer = require("nodemailer");
const twilio = require("twilio");
const cors = require("cors");
const { Client } = require("@googlemaps/google-maps-services-js");
const db = require("./db");
const path = require("path");
const multer = require("multer");
const fs = require("fs");
const OpenAI = require("openai");

dotenv.config();

// Configuration
const allowedOrigins = [
  'http://localhost:3001',    // Frontend React app
  'http://localhost:8081',    // React Native app (Expo web)
  'http://localhost:8082',    // React Native app (Expo web - alternate port)
  'https://alertcomm1.com',   // Production frontend
  'exp://localhost:8081',     // Expo development
  'exp://localhost:8082',     // Expo development - alternate port
  'http://*************:8081', // Local network access for mobile
  'http://*************:8082'  // Local network access for mobile - alternate port
];
const PORT = process.env.PORT || 3000;
const API_BASE_URL = process.env.API_BASE_URL || `http://localhost:${PORT}`;
const FRONTEND_BASE_URL = process.env.FRONTEND_BASE_URL || 'http://localhost:3001';

// Initialize Express and Socket.io
const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: allowedOrigins,
    methods: ["GET", "POST", "PUT", "DELETE"],
    allowedHeaders: ["Authorization", "Content-Type"],
    credentials: true,
  },
});

// Database connection
const pool = new Pool({
  host: process.env.DB_HOST || "localhost",
  user: process.env.DB_USER || "postgres",
  password: process.env.DB_PASSWORD || "password",
  database: process.env.DB_NAME || "alertcom",
  port: parseInt(process.env.DB_PORT) || 5432,
  ssl: false,
  connectionTimeoutMillis: 10000,
});

pool.connect((err) => {
  if (err) console.error("Database connection failed:", err.message);
  else console.log("Connected to PostgreSQL");
});

// Email and SMS services
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: Number(process.env.EMAIL_PORT),
  secure: process.env.EMAIL_SECURE === 'true',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

// Initialize Twilio only if credentials are provided and valid
let twilioClient = null;
if (process.env.TWILIO_SID && process.env.TWILIO_AUTH_TOKEN && process.env.TWILIO_SID.startsWith('AC')) {
  try {
    twilioClient = new twilio(
      process.env.TWILIO_SID,
      process.env.TWILIO_AUTH_TOKEN
    );
    console.log('✅ Twilio client initialized');
  } catch (error) {
    console.log('⚠️ Twilio initialization failed:', error.message);
  }
} else {
  console.log('⚠️ Twilio not configured - SMS features disabled');
}

const googleMapsClient = new Client({});

// Initialize OpenAI client only if API key is available
let openai = null;
if (process.env.OPENAI_API_KEY) {
  openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });
  console.log("OpenAI client initialized");
} else {
  console.log("OpenAI API key not found - AI features will use fallback responses");
}

// Middleware
app.use(
  cors({
    origin: allowedOrigins,
    methods: ["GET", "POST", "PUT", "DELETE"],
    allowedHeaders: ["Content-Type", "Authorization"],
    credentials: true,
  })
);
app.use(express.json());
// Serve frontend static files
app.use('/frontend', express.static(path.join(__dirname, 'frontend')));
// Serve favicon
app.use('/favicon.ico', express.static(path.join(__dirname, 'favicon.ico')));

// Authentication middleware
const authenticateToken = (req, res, next) => {

  const token = req.headers["authorization"]?.split(" ")[1];
  // console.log('Extracted token:', token ? `${token.substring(0, 20)}...` : 'NO TOKEN');

  if (!token) {
    return res.status(401).json({ error: "Access denied: No token provided" });
  }

  jwt.verify(token, process.env.JWT_SECRET, async (err, decoded) => {
    if (err) {
      return res.status(403).json({ error: "Invalid token", details: err.message });
    }

    // If username is missing from token (old token), fetch it from database
    if (!decoded.username && decoded.id) {
      try {
        const userResult = await pool.query('SELECT username FROM users WHERE id = $1', [decoded.id]);
        if (userResult.rows[0]) {
          decoded.username = userResult.rows[0].username;
        }
      } catch (dbError) {
        console.log('❌ Error fetching username from database:', dbError.message);
      }
    }

    // console.log('✅ Final username:', decoded.username);
    req.user = decoded;
    next();
  });
};

// Helper function to get company ID from token
const getCompanyIdFromToken = async (req) => {
  try {
    // Since the current database doesn't have proper company-user relationships,
    // we'll use a default company ID of 1 for all users
    // This is a temporary fix until the database structure is properly set up
    return 1;
  } catch (error) {
    console.error("Error getting company ID from token:", error);
    throw new Error("Failed to get company ID");
  }
};

const checkRole = (roles) => (req, res, next) => {
  if (!roles.includes(req.user.role)) {
    return res
      .status(403)
      .json({ error: `Permission denied: Role ${req.user.role} not allowed` });
  }
  next();
};

// File upload configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const eventId = req.params.eventId || req.body.eventId;
    const uploadPath = path.join(__dirname, 'uploads', 'events', eventId.toString());
    
    // Create directory if it doesn't exist
    fs.mkdirSync(uploadPath, { recursive: true });
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    // Keep original filename with timestamp prefix
    const timestamp = Date.now();
    const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8');
    cb(null, `${timestamp}_${originalName}`);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow most common file types
    const allowedMimeTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'text/csv',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/zip',
      'application/x-rar-compressed'
    ];
    
    if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('File type not allowed'), false);
    }
  }
});

// Helper functions
function generateShortCode(length = 6) {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}

async function createShortUrl(originalUrl, expiresInHours = 24) {
  try {
    let shortCode = generateShortCode();
    let isUnique = false;
    
    while (!isUnique) {
      const checkResult = await pool.query(
        "SELECT id FROM short_urls WHERE short_code = $1",
        [shortCode]
      );
      
      if (checkResult.rows.length === 0) {
        isUnique = true;
      } else {
        shortCode = generateShortCode();
      }
    }
    
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + expiresInHours);
    
    const result = await pool.query(
      "INSERT INTO short_urls (short_code, original_url, expires_at) VALUES ($1, $2, $3) RETURNING short_code",
      [shortCode, originalUrl, expiresAt]
    );
    
    return result.rows[0].short_code;
  } catch (error) {
    console.error("Error creating short URL:", error.message);
    throw error;
  }
}

const e164Regex = /^\+[1-9]\d{9,14}$/;

async function sendSmsWithShortUrl(phone, messageText, originalUrl) {
  try {
    if (!phone || !e164Regex.test(phone)) {
      console.log("Invalid phone number format:", phone);
      return { success: false, error: "Invalid phone number format" };
    }
    
    const shortCode = await createShortUrl(originalUrl);
    const shortUrl = `${API_BASE_URL}/s/${shortCode}`;
    const fullMessage = `${messageText} ${shortUrl}`;
    console.log("Short URL created:", fullMessage);

    if (!twilioClient) {
      console.log('⚠️ SMS not sent - Twilio not configured');
      return { success: false, error: 'Twilio not configured' };
    }

    const smsResult = await twilioClient.messages.create({
      body: fullMessage,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: phone,
    });
    
    console.log("SMS with short URL sent successfully:", smsResult.sid);
    return { success: true, sid: smsResult.sid };
  } catch (error) {
    console.error("Failed to send SMS with short URL:", {
      message: error.message,
      code: error.code,
      moreInfo: error.moreInfo,
      status: error.status,
    });
    return { success: false, error: error.message };
  }
}

async function notifyStaff(event, assignedIds = [], notifyAllIfUnassigned = false) {
  const launchTimestamp = new Date().toISOString();
  console.log("=== NOTIFY STAFF STARTED ===");
  console.log("Event Launch Time:", launchTimestamp);
  console.log("Event:", {
    id: event.id,
    title: event.title,
    event_type: event.event_type,
    notification_channels: event.notification_channels,
  });
  console.log("Notification params:", {
    assignedIds,
    notifyAllIfUnassigned,
  });

  try {
    let assignedIdsClean = assignedIds;

    if (typeof assignedIds === 'string') {
      try {
        assignedIdsClean = JSON.parse(assignedIds);
      } catch (e) {
        console.error("Failed to parse assignedIds string:", assignedIds);
        assignedIdsClean = [];
      }
    }

    const assignedIdsNum = Array.isArray(assignedIdsClean)
      ? assignedIdsClean.map((id) => parseInt(id, 10)).filter(Number.isFinite)
      : [];

    console.log("Parsed assigned IDs:", assignedIdsNum);
    
    if (!notifyAllIfUnassigned && assignedIdsNum.length === 0) {
      console.log("No users to notify: notifyAllIfUnassigned is false and assignedIds is empty.");
      return;
    }

    console.log("Querying users...");
    const usersResult = await pool.query(
      "SELECT email, phone, id FROM users WHERE status = 1 AND role IN ('staff', 'commander')"
    );
    const users = usersResult.rows;

    const notifyUsers = notifyAllIfUnassigned
      ? users
      : users.filter((user) => assignedIdsNum.includes(user.id));

    if (notifyUsers.length === 0) {
      console.log("No users matched to notify.");
      return;
    }

    console.log("Users to notify:", notifyUsers.length, notifyUsers.map((u) => ({
      id: u.id,
      email: u.email,
      phone: u.phone
    })));

    const calledPhones = new Set();

    for (const user of notifyUsers) {
      const userToken = jwt.sign(
        { id: user.id, role: "staff" },
        process.env.JWT_SECRET,
        { expiresIn: "30d" }
      );

      const dynamicUrl = `${API_BASE_URL}/responder-choice.html?token=${userToken}&eventId=${event.id}`;
      const message = `${event.info || "No additional info"}\n\nRespond here: ${dynamicUrl}\nEnable location permissions always for this event.`;
      const htmlContent = `
        <p>${event.info || "No additional info"}</p>
        <p><a href="${dynamicUrl}" target="_blank">Respond here</a></p>
        <p><strong>Note:</strong> Enable location permissions always for this event.</p>
      `;

      // Determine which notification channels to use
      const notificationChannels = event.notification_channels || ["web_app"];
      const channels = Array.isArray(notificationChannels) ? notificationChannels :
                      (typeof notificationChannels === 'string' ? JSON.parse(notificationChannels) : ["web_app"]);

      const userNotificationTime = new Date().toISOString();
      console.log(`Processing user ${user.id} at ${userNotificationTime}:`, {
        email: user.email,
        phone: user.phone,
        channels: channels,
      });

      // Send Email (if email channel is enabled)
      if (channels.includes("email")) {
        console.log(`📧 SENDING EMAIL to ${user.email}`);
        console.log("Email content:", {
          subject: `New Event: ${event.title}`,
          html: htmlContent
        });
        try {
          const emailResult = await transporter.sendMail({
            from: process.env.EMAIL_FROM,
            to: user.email,
            subject: `New Event: ${event.title}`,
            html: htmlContent,
          });
          console.log(`✅ Email sent successfully to ${user.email} at ${new Date().toISOString()}:`, emailResult.messageId);
        } catch (emailError) {
          console.error(`❌ Email error for ${user.email}:`, emailError.message);
        }
      } else {
        console.log(`⏭️ Skipping email for ${user.email} - email not in channels:`, channels);
      }

      // Send SMS and Call if phone exists and hasn't been called yet
      if (user.phone && !calledPhones.has(user.phone)) {
        // Send SMS if SMS channel is enabled
        if (channels.includes("sms")) {
          console.log(`📱 SENDING SMS to ${user.phone}`);
          try {
            const smsResult = await sendSmsWithShortUrl(
              user.phone,
              `Respond Link: `,
              dynamicUrl
            );
            console.log(`✅ SMS sent successfully to ${user.phone} at ${new Date().toISOString()}`);
          } catch (smsError) {
            console.error(`❌ SMS error for ${user.phone} at ${new Date().toISOString()}:`, smsError.message);
          }
        } else {
          console.log(`⏭️ Skipping SMS for ${user.phone} - sms not in channels:`, channels);
        }

        // Send Voice Call if voice_call channel is enabled
        if (channels.includes("voice_call")) {
          console.log(`📞 SENDING VOICE CALL to ${user.phone}`);
          console.log("Voice call content:", `New event: ${event.title}. ${event.info || "No additional info"}`);
          try {
            if (!twilioClient) {
              console.log(`⚠️ Voice call not sent to ${user.phone} - Twilio not configured`);
              continue;
            }

            const callResult = await twilioClient.calls.create({
              twiml: `<Response><Say>New event: ${event.title}. ${event.info || "No additional info"}</Say></Response>`,
              from: process.env.TWILIO_PHONE_NUMBER,
              to: user.phone,
            });
            console.log(`✅ Voice call initiated successfully to ${user.phone} at ${new Date().toISOString()}, SID: ${callResult.sid}`);
            calledPhones.add(user.phone);
          } catch (callError) {
            console.error(`❌ Voice call error for ${user.phone} at ${new Date().toISOString()}:`, callError.message);
          }
        } else {
          console.log(`⏭️ Skipping voice call for ${user.phone} - voice_call not in channels:`, channels);
        }
      } else if (!user.phone) {
        console.log(`No phone number for ${user.email}`);
      } else {
        console.log(`⏭️ Skipping duplicate notifications to ${user.phone}`);
      }
      // Web App notification (always enabled for now)
      if (channels.includes("web_app")) {
        console.log(`🌐 WEB APP notification enabled for user ${user.id}`);
        // Web app notifications are handled via WebSocket (io.emit) and database storage
        // This happens automatically when the event is created
      } else {
        console.log(`⏭️ Skipping web app notification for user ${user.id} - web_app not in channels:`, channels);
      }
    }

    const completionTimestamp = new Date().toISOString();
    console.log("=== NOTIFY STAFF COMPLETED ===");
    console.log(`Event Launch Completion Time: ${completionTimestamp}`);
    console.log(`Total users notified: ${notifyUsers.length}`);
    console.log(`Event ID: ${event.id}, Title: ${event.title}`);
  } catch (error) {
    console.error("Error in notifyStaff:", error.message, error.stack);
  }
}

// AI Helper Functions
function generateMockSummary(chatText) {
  const lines = chatText.split('\n').filter(line => line.trim());
  const participants = [...new Set(lines.map(line => line.split(':')[0]))];
  const keyMessages = lines.slice(-5).map(line => `• ${line}`).join('\n');
  
  return `Emergency Chat Summary:

Participants: ${participants.join(', ')}
Total Messages: ${lines.length}

Key Recent Communications:
${keyMessages}

Summary: Active coordination ongoing with ${participants.length} participants. Recent focus on status updates and resource coordination.

Note: This is a basic summary. Full AI analysis requires OpenAI API integration.`;
}

function generateMockActions(userRole, eventType, eventTitle) {
  const baseActions = [
    "Review event details and location information",
    "Check in with your team or supervisor", 
    "Confirm your participation status"
  ];

  const roleActions = {
    commander: [
      "Establish incident command structure",
      "Coordinate with other emergency services", 
      "Monitor overall response progress",
      "Authorize resource deployment",
      "Conduct safety briefings"
    ],
    lead: [
      "Brief your team on specific assignments",
      "Maintain communication with incident command",
      "Monitor team member safety and status",
      "Report progress to command structure",
      "Coordinate with other team leads"
    ],
    staff: [
      "Follow your team leader's instructions",
      "Report any safety concerns immediately",
      "Maintain situational awareness", 
      "Assist team members as needed",
      "Document any important observations"
    ]
  };

  const eventTypeActions = {
    notification_only: [
      "Acknowledge receipt of notification",
      "Stay available for potential activation",
      "Monitor communication channels",
      "Review standard operating procedures"
    ],
    response: [
      "Prepare necessary equipment",
      "Confirm transportation to event location", 
      "Review route and safety considerations",
      "Check in upon arrival at scene"
    ]
  };

  const actions = [
    ...baseActions,
    ...(roleActions[userRole] || roleActions.staff),
    ...(eventTypeActions[eventType] || eventTypeActions.response)
  ];

  return actions;
}

// AI API Routes (Removed duplicate - using the one below)

// Routes
app.get("/", (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

app.get("/responder.html", (req, res) => {
  res.sendFile(path.join(__dirname, 'responder.html'));
});

app.get("/responder-choice.html", (req, res) => {
  res.sendFile(path.join(__dirname, 'responder-choice.html'));
});

app.get("/login.html", (req, res) => {
  res.sendFile(path.join(__dirname, 'login.html'));
});

app.get("/signup.html", (req, res) => {
  res.sendFile(path.join(__dirname, 'signup.html'));
});

app.get("/dashboard.html", (req, res) => {
  res.sendFile(path.join(__dirname, 'dashboard.html'));
});

app.get("/", (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

app.get("/index.html", (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

// User profile route
app.get("/user/profile", authenticateToken, async (req, res) => {
  try {
    const result = await pool.query(
      "SELECT id, username, email, role, first_name, last_name, job_role, main_location, phone, city, state, zip FROM users WHERE id = $1",
      [req.user.id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: "User not found" });
    }

    const user = result.rows[0];
    res.json({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      firstName: user.first_name,
      lastName: user.last_name,
      jobRole: user.job_role,
      mainLocation: user.main_location,
      phone: user.phone,
      city: user.city,
      state: user.state,
      zip: user.zip
    });
  } catch (error) {
    console.error("Error fetching user profile:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Update user profile
app.put("/user/profile", authenticateToken, async (req, res) => {
  try {
    const { firstName, lastName, email, phone, city, state, zip } = req.body;

    const result = await pool.query(
      "UPDATE users SET first_name = $1, last_name = $2, email = $3, phone = $4, city = $5, state = $6, zip = $7 WHERE id = $8 RETURNING *",
      [firstName, lastName, email, phone, city, state, zip, req.user.id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: "User not found" });
    }

    const user = result.rows[0];
    res.json({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      firstName: user.first_name,
      lastName: user.last_name,
      jobRole: user.job_role,
      mainLocation: user.main_location,
      phone: user.phone,
      city: user.city,
      state: user.state,
      zip: user.zip
    });
  } catch (error) {
    console.error("Error updating user profile:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Get chat messages for an event
app.get("/events/:eventId/chat", authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params;

    const result = await pool.query(
      "SELECT cm.*, u.username, u.first_name, u.last_name FROM chat_messages cm " +
      "JOIN users u ON cm.user_id = u.id " +
      "WHERE cm.event_id = $1 " +
      "ORDER BY cm.timestamp ASC",
      [eventId]
    );

    res.json(result.rows);
  } catch (error) {
    console.error("Error fetching chat messages:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Send chat message
app.post("/events/:eventId/chat", authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params;
    const { message } = req.body;

    const result = await pool.query(
      "INSERT INTO chat_messages (event_id, user_id, message, timestamp) VALUES ($1, $2, $3, NOW()) RETURNING *",
      [eventId, req.user.id, message]
    );

    const messageData = result.rows[0];

    // Get user info for the message
    const userResult = await pool.query(
      "SELECT username, first_name, last_name FROM users WHERE id = $1",
      [req.user.id]
    );

    const responseData = {
      ...messageData,
      username: userResult.rows[0].username,
      first_name: userResult.rows[0].first_name,
      last_name: userResult.rows[0].last_name
    };

    res.json(responseData);
  } catch (error) {
    console.error("Error sending chat message:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Get notifications for user
app.get("/notifications", authenticateToken, async (req, res) => {
  try {
    const result = await pool.query(
      "SELECT * FROM notifications WHERE user_id = $1 ORDER BY created_at DESC LIMIT 50",
      [req.user.id]
    );

    res.json(result.rows);
  } catch (error) {
    console.error("Error fetching notifications:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Mark notification as read
app.put("/notifications/:notificationId/read", authenticateToken, async (req, res) => {
  try {
    const { notificationId } = req.params;

    await pool.query(
      "UPDATE notifications SET is_read = true WHERE id = $1 AND user_id = $2",
      [notificationId, req.user.id]
    );

    res.json({ success: true });
  } catch (error) {
    console.error("Error marking notification as read:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Authentication routes
app.post("/signup", async (req, res) => {
  const {
    username,
    password,
    email,
    main_location,
    phone,
    first_name,
    last_name,
    job_role,
    home_address,
    city,
    state,
    zip,
  } = req.body;
  
  if (!username || !password || !email || !main_location) {
    return res.status(400).json({
      error: "Missing required fields: username, password, email, main_location",
    });
  }
  
  const role = 'staff';
  try {
    const hashedPassword = await bcrypt.hash(password, 10);
    const result = await pool.query(
      "INSERT INTO users (username, password, email, role, main_location, phone, first_name, last_name, job_role, home_address, city, state, zip) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13) RETURNING id, role",
      [
        username,
        hashedPassword,
        email,
        role,
        main_location,
        phone || "",
        first_name || "",
        last_name || "",
        job_role || "",
        home_address || "",
        city || "",
        state || "",
        zip || "",
      ]
    );
    const user = result.rows[0];
    const token = jwt.sign(
      { id: user.id, role: user.role, username: user.username },
      process.env.JWT_SECRET,
      { expiresIn: "1h" }
    );
    res.json({ token });
  } catch (error) {
    console.error("Signup error:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.post("/user-store", async (req, res) => {
  const {
    username,
    password,
    role,
    email,
    main_location,
    phone,
    first_name,
    last_name,
    job_role,
    home_address,
    city,
    state,
    zip,
    company_code,
  } = req.body;

  if (!company_code || !username || !password || !role || !email || !main_location) {
    return res.status(400).json({
      error: "Missing required fields: username, password, role, email, main_location, company_code",
    });
  }

  try {
    // Find company_id from company_code
    const companyResult = await pool.query(
      "SELECT id FROM companies WHERE code = $1 LIMIT 1",
      [company_code]
    );

    if (companyResult.rowCount === 0) {
      return res.status(400).json({ error: "Invalid company code provided." });
    }

    const company_id = companyResult.rows[0].id;

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Generate a 6-digit OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString();

    // Insert into users table using company_id
    await pool.query(
      `INSERT INTO users 
       (username, password, email, role, main_location, phone, first_name, last_name, job_role, home_address, city, state, zip, otp_code, company_id) 
       VALUES 
       ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)`,
      [
        username,
        hashedPassword,
        email,
        role,
        main_location,
        phone || "",
        first_name || "",
        last_name || "",
        job_role || "",
        home_address || "",
        city || "",
        state || "",
        zip || "",
        otp,
        company_id,
      ]
    );

    if (!phone || !e164Regex.test(phone)) {
      return res.status(200).json({
        message: "User added, but phone number is invalid or missing. OTP not sent.",
      });
    }

    // Create verification URL and send SMS
    const verificationUrl = `${API_BASE_URL}/verify?otp=${otp}&username=${encodeURIComponent(username)}`;

    const smsResult = await sendSmsWithShortUrl(
      phone,
      `Your AlertComm verification code is: ${otp}. Or use this link to verify:`,
      verificationUrl
    );

    if (!smsResult.success) {
      console.error("Failed to send OTP SMS:", smsResult.error);
    }

    res.status(200).json({ message: "User added and OTP sent successfully." });
  } catch (error) {
    console.error("Add user error:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.post("/verify-otp", async (req, res) => {
  const { username, otp } = req.body;
  
  if (!username || !otp) {
    return res.status(400).json({ error: "Username and OTP are required." });
  }

  try {
    const result = await pool.query(
      "SELECT id, role, otp_code FROM users WHERE username = $1",
      [username]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: "User not found." });
    }

    const user = result.rows[0];

    if (user.otp_code !== otp) {
      return res.status(401).json({ error: "Invalid OTP." });
    }

    // Clear OTP and set status to 1
    await pool.query(
      "UPDATE users SET otp_code = NULL, status = 1 WHERE id = $1",
      [user.id]
    );

    // Generate JWT token
    const token = jwt.sign(
      { id: user.id, role: user.role, username: user.username },
      process.env.JWT_SECRET,
      { expiresIn: "24h" }
    );

    res.status(200).json({ message: "OTP verified successfully", token });
  } catch (error) {
    console.error("OTP verification error:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.post("/login", async (req, res) => {
  const { username, password } = req.body;
  
  try {
    const result = await pool.query("SELECT * FROM users WHERE username = $1", [
      username,
    ]);
    const user = result.rows[0];
    if (!user) {
      return res.status(401).json({ error: "Invalid credentials" });
    }
    const match = await bcrypt.compare(password, user.password);
    if (!match) {
      return res.status(401).json({ error: "Invalid credentials" });
    }
    const token = jwt.sign(
      { id: user.id, role: user.role, username: user.username },
      process.env.JWT_SECRET,
      { expiresIn: "24h" }
    );
    res.json({ token });
  } catch (error) {
    console.error("Login error:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Company routes
app.post("/user/store-company", authenticateToken, async (req, res) => {
  const { name, description, code, user_id } = req.body;
  
  if (!name || !code || !user_id) {
    return res.status(400).json({
      error: "Missing required fields: name, code, user_id"
    });
  }
  
  try {
    let baseSlug = name.toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '')
      .substring(0, 15);
    
    let slug = baseSlug;
    let isUnique = false;
    let counter = 1;
    
    while (!isUnique) {
      const checkResult = await pool.query(
        "SELECT id FROM companys WHERE slug = $1",
        [slug]
      );

      if (checkResult.rows.length === 0) {
        isUnique = true;
      } else {
        const counterStr = `-${counter}`;
        slug = baseSlug.substring(0, 15 - counterStr.length) + counterStr;
        counter++;
      }
    }

    const result = await pool.query(
      "INSERT INTO companys (name, slug, description, code, user_id, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP) RETURNING *",
      [name, slug, description || "", code, user_id]
    );
    
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error("Error creating company:", error.message);
    
    if (error.code === '23505' && error.constraint === 'companys_code_key') {
      return res.status(400).json({ error: "Company code already exists" });
    }
    
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.get("/user/company", authenticateToken, async (req, res) => {
  try {
    const userId = req.query.user_id || req.user.id;

    // Fetch companies from the database for the specific user
    const result = await pool.query(
      "SELECT id, name, slug, description, code, user_id, created_at, updated_at FROM companys WHERE user_id = $1 ORDER BY created_at DESC",
      [userId]
    );

    res.status(200).json(result.rows);
  } catch (error) {
    console.error("Error fetching companies:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.delete("/user/delete-company/:company_id", authenticateToken, async (req, res) => {
  const { company_id } = req.params;
  const { user_id } = req.query;

  if (!company_id || !user_id) {
    return res.status(400).json({
      error: "Missing required parameters: company_id and/or user_id"
    });
  }

  try {
    const result = await pool.query(
      "DELETE FROM companys WHERE id = $1 AND user_id = $2 RETURNING *",
      [company_id, user_id]
    );

    if (result.rowCount === 0) {
      return res.status(404).json({ error: "Company not found or not authorized" });
    }

    res.status(200).json({ message: "Company deleted successfully", deleted: result.rows[0] });
  } catch (error) {
    console.error("Error deleting company:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// User management routes
app.post("/add-user", authenticateToken, checkRole(["commander"]), async (req, res) => {
  const {
    username,
    password,
    role,
    email,
    main_location,
    phone,
    first_name,
    last_name,
    job_role,
    home_address,
    city,
    state,
    zip,
  } = req.body;
  
  if (!username || !password || !role || !email || !main_location) {
    return res.status(400).json({
      error: "Missing required fields: username, password, role, email, main_location",
    });
  }
  
  try {
    const hashedPassword = await bcrypt.hash(password, 10);
    await pool.query(
      "INSERT INTO users (username, password, email, role, main_location, phone, first_name, last_name, job_role, home_address, city, state, zip) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)",
      [
        username,
        hashedPassword,
        email,
        role,
        main_location,
        phone || "",
        first_name || "",
        last_name || "",
        job_role || "",
        home_address || "",
        city || "",
        state || "",
        zip || "",
      ]
    );
    res.json({ message: "User added successfully" });
  } catch (error) {
    console.error("Add user error:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.put("/edit-user/:id", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  const { id } = req.params;
  const {
    username,
    password,
    role,
    email,
    main_location,
    phone,
    first_name,
    last_name,
    job_role,
    home_address,
    city,
    state,
    zip,
  } = req.body;
  
  if (!username || !role || !email || !main_location) {
    return res.status(400).json({
      error: "Missing required fields: username, role, email, main_location",
    });
  }
  
  try {
    let query = "UPDATE users SET username = $1, role = $2, email = $3, main_location = $4, phone = $5, first_name = $6, last_name = $7, job_role = $8, home_address = $9, city = $10, state = $11, zip = $12";
    const values = [
      username,
      role,
      email,
      main_location,
      phone || "",
      first_name || "",
      last_name || "",
      job_role || "",
      home_address || "",
      city || "",
      state || "",
      zip || "",
    ];
    
    if (password) {
      const hashedPassword = await bcrypt.hash(password, 10);
      query += ", password = $13";
      values.push(hashedPassword);
    }
    
    query += " WHERE id = $" + (values.length + 1) + " RETURNING *";
    values.push(id);

    const result = await pool.query(query, values);
    if (result.rows.length === 0) {
      return res.status(404).json({ error: "User not found" });
    }
    
    res.json({ message: "User updated successfully", user: result.rows[0] });
  } catch (error) {
    console.error("Error updating user:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.get("/users", authenticateToken, checkRole(["commander"]), async (req, res) => {
  try {
    const result = await pool.query(
      "SELECT id, username, role, email, main_location, phone, first_name, last_name, job_role, home_address, city, state, status, zip FROM users"
    );
    res.json(result.rows);
  } catch (error) {
    console.error("Error fetching users:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.delete("/delete-user/:id", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  const { id } = req.params;
  try {
    // Begin transaction
    await pool.query('BEGIN');
    
    try {
      // Delete notifications
      await pool.query(
        "DELETE FROM notifications WHERE responder_id = $1",
        [id]
      );
      
      // Delete responders
      await pool.query(
        "DELETE FROM responders WHERE id = $1",
        [id]
      );
      
      // Update tasks
      await pool.query(
        "UPDATE tasks SET assigned_to = NULL WHERE assigned_to = $1",
        [id]
      );
      
      // Update events
      await pool.query(
        "UPDATE events SET created_by = NULL WHERE created_by = $1",
        [id]
      );
      
      // Delete user
      const result = await pool.query(
        "DELETE FROM users WHERE id = $1 RETURNING *",
        [id]
      );
      
      // Commit transaction
      await pool.query('COMMIT');
      
      res.json({ message: "User deleted successfully" });
    } catch (innerError) {
      // Rollback on error
      await pool.query('ROLLBACK');
      throw innerError;
    }
  } catch (error) {
    console.error("Error deleting user:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Event routes
app.post("/events", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  console.log("=== EVENT CREATION REQUEST ===");
  console.log("Request body:", JSON.stringify(req.body, null, 2));
  console.log("User:", req.user);

  const {
    title,
    info,
    scale,
    urgency,
    location,
    description,
    included_report_to_locations,
    assignedIds,
    notifyAllIfUnassigned,
    module,
    customFields,
    event_type,
    location_update_interval,
    notification_channels,
    event_documents,
  } = req.body;

  console.log("Extracted fields:");
  console.log("- title:", title);
  console.log("- event_type:", event_type);
  console.log("- notification_channels:", notification_channels);
  console.log("- location_update_interval:", location_update_interval);
  console.log("- assignedIds:", assignedIds);
  console.log("- info:", info);
  console.log("- location:", location);

  const created_by = req.user.id;

  try {
    const eventData = {
      title,
      info,
      scale,
      urgency,
      location: location || {
        commonName: "",
        address: "",
        city: "",
        state: "",
        zip: "",
      },
      description: description || "",
      status: "open",
      created_by,
      included_report_to_locations: included_report_to_locations || [],
      assignedIds: assignedIds || [],
      notifyAllIfUnassigned: notifyAllIfUnassigned || false,
      module,
      customFields,
      event_type: event_type || "response",
      location_update_interval: location_update_interval || 60,
      notification_channels: notification_channels || ["web_app"],
      event_documents: event_documents || [],
    };
    
    const event = await db.createEvent(eventData);

    // Log event creation
    console.log(`🚨 EVENT LAUNCHED: ID=${event.id}, Title="${event.title}", Created by User ID=${req.user.id} at ${new Date().toISOString()}`);

    await notifyStaff(event, eventData.assignedIds??[], eventData.notifyAllIfUnassigned);

    // Emit new event to WebSocket clients
    io.emit("new-event", {
      id: event.id,
      title: event.title,
      status: event.status,
    });

    res.status(201).json(event);
  } catch (error) {
    console.error("Error creating event:", error.message, error.stack);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.get("/all-active-events", authenticateToken, checkRole(["commander", "lead", "staff"]), async (req, res) => {
  try {
    const result = await pool.query(
      "SELECT e.*, " +
        "json_agg(json_build_object('id', t.id, 'assigned_to', t.assigned_to, 'report_to_location', t.report_to_location, 'status', t.status)) AS tasks " +
        "FROM events e " +
        "LEFT JOIN tasks t ON e.id = t.event_id " +
        "WHERE e.status != $1 " +
        "GROUP BY e.id " +
        "ORDER BY e.created_at DESC",
      ["resolved"]
    );
    
    const events = result.rows.map((row) => ({
      ...row,
      tasks: row.tasks.filter((task) => task.id !== null),
    }));
    
    res.json(events);
  } catch (error) {
    console.error("Error in /all-active-events:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.get("/active-events/:id", authenticateToken, checkRole(["commander", "lead", "staff"]), async (req, res) => {
  try {
    const eventId = parseInt(req.params.id, 10);
    
    if (isNaN(eventId)) {
      return res.status(400).json({ error: "Invalid event ID: must be a number" });
    }
    
    const result = await pool.query(
      "SELECT e.*, t.id AS task_id, t.assigned_to, t.report_to_location, t.status FROM events e LEFT JOIN tasks t ON e.id = t.event_id WHERE e.id = $1 AND e.status != $2",
      [eventId, "resolved"]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: "Event not found or already resolved" });
    }

    const event = {
      id: result.rows[0].id,
      title: result.rows[0].title,
      info: result.rows[0].info,
      scale: result.rows[0].scale,
      urgency: result.rows[0].urgency,
      location: result.rows[0].location,
      description: result.rows[0].description,
      status: result.rows[0].status,
      created_by: result.rows[0].created_by,
      created_at: result.rows[0].created_at,
      event_type: result.rows[0].event_type,
      included_report_to_locations: result.rows[0].included_report_to_locations,
      tasks: [],
      assignIds: result.rows[0].assigned_ids,
      notifyAll: result.rows[0].notify_all_if_unassigned,
    };

    result.rows.forEach((row) => {
      if (row.task_id) {
        event.tasks.push({
          id: row.task_id,
          assigned_to: row.assigned_to,
          report_to_location: row.report_to_location,
          status: row.status,
        });
      }
    });

    res.json(event);
  } catch (error) {
    console.error("Error in /active-events/:id:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.put("/event/:id", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  const { status } = req.body;
  const eventId = req.params.id;
  
  if (!status) {
    return res.status(400).json({ error: "Status is required" });
  }
  
  try {
    const result = await pool.query(
      "UPDATE events SET status = $1 WHERE id = $2 RETURNING *",
      [status, eventId]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: "Event not found" });
    }
    
    const event = result.rows[0];
    io.emit("new-event", event);
    res.json(event);
  } catch (error) {
    console.error("Error in /event/:id PUT:", error.message, error.stack);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.delete("/events/:id", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  const { id } = req.params;
  const eventId = parseInt(id, 10);

  if (isNaN(eventId)) {
    return res.status(400).json({ error: "Invalid event ID: must be a number" });
  }

  try {
    // Delete notifications first
    await pool.query("DELETE FROM notifications WHERE event_id = $1", [eventId]);

    // Then delete the event
    const result = await pool.query(
      "DELETE FROM events WHERE id = $1 RETURNING *",
      [eventId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: `Event ${eventId} not found` });
    }

    // Emit event deletion
    io.emit("event-deleted", { id: eventId });

    res.json({ message: "Event deleted successfully", event: result.rows[0] });
  } catch (error) {
    console.error("Error deleting event:", error.message, error.stack);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Task routes
app.put("/tasks", authenticateToken, checkRole(["commander", "lead", "staff"]), async (req, res) => {
  const { event_id, assigned_to, status, report_to_location } = req.body;
  
  if (!event_id || !assigned_to || !report_to_location) {
    return res.status(400).json({
      error: "Missing required fields: event_id, assigned_to, report_to_location",
    });
  }
  
  try {
    const result = await pool.query(
      "UPDATE tasks SET report_to_location = $1, status = COALESCE($2, status) WHERE event_id = $3 AND assigned_to = $4 RETURNING *",
      [report_to_location, status, event_id, assigned_to]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: "Task not found" });
    }
    
    const updatedTask = result.rows[0];
    io.to(`event-${event_id}`).emit("task-response", updatedTask);
    res.json(updatedTask);
  } catch (error) {
    console.error("Error updating task:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.post("/tasks", authenticateToken, checkRole(["commander", "lead", "staff"]), async (req, res) => {
  const { event_id, assigned_to, status } = req.body;
  
  if (!event_id || !assigned_to) {
    return res.status(400).json({
      error: "Missing required fields: event_id, assigned_to",
    });
  }
  
  try {
    const eventIdNum = parseInt(event_id, 10);
    
    if (isNaN(eventIdNum)) {
      return res.status(400).json({ error: "Invalid event_id: must be a number" });
    }
    
    const eventResult = await pool.query(
      "SELECT * FROM events WHERE id = $1",
      [eventIdNum]
    );
    
    const event = eventResult.rows[0];
    if (!event) {
      return res.status(404).json({ error: `Event ${eventIdNum} not found` });
    }

    const locationsWithStaff = event.included_report_to_locations
      ? event.included_report_to_locations.map((loc) => ({
          location: `${loc.address}, ${loc.city}, ${loc.state} ${loc.zip}`.trim(),
          staffNeeded: loc.staffNeeded || 2,
          assignedCount: 0,
        }))
      : [
          {
            location: event.location || "Unknown",
            staffNeeded: 2,
            assignedCount: 0,
          },
        ];
    
    const primaryLoc = locationsWithStaff.find((loc) => loc.location === event.location) || locationsWithStaff[0];

    const existingTask = await pool.query(
      "SELECT * FROM tasks WHERE event_id = $1 AND assigned_to = $2",
      [eventIdNum, assigned_to]
    );
    
    let task;

    if (existingTask.rows.length > 0) {
      const result = await pool.query(
        "UPDATE tasks SET status = $1, report_to_location = COALESCE(report_to_location, $2) WHERE event_id = $3 AND assigned_to = $4 RETURNING *",
        [status || "pending", primaryLoc.location, eventIdNum, assigned_to]
      );
      task = result.rows[0];
    } else {
      const result = await pool.query(
        "INSERT INTO tasks (event_id, assigned_to, status, report_to_location) VALUES ($1, $2, $3, $4) RETURNING *",
        [eventIdNum, assigned_to, status || "pending", primaryLoc.location]
      );
      task = result.rows[0];
    }

    if (status !== "unable") {
      const responderResult = await pool.query(
        "SELECT latitude, longitude FROM responders WHERE id = $1",
        [assigned_to]
      );
      
      const responder = responderResult.rows[0];
      if (responder && responder.latitude && responder.longitude) {
        const userLocation = `${responder.latitude},${responder.longitude}`;
        
        try {
          const apiKey = "AIzaSyBigR0OaTpsLIIFuUJphgtMgWB7PMDDm7k";
          const url = `https://maps.googleapis.com/maps/api/distancematrix/json?origins=${encodeURIComponent(
            userLocation
          )}&destinations=${encodeURIComponent(
            locationsWithStaff.map((l) => l.location).join("|")
          )}&key=${apiKey}&units=imperial`;
          
          const response = await fetch(url, {
            headers: {
              "User-Agent":
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            },
          });
          
          const data = await response.json();

          if (data.status === "OK") {
            const distances = data.rows[0].elements;
            let closestLoc = null;
            let minDuration = Infinity;
            
            for (let i = 0; i < distances.length; i++) {
              if (distances[i].status === "OK") {
                const duration = distances[i].duration.value / 60;
                if (duration < minDuration) {
                  closestLoc = locationsWithStaff[i];
                  minDuration = duration;
                }
              }
            }
            
            if (closestLoc) {
              const updateResult = await pool.query(
                "UPDATE tasks SET report_to_location = $1 WHERE id = $2 RETURNING *",
                [closestLoc.location, task.id]
              );
              task = updateResult.rows[0];
            }
          }
        } catch (distanceError) {
          console.error("Distance Matrix error:", distanceError.message);
        }
      }
    }

    io.to(`user-${assigned_to}`).emit("task-assigned", { task, event });
    io.to(`event-${eventIdNum}`).emit("task-response", task);
    res.json(task);
  } catch (error) {
    console.error("Error in /tasks:", error.message, error.stack);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Responder routes
app.get("/responders", authenticateToken, checkRole(["commander", "lead", "staff"]), async (req, res) => {
  try {
    // Handle both 'ids' and 'ids[]' query parameters
    let ids = req.query.ids || req.query['ids[]'];

    // Ensure ids is an array
    if (ids && !Array.isArray(ids)) {
      ids = [ids];
    }

    console.log("Responders endpoint - received ids:", ids);

    let result;
    const baseQuery =
      "SELECT u.id, u.username, u.role, u.first_name, u.last_name, u.job_role, u.main_location, r.latitude, r.longitude " +
      "FROM users u LEFT JOIN responders r ON u.id = r.id " +
      "WHERE u.status = 1 AND u.role IN ($1, $2)";

    if (Array.isArray(ids) && ids.length > 0) {
      const placeholders = ids.map((_, i) => `$${i + 3}`).join(", ");
      const query = `${baseQuery} AND u.id IN (${placeholders})`;
      const params = ["staff", "commander", ...ids];
      console.log("Responders query with IDs:", query, params);
      result = await pool.query(query, params);
    } else {
      console.log("Responders query without IDs - returning all responders");
      result = await pool.query(baseQuery, ["staff", "commander"]);
    }

    console.log("Responders result count:", result.rows.length);
    res.json(result.rows);
  } catch (error) {
    console.error("Error in /responders:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.post("/responder/location", authenticateToken, checkRole(["lead", "staff"]), async (req, res) => {
  const { latitude, longitude } = req.body;
  
  if (!latitude || !longitude) {
    return res.status(400).json({ error: "Missing coordinates: latitude, longitude" });
  }
  
  try {
    const result = await pool.query(
      "INSERT INTO responders (id, latitude, longitude, status, last_updated) VALUES ($1, $2, $3, $4, NOW()) ON CONFLICT (id) DO UPDATE SET latitude = $2, longitude = $3, status = $4, last_updated = NOW() RETURNING *",
      [req.user.id, latitude, longitude, "available"]
    );
    
    const responder = result.rows[0];
    io.emit("responder-update", responder);
    res.json(responder);
  } catch (error) {
    console.error("Error in /responder/location:", error.message, error.stack);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// AI Routes
app.post('/ai/summarize-chat', authenticateToken, async (req, res) => {
  try {
    const { eventId, chatText } = req.body;

    if (!chatText || chatText.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No chat text provided',
        code: 'MISSING_CHAT_TEXT'
      });
    }

    // Check if OpenAI client is available
    if (!openai) {
      console.log("OpenAI client not available, using mock summary");
      const summary = generateMockSummary(chatText);
      return res.json({
        success: true,
        summary: summary,
        eventId: eventId,
        timestamp: new Date().toISOString(),
        note: "AI summarization requires OpenAI API configuration"
      });
    }

    console.log("Using OpenAI for chat summarization");

    // Use OpenAI to summarize the chat
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are an AI assistant helping emergency responders. Summarize the following chat messages from an emergency response event. Focus on key decisions, status updates, resource needs, and action items. Keep the summary concise but comprehensive."
        },
        {
          role: "user",
          content: `Please summarize this emergency response chat:\n\n${chatText}`
        }
      ],
      max_tokens: 300,
      temperature: 0.3,
    });

    const summary = completion.choices[0].message.content;

    res.json({
      success: true,
      summary: summary,
      eventId: eventId,
      timestamp: new Date().toISOString(),
      note: "Generated using OpenAI GPT-3.5-turbo"
    });

  } catch (error) {
    console.error('Error generating chat summary:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate chat summary',
      code: 'SUMMARIZATION_FAILED'
    });
  }
});

// AI Document Summarization Route
app.post("/ai/summarize-document", authenticateToken, async (req, res) => {
  const { eventId, documentName, documentUrl } = req.body;

  if (!eventId || !documentName) {
    return res.status(400).json({ error: "Missing required fields: eventId, documentName" });
  }

  try {
    // Check if summary already exists for this user and document
    const existingSummary = await pool.query(
      "SELECT summary, checklist_items FROM document_summaries WHERE event_id = $1 AND user_id = $2 AND document_name = $3",
      [eventId, req.user.id, documentName]
    );

    if (existingSummary.rows.length > 0) {
      return res.json({
        success: true,
        summary: existingSummary.rows[0].summary,
        checklist_items: existingSummary.rows[0].checklist_items || [],
        documentName: documentName,
        timestamp: new Date().toISOString(),
        note: "Retrieved existing user-specific summary"
      });
    }

    // Check if OpenAI client is available
    if (!openai) {
      console.log("OpenAI client not available, using mock summary");
      const mockSummary = `Document Summary for "${documentName}":

• This document contains important information related to the emergency response
• Key procedures and protocols are outlined for responder reference
• Contact information and resource allocation details are included
• Safety guidelines and operational instructions are provided
• This summary was generated without AI analysis - full analysis requires OpenAI API integration`;

      const mockChecklist = [
        "Review document content thoroughly",
        "Identify key contact information",
        "Note important procedures and protocols",
        "Check safety guidelines compliance",
        "Verify resource requirements"
      ];

      // Store the mock summary and checklist for this user
      await pool.query(
        "INSERT INTO document_summaries (event_id, user_id, document_name, summary, checklist_items) VALUES ($1, $2, $3, $4, $5)",
        [eventId, req.user.id, documentName, mockSummary, JSON.stringify(mockChecklist)]
      );

      return res.json({
        success: true,
        summary: mockSummary,
        checklist_items: mockChecklist,
        documentName: documentName,
        timestamp: new Date().toISOString(),
        note: "AI document analysis requires OpenAI API configuration"
      });
    }

    console.log("Using OpenAI for document summarization");

    // Get AI prompts from company settings
    const companyId = await getCompanyIdFromToken(req);
    const settingsResult = await pool.query(
      "SELECT ai_prompts FROM company_settings WHERE company_id = $1",
      [companyId]
    );

    let summaryPrompt = "You are an AI assistant helping emergency responders analyze documents. Provide a concise summary of the document focusing on key information relevant to emergency response, including procedures, contacts, resources, and safety guidelines. Format the summary with bullet points for easy reading.";

    if (settingsResult.rows.length > 0 && settingsResult.rows[0].ai_prompts?.document_summary) {
      summaryPrompt = settingsResult.rows[0].ai_prompts.document_summary;
    }

    // Generate summary
    const summaryCompletion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: summaryPrompt
        },
        {
          role: "user",
          content: `Please analyze and summarize this emergency response document: "${documentName}". Focus on actionable information, key procedures, important contacts, resource requirements, and safety considerations.`
        }
      ],
      max_tokens: 400,
      temperature: 0.3,
    });

    const summary = summaryCompletion.choices[0].message.content;

    // Generate checklist based on the summary
    const checklistCompletion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are an AI assistant that creates actionable checklists for emergency responders. Based on the document summary provided, create a list of 5-8 specific action items that responders should complete. Return only the checklist items as a JSON array of strings."
        },
        {
          role: "user",
          content: `Based on this document summary, create a checklist of action items for emergency responders:\n\n${summary}\n\nReturn only a JSON array of strings, each representing a specific action item.`
        }
      ],
      max_tokens: 300,
      temperature: 0.3,
    });

    let checklistItems = [];
    try {
      checklistItems = JSON.parse(checklistCompletion.choices[0].message.content);
    } catch (parseError) {
      console.error("Error parsing checklist JSON:", parseError);
      // Fallback checklist
      checklistItems = [
        "Review document content thoroughly",
        "Identify key contact information",
        "Note important procedures and protocols",
        "Check safety guidelines compliance",
        "Verify resource requirements"
      ];
    }

    // Store the summary and checklist for this user
    await pool.query(
      "INSERT INTO document_summaries (event_id, user_id, document_name, summary, checklist_items) VALUES ($1, $2, $3, $4, $5)",
      [eventId, req.user.id, documentName, summary, JSON.stringify(checklistItems)]
    );

    res.json({
      success: true,
      summary: summary,
      checklist_items: checklistItems,
      documentName: documentName,
      timestamp: new Date().toISOString(),
      note: "Generated using OpenAI GPT-3.5-turbo"
    });

  } catch (error) {
    console.error("Error in AI document summarization:", error.message);

    // Fallback to mock summary if OpenAI fails
    const mockSummary = `Document Summary for "${documentName}":

• This document contains important information related to the emergency response
• Key procedures and protocols are outlined for responder reference
• Contact information and resource allocation details are included
• Safety guidelines and operational instructions are provided
• AI analysis temporarily unavailable - please review document manually`;

    res.json({
      success: true,
      summary: mockSummary,
      documentName: documentName,
      timestamp: new Date().toISOString(),
      note: "AI document analysis temporarily unavailable, showing basic summary."
    });
  }
});

// AI Document Question-Answering Route
app.post("/ai/ask-document", authenticateToken, async (req, res) => {
  const { eventId, question, documents } = req.body;

  if (!eventId || !question) {
    return res.status(400).json({
      success: false,
      error: "Missing required fields: eventId, question"
    });
  }

  try {
    // Check if this question was already asked by this user for this event
    const existingQuestion = await pool.query(
      "SELECT answer FROM document_questions WHERE event_id = $1 AND user_id = $2 AND question = $3 ORDER BY created_at DESC LIMIT 1",
      [eventId, req.user.id, question]
    );

    if (existingQuestion.rows.length > 0 && existingQuestion.rows[0].answer) {
      return res.json({
        success: true,
        answer: existingQuestion.rows[0].answer,
        question: question,
        timestamp: new Date().toISOString(),
        note: "Retrieved existing answer for this user"
      });
    }

    // Store the question in database
    await pool.query(
      "INSERT INTO document_questions (event_id, user_id, question, document_context) VALUES ($1, $2, $3, $4)",
      [eventId, req.user.id, question, JSON.stringify(documents || [])]
    );

    // Check if OpenAI client is available
    if (!openai) {
      console.log("OpenAI client not available, using mock answer");
      const mockAnswer = `Mock Answer for "${question}":

Based on the available documents, here are the key points:
• This is a simulated response as OpenAI API is not configured
• Please check the uploaded documents manually for specific information
• Contact your system administrator to configure AI features

Note: AI question-answering requires OpenAI API configuration.`;

      // Update the question with the mock answer
      await pool.query(
        "UPDATE document_questions SET answer = $1 WHERE event_id = $2 AND user_id = $3 AND question = $4",
        [mockAnswer, eventId, req.user.id, question]
      );

      return res.json({
        success: true,
        answer: mockAnswer,
        question: question,
        timestamp: new Date().toISOString(),
        note: "AI question-answering requires OpenAI API configuration"
      });
    }

    // Get AI preconditions from company settings
    const companyId = await getCompanyIdFromToken(req);
    const settingsResult = await pool.query(
      "SELECT ai_preconditions FROM company_settings WHERE company_id = $1",
      [companyId]
    );

    let systemPrompt = "You are an AI assistant helping emergency responders analyze documents. Provide accurate, concise answers based on the uploaded documents.";

    if (settingsResult.rows.length > 0 && settingsResult.rows[0].ai_preconditions?.document_qa_prompt) {
      systemPrompt = settingsResult.rows[0].ai_preconditions.document_qa_prompt;
    }

    // Create context from documents
    let documentContext = "Available documents: ";
    if (documents && documents.length > 0) {
      documentContext += documents.map(doc => doc.originalName || doc.filename).join(", ");
    } else {
      documentContext += "No specific documents provided";
    }

    // Use OpenAI to answer the question
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: systemPrompt
        },
        {
          role: "user",
          content: `${documentContext}\n\nQuestion: ${question}\n\nPlease provide a helpful answer based on the available emergency response documents.`
        }
      ],
      max_tokens: 300,
      temperature: 0.3,
    });

    const answer = completion.choices[0].message.content;

    // Update the question with the AI answer
    await pool.query(
      "UPDATE document_questions SET answer = $1 WHERE event_id = $2 AND user_id = $3 AND question = $4",
      [answer, eventId, req.user.id, question]
    );

    res.json({
      success: true,
      answer: answer,
      question: question,
      timestamp: new Date().toISOString(),
      note: "Generated using OpenAI GPT-3.5-turbo"
    });

  } catch (error) {
    console.error("Error in AI document Q&A:", error.message);

    // Fallback answer
    const fallbackAnswer = `I apologize, but I'm unable to process your question at the moment due to a technical issue. Please try again later or consult the documents manually.

Your question: "${question}"

Error: ${error.message}`;

    try {
      await pool.query(
        "UPDATE document_questions SET answer = $1 WHERE event_id = $2 AND user_id = $3 AND question = $4",
        [fallbackAnswer, eventId, req.user.id, question]
      );
    } catch (dbError) {
      console.error("Error updating question with fallback:", dbError.message);
    }

    res.status(500).json({
      success: false,
      error: "AI question-answering failed",
      fallbackAnswer: fallbackAnswer
    });
  }
});

app.post('/ai/generate-actions', authenticateToken, async (req, res) => {
  try {
    const { eventId, userRole, eventType, eventTitle, eventInfo, documents } = req.body;

    if (!userRole || !eventType) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: userRole and eventType',
        code: 'MISSING_REQUIRED_FIELDS'
      });
    }

    // Check if OpenAI client is available
    if (!openai) {
      console.log("OpenAI client not available, using mock actions");
      const actions = generateMockActions(userRole, eventType, eventTitle);
      return res.json({
        success: true,
        actions: actions,
        eventId: eventId,
        userRole: userRole,
        generatedAt: new Date().toISOString(),
        note: "AI analysis requires OpenAI API configuration"
      });
    }

    // Prepare context for AI
    let contextText = `Event: ${eventTitle}\nType: ${eventType}\nInfo: ${eventInfo || 'No additional information'}\nUser Role: ${userRole}`;

    if (documents && documents.length > 0) {
      contextText += `\n\nAttached Documents:\n${documents.map(doc => `- ${doc.originalName} (${doc.mimetype})`).join('\n')}`;
      contextText += '\n\nNote: Document contents would be analyzed in full implementation.';
    }

    // Use OpenAI to generate role-specific actions
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: `You are an AI assistant helping emergency responders. Generate specific, actionable tasks for a ${userRole} in a ${eventType} event. Focus on role-appropriate responsibilities, safety protocols, and coordination tasks. Return a JSON array of action items, each with 'text' and 'priority' (high/medium/low) fields.`
        },
        {
          role: "user",
          content: `Generate action items for this emergency response scenario:\n\n${contextText}`
        }
      ],
      max_tokens: 500,
      temperature: 0.3,
    });

    let actions;
    try {
      const aiResponse = completion.choices[0].message.content;
      // Try to parse as JSON, fallback to text parsing
      if (aiResponse.includes('[') && aiResponse.includes(']')) {
        const jsonMatch = aiResponse.match(/\[[\s\S]*\]/);
        if (jsonMatch) {
          actions = JSON.parse(jsonMatch[0]);
        }
      }

      if (!actions) {
        // Fallback: parse text response into action items
        const lines = aiResponse.split('\n').filter(line => line.trim() && (line.includes('-') || line.includes('•')));
        actions = lines.map(line => ({
          text: line.replace(/^[-•]\s*/, '').trim(),
          priority: 'medium'
        }));
      }
    } catch (parseError) {
      console.error("Error parsing AI response, using fallback:", parseError);
      actions = generateMockActions(userRole, eventType, eventTitle);
    }

    res.json({
      success: true,
      actions: actions,
      eventId: eventId,
      userRole: userRole,
      generatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error generating actions:', error);

    // Fallback to mock actions if AI fails
    const actions = generateMockActions(userRole, eventType, eventTitle);
    res.json({
      success: true,
      actions: actions,
      eventId: eventId,
      userRole: userRole,
      generatedAt: new Date().toISOString(),
      note: "AI analysis temporarily unavailable, showing standard actions."
    });
  }
});

// List all events for mobile app
app.get('/events', authenticateToken, async (req, res) => {
  try {
    const result = await pool.query(
      "SELECT e.*, " +
        "json_agg(json_build_object('id', t.id, 'assigned_to', t.assigned_to, 'report_to_location', t.report_to_location, 'status', t.status)) AS tasks " +
        "FROM events e " +
        "LEFT JOIN tasks t ON e.id = t.event_id " +
        "WHERE e.status != $1 " +
        "GROUP BY e.id " +
        "ORDER BY e.created_at DESC",
      ["resolved"]
    );

    const events = result.rows.map((row) => ({
      ...row,
      tasks: row.tasks.filter((task) => task.id !== null),
    }));

    res.json(events);
  } catch (error) {
    console.error('Error fetching events list:', error);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Get single event details
app.get('/events/:eventId', authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params;
    const userId = req.user.id;

    // Get event details with tasks
    const eventResult = await pool.query(
      `SELECT e.*, u.username as created_by_username,
              t.id AS task_id, t.assigned_to, t.report_to_location, t.status as task_status
       FROM events e
       LEFT JOIN users u ON e.created_by = u.id
       LEFT JOIN tasks t ON e.id = t.event_id
       WHERE e.id = $1`,
      [eventId]
    );

    if (eventResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: "Event not found",
        code: "EVENT_NOT_FOUND"
      });
    }

    const event = eventResult.rows[0];

    // Build tasks array from the joined results
    const tasks = [];
    eventResult.rows.forEach((row) => {
      if (row.task_id) {
        tasks.push({
          id: row.task_id,
          assigned_to: row.assigned_to,
          report_to_location: row.report_to_location,
          status: row.task_status,
        });
      }
    });

    // Check if user has access to this event
    // For now, allow access to all authenticated users
    // You can add more specific access control here if needed

    res.json({
      success: true,
      ...event,
      // Parse JSON fields if they exist
      location: event.location ? (typeof event.location === 'string' ? JSON.parse(event.location) : event.location) : null,
      event_documents: event.event_documents ? (typeof event.event_documents === 'string' ? JSON.parse(event.event_documents) : event.event_documents) : [],
      included_report_to_locations: event.included_report_to_locations ? (typeof event.included_report_to_locations === 'string' ? JSON.parse(event.included_report_to_locations) : event.included_report_to_locations) : [],
      // Add the missing fields that dashboard expects
      assignIds: event.assigned_ids,
      notifyAll: event.notify_all_if_unassigned,
      tasks: tasks
    });

  } catch (error) {
    console.error('Error fetching event:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch event details',
      code: 'FETCH_EVENT_FAILED'
    });
  }
});

// Get event tasks
app.get('/events/:eventId/tasks', authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params;

    // Verify event exists
    const eventResult = await pool.query("SELECT id FROM events WHERE id = $1", [eventId]);
    if (eventResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: "Event not found",
        code: "EVENT_NOT_FOUND"
      });
    }

    // Get tasks for this event (if you have a tasks table)
    // For now, return empty array since tasks table might not exist
    try {
      const tasksResult = await pool.query(
        "SELECT * FROM tasks WHERE event_id = $1 ORDER BY created_at DESC",
        [eventId]
      );
      res.json(tasksResult.rows);
    } catch (error) {
      // If tasks table doesn't exist, return empty array
      console.log('Tasks table might not exist, returning empty array');
      res.json([]);
    }

  } catch (error) {
    console.error('Error fetching event tasks:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch event tasks',
      code: 'FETCH_TASKS_FAILED'
    });
  }
});

// Get notified responders for an event
app.get('/events/:eventId/notified-responders', authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params;

    // Verify event exists
    const eventResult = await pool.query("SELECT id FROM events WHERE id = $1", [eventId]);
    if (eventResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: "Event not found",
        code: "EVENT_NOT_FOUND"
      });
    }

    // Get notified responders (if you have a notifications table)
    try {
      const respondersResult = await pool.query(
        `SELECT u.id, u.username, u.first_name, u.last_name, u.phone, u.email, n.status, n.created_at
         FROM notifications n
         JOIN users u ON n.user_id = u.id
         WHERE n.event_id = $1
         ORDER BY n.created_at DESC`,
        [eventId]
      );
      res.json(respondersResult.rows);
    } catch (error) {
      // If notifications table doesn't exist, return empty array
      console.log('Notifications table might not exist, returning empty array');
      res.json([]);
    }

  } catch (error) {
    console.error('Error fetching notified responders:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch notified responders',
      code: 'FETCH_RESPONDERS_FAILED'
    });
  }
});

// Get responder locations for an event
app.get('/events/:eventId/responder-locations', authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params;

    // Verify event exists
    const eventResult = await pool.query("SELECT id FROM events WHERE id = $1", [eventId]);
    if (eventResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: "Event not found",
        code: "EVENT_NOT_FOUND"
      });
    }

    // Get responder locations (if you have a locations table)
    try {
      const locationsResult = await pool.query(
        `SELECT u.id as userId, u.username, u.first_name, u.last_name,
                l.latitude as lat, l.longitude as lng, l.updated_at
         FROM user_locations l
         JOIN users u ON l.user_id = u.id
         WHERE l.event_id = $1 AND l.updated_at > NOW() - INTERVAL '1 hour'
         ORDER BY l.updated_at DESC`,
        [eventId]
      );
      res.json(locationsResult.rows);
    } catch (error) {
      // If locations table doesn't exist, return empty array
      console.log('User locations table might not exist, returning empty array');
      res.json([]);
    }

  } catch (error) {
    console.error('Error fetching responder locations:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch responder locations',
      code: 'FETCH_LOCATIONS_FAILED'
    });
  }
});

// Chat routes
app.get('/events/:eventId/chat', authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params;
    const { limit = 50, offset = 0 } = req.query;

    // Create chat_messages table if it doesn't exist
    await pool.query(`
      CREATE TABLE IF NOT EXISTS chat_messages (
        id SERIAL PRIMARY KEY,
        event_id INTEGER REFERENCES events(id) ON DELETE CASCADE,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        username VARCHAR(255) NOT NULL,
        text TEXT NOT NULL,
        timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Fetch actual chat messages from database
    const result = await pool.query(`
      SELECT cm.*, u.first_name, u.last_name
      FROM chat_messages cm
      LEFT JOIN users u ON cm.user_id = u.id
      WHERE cm.event_id = $1
      ORDER BY cm.timestamp ASC
      LIMIT $2 OFFSET $3
    `, [eventId, limit, offset]);

    const messages = result.rows.map(row => ({
      id: row.id,
      text: row.text,
      timestamp: row.timestamp,
      username: row.username,
      first_name: row.first_name || 'Unknown',
      last_name: row.last_name || 'User',
      userId: row.user_id
    }));

    res.json({
      success: true,
      messages: messages
    });

  } catch (error) {
    console.error('Error fetching chat messages:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch chat messages',
      code: 'CHAT_FETCH_FAILED'
    });
  }
});

app.post('/events/:eventId/chat', authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params;
    const { text } = req.body;

    console.log('Chat POST request received:', { eventId, text, user: req.user });

    if (!text || !text.trim()) {
      return res.status(400).json({
        success: false,
        error: 'Message text is required',
        code: 'MISSING_MESSAGE_TEXT'
      });
    }

    if (!req.user || !req.user.id || !req.user.username) {
      return res.status(401).json({
        success: false,
        error: 'User authentication failed - missing user data',
        code: 'AUTH_FAILED'
      });
    }

    // Ensure chat_messages table exists
    try {
      await pool.query(`
        CREATE TABLE IF NOT EXISTS chat_messages (
          id SERIAL PRIMARY KEY,
          event_id INTEGER REFERENCES events(id) ON DELETE CASCADE,
          user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
          username VARCHAR(255) NOT NULL,
          text TEXT NOT NULL,
          timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        )
      `);
      console.log('Chat messages table ensured');
    } catch (tableError) {
      console.error('Error creating chat_messages table:', tableError);
      return res.status(500).json({
        success: false,
        error: 'Database table creation failed',
        code: 'TABLE_CREATION_FAILED'
      });
    }

    // Insert message into database
    const result = await pool.query(`
      INSERT INTO chat_messages (event_id, user_id, username, text, timestamp)
      VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
      RETURNING *
    `, [eventId, req.user.id, req.user.username, text.trim()]);

    console.log('Message inserted successfully:', result.rows[0]);

    const savedMessage = result.rows[0];

    // Get user details for the message
    const userResult = await pool.query(`
      SELECT first_name, last_name FROM users WHERE id = $1
    `, [req.user.id]);

    const newMessage = {
      id: savedMessage.id,
      eventId: parseInt(eventId),
      text: savedMessage.text,
      username: req.user.username,
      userId: savedMessage.user_id,
      timestamp: savedMessage.timestamp,
      first_name: userResult.rows[0]?.first_name || 'Unknown',
      last_name: userResult.rows[0]?.last_name || 'User'
    };

    // Broadcast message to all users in the event room via Socket.IO
    io.to(`event-${eventId}`).emit("chat-message", newMessage);

    res.status(201).json({
      success: true,
      message: newMessage
    });

  } catch (error) {
    console.error('Error sending chat message:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send message',
      code: 'CHAT_SEND_FAILED'
    });
  }
});

// Additional User Management Routes
app.get("/check-twilo", async (req, res) => {
  let content = "This is a test message from alertcome twilio service"
  try {
    if (!twilioClient) {
      return res.status(500).json({ error: 'Twilio not configured' });
    }

    const smsResult = await twilioClient.messages.create({
      body: content,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: '+16083510444',
    });

    res.status(200).json({ success: true, message: "Test SMS sent successfully" });
  } catch (error) {
    console.error("Failed to send SMS:", {
      message: error.message,
      code: error.code,
      moreInfo: error.moreInfo,
      status: error.status,
    });
    res.status(500).json({ success: false, error: "Failed to send SMS" });
  }
});

app.post("/user/change-status", authenticateToken, async (req, res) => {
  const { email, status } = req.body;

  if (!email) {
    return res.status(400).json({ error: "Email is required." });
  }

  try {
    const result = await pool.query(
      "SELECT id, status FROM users WHERE email = $1",
      [email]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: "User not found." });
    }

    const user = result.rows[0];
    const newStatus = user.status === 1 ? 0 : 1;

    await pool.query(
      "UPDATE users SET status = $1 WHERE id = $2",
      [newStatus, user.id]
    );

    res.status(200).json({ message: "User status updated successfully.", newStatus });
  } catch (error) {
    console.error("Change status error:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.post("/add-user", authenticateToken, checkRole(["commander"]), async (req, res) => {
  const {
    username, password, role, email, main_location, phone,
    first_name, last_name, job_role, home_address, city, state, zip,
  } = req.body;
  
  if (!username || !password || !role || !email || !main_location) {
    return res.status(400).json({
      error: "Missing required fields: username, password, role, email, main_location",
    });
  }
  
  try {
    const hashedPassword = await bcrypt.hash(password, 10);
    await pool.query(
      "INSERT INTO users (username, password, email, role, main_location, phone, first_name, last_name, job_role, home_address, city, state, zip) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)",
      [username, hashedPassword, email, role, main_location, phone || "", first_name || "", last_name || "", job_role || "", home_address || "", city || "", state || "", zip || ""]
    );
    res.json({ message: "User added successfully" });
  } catch (error) {
    console.error("Add user error:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.post("/import-users", authenticateToken, checkRole(["commander"]), async (req, res) => {
  const { csv } = req.body;
  if (!csv) return res.status(400).json({ error: "Missing CSV data" });
  
  try {
    const lines = csv.trim().split("\n");
    let count = 0;
    for (const line of lines) {
      const [username, password, role, email, main_location, phone, first_name, last_name, job_role, home_address, city, state, zip] = line.split(",").map((s) => s.trim());
      if (username && password && role && main_location) {
        const hashedPassword = await bcrypt.hash(password, 10);
        await pool.query(
          "INSERT INTO users (username, password, email, role, main_location, phone, first_name, last_name, job_role, home_address, city, state, zip) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13) ON CONFLICT (username) DO NOTHING",
          [username, hashedPassword, email || "<EMAIL>", role, main_location, phone || "", first_name || "", last_name || "", job_role || "", home_address || "", city || "", state || "", zip || ""]
        );
        count++;
      }
    }
    res.json({ count, message: "Users imported successfully" });
  } catch (error) {
    console.error("Import users error:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.put("/edit-user/:id", authenticateToken, checkRole(["commander"]), async (req, res) => {
  const { id } = req.params;
  const {
    username, password, role, email, main_location, phone,
    first_name, last_name, job_role, home_address, city, state, zip,
  } = req.body;
  
  if (!username || !role || !email || !main_location) {
    return res.status(400).json({
      error: "Missing required fields: username, role, email, main_location",
    });
  }
  
  try {
    let query = "UPDATE users SET username = $1, role = $2, email = $3, main_location = $4, phone = $5, first_name = $6, last_name = $7, job_role = $8, home_address = $9, city = $10, state = $11, zip = $12";
    const values = [username, role, email, main_location, phone || "", first_name || "", last_name || "", job_role || "", home_address || "", city || "", state || "", zip || ""];
    
    if (password) {
      const hashedPassword = await bcrypt.hash(password, 10);
      query += ", password = $13";
      values.push(hashedPassword);
    }
    
    query += " WHERE id = $" + (values.length + 1) + " RETURNING *";
    values.push(id);

    const result = await pool.query(query, values);
    if (result.rows.length === 0) {
      return res.status(404).json({ error: "User not found" });
    }
    res.json({ message: "User updated successfully", user: result.rows[0] });
  } catch (error) {
    console.error("Error updating user:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.get("/users", authenticateToken, checkRole(["commander"]), async (req, res) => {
  try {
    const result = await pool.query(
      "SELECT id, username, role, email, main_location, phone, first_name, last_name, job_role, home_address, city, state, status, zip FROM users"
    );
    res.json(result.rows);
  } catch (error) {
    console.error("Error fetching users:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Create new task
app.post("/tasks", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  const { event_id, assigned_to, title, description, status, priority, due_date } = req.body;

  try {
    const result = await pool.query(`
      INSERT INTO tasks (event_id, assigned_to, title, description, status, priority, due_date, created_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
      RETURNING *
    `, [event_id, assigned_to, title, description, status || 'pending', priority, due_date]);

    const newTask = result.rows[0];

    // Emit socket event for real-time updates
    io.to(`user_${assigned_to}`).emit('taskAssigned', newTask);

    res.status(201).json(newTask);
  } catch (error) {
    console.error("Error creating task:", error);
    res.status(500).json({ error: "Failed to create task" });
  }
});

// Create notification
app.post("/notifications", authenticateToken, async (req, res) => {
  const { responder_id, event_id, title, message, type, priority } = req.body;

  try {
    const result = await pool.query(`
      INSERT INTO notifications (responder_id, event_id, title, message, type, priority, is_read, created_at)
      VALUES ($1, $2, $3, $4, $5, $6, false, NOW())
      RETURNING *
    `, [responder_id, event_id, title, message, type || 'general', priority || 'medium']);

    const notification = result.rows[0];

    // Emit socket event for real-time notifications
    io.to(`user_${responder_id}`).emit('notification', notification);

    res.status(201).json(notification);
  } catch (error) {
    console.error("Error creating notification:", error);
    res.status(500).json({ error: "Failed to create notification" });
  }
});

// Get notifications for current user
app.get("/notifications", authenticateToken, async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT n.*, e.title as event_title
      FROM notifications n
      LEFT JOIN events e ON n.event_id = e.id
      WHERE n.responder_id = $1
      ORDER BY n.created_at DESC
      LIMIT 50
    `, [req.user.id]);

    res.json(result.rows);
  } catch (error) {
    console.error("Error fetching notifications:", error);
    res.status(500).json({ error: "Failed to fetch notifications" });
  }
});

// Mark notification as read
app.put("/notifications/:id/read", authenticateToken, async (req, res) => {
  const { id } = req.params;

  try {
    const result = await pool.query(`
      UPDATE notifications
      SET is_read = true
      WHERE id = $1 AND responder_id = $2
      RETURNING *
    `, [id, req.user.id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: "Notification not found" });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error("Error marking notification as read:", error);
    res.status(500).json({ error: "Failed to update notification" });
  }
});

app.delete("/delete-user/:id", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  const { id } = req.params;
  try {
    const checkUser = await pool.query("SELECT * FROM users WHERE id = $1", [id]);

    if (checkUser.rows.length === 0) {
      return res.status(404).json({ error: "User not found" });
    }

    await pool.query('BEGIN');
    
    try {
      await pool.query("DELETE FROM notifications WHERE responder_id = $1", [id]);
      await pool.query("DELETE FROM responders WHERE id = $1", [id]);
      await pool.query("UPDATE tasks SET assigned_to = NULL WHERE assigned_to = $1", [id]);
      await pool.query("UPDATE events SET created_by = NULL WHERE created_by = $1", [id]);
      
      const result = await pool.query("DELETE FROM users WHERE id = $1 RETURNING *", [id]);
      
      await pool.query('COMMIT');
      res.json({ message: "User deleted successfully" });
    } catch (innerError) {
      await pool.query('ROLLBACK');
      throw innerError;
    }
  } catch (error) {
    console.error("Error deleting user:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Event Management Routes
app.post("/events", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  const {
    title, info, scale, urgency, location, description,
    included_report_to_locations, assignedIds, notifyAllIfUnassigned,
    module, customFields, event_type, location_update_interval,
    notification_channels, event_documents,
  } = req.body;
  const created_by = req.user.id;

  try {
    const eventData = {
      title, info, scale, urgency,
      location: location || { commonName: "", address: "", city: "", state: "", zip: "" },
      description: description || "", status: "open", created_by,
      included_report_to_locations: included_report_to_locations || [],
      assignedIds: assignedIds || [], notifyAllIfUnassigned: notifyAllIfUnassigned || false,
      module, customFields,
      event_type: event_type || "response",
      location_update_interval: location_update_interval || 60,
      notification_channels: notification_channels || ["web_app"],
      event_documents: event_documents || [],
    };
    
    const event = await db.createEvent(eventData);

    // Log event creation
    console.log(`🚨 EVENT LAUNCHED (Duplicate Route): ID=${event.id}, Title="${event.title}", Created by User ID=${created_by} at ${new Date().toISOString()}`);

    await notifyStaff(event, eventData.assignedIds??[], eventData.notifyAllIfUnassigned);

    io.emit("new-event", { id: event.id, title: event.title, status: event.status });
    res.status(201).json(event);
  } catch (error) {
    console.error("Error creating event:", error.message, error.stack);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.get("/all-events", authenticateToken, checkRole(["commander", "staff"]), async (req, res) => {
  try {
    const result = await pool.query("SELECT * FROM events ORDER BY created_at DESC");
    res.json(result.rows);
  } catch (error) {
    console.error("Error in /all-events:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.get("/all-active-events", authenticateToken, checkRole(["commander", "lead", "staff"]), async (req, res) => {
  try {
    const result = await pool.query(
      "SELECT e.*, json_agg(json_build_object('id', t.id, 'assigned_to', t.assigned_to, 'report_to_location', t.report_to_location, 'status', t.status)) AS tasks FROM events e LEFT JOIN tasks t ON e.id = t.event_id WHERE e.status != $1 GROUP BY e.id ORDER BY e.created_at DESC",
      ["resolved"]
    );
    const events = result.rows.map((row) => ({
      ...row,
      tasks: row.tasks.filter((task) => task.id !== null),
    }));
    res.json(events);
  } catch (error) {
    console.error("Error in /all-active-events:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.get("/dept-events", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  try {
    const result = await pool.query(
      "SELECT * FROM events WHERE created_by IN (SELECT id FROM users WHERE role IN ($1, $2)) ORDER BY created_at DESC",
      ["commander", "lead"]
    );
    res.json(result.rows);
  } catch (error) {
    console.error("Error in /dept-events:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.get("/my-tasks", authenticateToken, checkRole(["commander", "lead", "staff"]), async (req, res) => {
  try {
    const eventsResult = await pool.query(
      "SELECT * FROM events WHERE created_by = $1 ORDER BY created_at DESC",
      [req.user.id]
    );
    const tasksResult = await pool.query(
      "SELECT t.*, e.title, e.info, e.scale, e.urgency, e.location FROM tasks t JOIN events e ON t.event_id = e.id WHERE t.assigned_to = $1 ORDER BY t.created_at DESC",
      [req.user.id]
    );
    res.json({
      created_events: eventsResult.rows,
      assigned_tasks: tasksResult.rows,
    });
  } catch (error) {
    console.error("Error in /my-tasks:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.post("/event/qa", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  const { title, info, scale, urgency, location, description, status, escalation_level } = req.body;
  
  if (!title || !scale || !urgency) {
    return res.status(400).json({ error: "Missing required fields: title, scale, urgency" });
  }
  
  try {
    const result = await pool.query(
      "INSERT INTO events (title, info, scale, urgency, location, description, status, escalation_level, created_by) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING *",
      [title, info, scale, urgency, location || "Unknown", description || "", status || "open", escalation_level || 0, req.user.id]
    );
    const event = result.rows[0];
    await notifyStaff(event, event.assigned_ids??[], event.notify_all_if_unassigned);
    io.emit("new-event", event);
    res.json(event);
  } catch (error) {
    console.error("Error in /event/qa:", error.message, error.stack);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.put("/event/:id", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  const { status } = req.body;
  const eventId = req.params.id;
  
  if (!status) {
    return res.status(400).json({ error: "Status is required" });
  }
  
  try {
    const result = await pool.query(
      "UPDATE events SET status = $1 WHERE id = $2 RETURNING *",
      [status, eventId]
    );
    if (result.rows.length === 0) {
      return res.status(404).json({ error: "Event not found" });
    }
    const event = result.rows[0];
    io.emit("new-event", event);
    res.json(event);
  } catch (error) {
    console.error("Error in /event/:id PUT:", error.message, error.stack);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.delete("/events/:id", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  const { id } = req.params;
  const eventId = parseInt(id, 10);

  if (isNaN(eventId)) {
    return res.status(400).json({ error: "Invalid event ID: must be a number" });
  }

  try {
    await pool.query("DELETE FROM notifications WHERE event_id = $1", [eventId]);
    const result = await pool.query("DELETE FROM events WHERE id = $1 RETURNING *", [eventId]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: `Event ${eventId} not found` });
    }

    io.emit("event-deleted", { id: eventId });
    res.json({ message: "Event deleted successfully", event: result.rows[0] });
  } catch (error) {
    console.error("Error deleting event:", error.message, error.stack);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Task Management Routes
app.post("/tasks", authenticateToken, checkRole(["commander", "lead", "staff"]), async (req, res) => {
  const { event_id, assigned_to, status } = req.body;
  
  if (!event_id || !assigned_to) {
    return res.status(400).json({ error: "Missing required fields: event_id, assigned_to" });
  }
  
  try {
    const eventIdNum = parseInt(event_id, 10);
    if (isNaN(eventIdNum)) {
      return res.status(400).json({ error: "Invalid event_id: must be a number" });
    }

    const eventResult = await pool.query("SELECT * FROM events WHERE id = $1", [eventIdNum]);
    const event = eventResult.rows[0];
    if (!event) {
      return res.status(404).json({ error: `Event ${eventIdNum} not found` });
    }

    const locationsWithStaff = event.included_report_to_locations
      ? event.included_report_to_locations.map((loc) => ({
          location: `${loc.address}, ${loc.city}, ${loc.state} ${loc.zip}`.trim(),
          staffNeeded: loc.staffNeeded || 2,
          assignedCount: 0,
        }))
      : [{ location: event.location || "Unknown", staffNeeded: 2, assignedCount: 0 }];
    
    const primaryLoc = locationsWithStaff.find((loc) => loc.location === event.location) || locationsWithStaff[0];

    const existingTask = await pool.query(
      "SELECT * FROM tasks WHERE event_id = $1 AND assigned_to = $2",
      [eventIdNum, assigned_to]
    );
    
    let task;
    if (existingTask.rows.length > 0) {
      const result = await pool.query(
        "UPDATE tasks SET status = $1, report_to_location = COALESCE(report_to_location, $2) WHERE event_id = $3 AND assigned_to = $4 RETURNING *",
        [status || "pending", primaryLoc.location, eventIdNum, assigned_to]
      );
      task = result.rows[0];
    } else {
      const result = await pool.query(
        "INSERT INTO tasks (event_id, assigned_to, status, report_to_location) VALUES ($1, $2, $3, $4) RETURNING *",
        [eventIdNum, assigned_to, status || "pending", primaryLoc.location]
      );
      task = result.rows[0];
    }

    io.to(`user-${assigned_to}`).emit("task-assigned", { task, event });
    io.to(`event-${eventIdNum}`).emit("task-response", task);
    res.json(task);
  } catch (error) {
    console.error("Error in /tasks:", error.message, error.stack);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.put("/tasks", authenticateToken, checkRole(["commander", "lead", "staff"]), async (req, res) => {
  const { event_id, assigned_to, status, report_to_location } = req.body;
  
  if (!event_id || !assigned_to || !report_to_location) {
    return res.status(400).json({
      error: "Missing required fields: event_id, assigned_to, report_to_location",
    });
  }
  
  try {
    const result = await pool.query(
      "UPDATE tasks SET report_to_location = $1, status = COALESCE($2, status) WHERE event_id = $3 AND assigned_to = $4 RETURNING *",
      [report_to_location, status, event_id, assigned_to]
    );
    if (result.rows.length === 0) {
      return res.status(404).json({ error: "Task not found" });
    }
    const updatedTask = result.rows[0];
    io.to(`event-${event_id}`).emit("task-response", updatedTask);
    res.json(updatedTask);
  } catch (error) {
    console.error("Error updating task:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Responder Management Routes
// Responders endpoint is already defined above

app.post("/responder/location", authenticateToken, checkRole(["lead", "staff"]), async (req, res) => {
  const { latitude, longitude } = req.body;
  
  if (!latitude || !longitude) {
    return res.status(400).json({ error: "Missing coordinates: latitude, longitude" });
  }
  
  try {
    const result = await pool.query(
      "INSERT INTO responders (id, latitude, longitude, status, last_updated) VALUES ($1, $2, $3, $4, NOW()) ON CONFLICT (id) DO UPDATE SET latitude = $2, longitude = $3, status = $4, last_updated = NOW() RETURNING *",
      [req.user.id, latitude, longitude, "available"]
    );
    const responder = result.rows[0];
    io.emit("responder-update", responder);
    res.json(responder);
  } catch (error) {
    console.error("Error in /responder/location:", error.message, error.stack);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.get("/active-events/:id", authenticateToken, checkRole(["commander", "lead", "staff"]), async (req, res) => {
  try {
    const eventId = parseInt(req.params.id, 10);
    if (isNaN(eventId)) {
      return res.status(400).json({ error: "Invalid event ID: must be a number" });
    }
    
    const result = await pool.query(
      "SELECT e.*, t.id AS task_id, t.assigned_to, t.report_to_location, t.status FROM events e LEFT JOIN tasks t ON e.id = t.event_id WHERE e.id = $1 AND e.status != $2",
      [eventId, "resolved"]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: "Event not found or already resolved" });
    }

    const event = {
      id: result.rows[0].id, title: result.rows[0].title, info: result.rows[0].info,
      scale: result.rows[0].scale, urgency: result.rows[0].urgency, location: result.rows[0].location,
      description: result.rows[0].description, status: result.rows[0].status,
      created_by: result.rows[0].created_by, created_at: result.rows[0].created_at,
      included_report_to_locations: result.rows[0].included_report_to_locations,
      tasks: [], assignIds: result.rows[0].assigned_ids, notifyAll: result.rows[0].notify_all_if_unassigned,
    };

    result.rows.forEach((row) => {
      if (row.task_id) {
        event.tasks.push({
          id: row.task_id, assigned_to: row.assigned_to,
          report_to_location: row.report_to_location, status: row.status,
        });
      }
    });

    res.json(event);
  } catch (error) {
    console.error("Error in /active-events/:id:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.get("/active-events-for-staff", authenticateToken, checkRole(["staff"]), async (req, res) => {
  try {
    const result = await pool.query(
      "SELECT e.* FROM events e JOIN tasks t ON e.id = t.event_id WHERE t.assigned_to = $1 AND e.status != $2 ORDER BY e.created_at DESC",
      [req.user.id, "resolved"]
    );
    res.json(result.rows);
  } catch (error) {
    console.error("Error in /active-events-for-staff:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.get("/events/initial", authenticateToken, async (req, res) => {
  try {
    let query;
    if (req.user.role === "commander") {
      query = "SELECT * FROM events ORDER BY created_at DESC";
      const result = await pool.query(query);
      return res.json(result.rows);
    } else if (req.user.role === "lead") {
      query = "SELECT * FROM events WHERE created_by IN (SELECT id FROM users WHERE role IN ($1, $2)) ORDER BY created_at DESC";
      const result = await pool.query(query, ["commander", "lead"]);
      return res.json(result.rows);
    } else if (req.user.role === "staff") {
      query = "SELECT * FROM events WHERE created_by = $1 ORDER BY created_at DESC";
      const result = await pool.query(query, [req.user.id]);
      return res.json(result.rows);
    } else {
      query = "SELECT title, scale, urgency, created_at FROM events ORDER BY created_at DESC";
      const result = await pool.query(query);
      return res.json(result.rows);
    }
  } catch (error) {
    console.error("Error in /events/initial:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Template Management Routes
app.get("/templates", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  try {
    const result = await pool.query("SELECT * FROM templates ORDER BY created_at DESC");
    res.json(result.rows);
  } catch (error) {
    console.error("Error in /templates:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.post("/templates", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  const { name, description, config } = req.body;
  if (!name || !config) {
    return res.status(400).json({ error: "Missing required fields: name, config" });
  }
  
  try {
    const result = await pool.query(
      "INSERT INTO templates (name, description, config, created_by) VALUES ($1, $2, $3, $4) RETURNING *",
      [name, description || "", JSON.stringify(config), req.user.id]
    );
    res.json(result.rows[0]);
  } catch (error) {
    console.error("Error creating template:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.put("/templates/:id", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  const { id } = req.params;
  const { name, description, config } = req.body;
  if (!name || !config) {
    return res.status(400).json({ error: "Missing required fields: name, config" });
  }
  
  try {
    const result = await pool.query(
      "UPDATE templates SET name = $1, description = $2, config = $3 WHERE id = $4 RETURNING *",
      [name, description || "", JSON.stringify(config), id]
    );
    if (result.rows.length === 0) {
      return res.status(404).json({ error: "Template not found" });
    }
    res.json(result.rows[0]);
  } catch (error) {
    console.error("Error updating template:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.delete("/templates/:id", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  const { id } = req.params;
  try {
    const result = await pool.query("DELETE FROM templates WHERE id = $1 RETURNING *", [id]);
    if (result.rows.length === 0) {
      return res.status(404).json({ error: "Template not found" });
    }
    res.json({ message: "Template deleted successfully" });
  } catch (error) {
    console.error("Error deleting template:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Template Document Management Routes
app.post("/templates/:templateId/documents", authenticateToken, checkRole(["commander", "lead"]), upload.array('documents', 10), async (req, res) => {
  const { templateId } = req.params;

  try {
    // Verify template exists and user has access
    const templateResult = await pool.query("SELECT * FROM templates WHERE id = $1", [templateId]);
    if (templateResult.rows.length === 0) {
      return res.status(404).json({ error: "Template not found" });
    }

    const files = req.files;
    if (!files || files.length === 0) {
      return res.status(400).json({ error: "No files uploaded" });
    }

    // Prepare documents data
    const documentsData = files.map(file => ({
      filename: file.filename,
      originalName: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      uploadedBy: req.user.id,
      uploadedAt: new Date()
    }));

    // Get existing template config
    const template = templateResult.rows[0];
    const config = typeof template.config === 'string' ? JSON.parse(template.config) : template.config;
    const existingDocuments = config.event_documents || [];

    // Merge with new documents
    const updatedDocuments = [...existingDocuments, ...documentsData];
    config.event_documents = updatedDocuments;

    // Update template with new documents
    const updateResult = await pool.query(
      "UPDATE templates SET config = $1 WHERE id = $2 RETURNING *",
      [JSON.stringify(config), templateId]
    );

    res.json({
      message: "Documents uploaded successfully to template",
      documents: documentsData,
      totalDocuments: updatedDocuments.length
    });

  } catch (error) {
    console.error("Error uploading documents to template:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.get("/templates/:templateId/documents", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  const { templateId } = req.params;

  try {
    // Verify template exists and user has access
    const templateResult = await pool.query("SELECT config FROM templates WHERE id = $1", [templateId]);
    if (templateResult.rows.length === 0) {
      return res.status(404).json({ error: "Template not found" });
    }

    const config = typeof templateResult.rows[0].config === 'string' ?
      JSON.parse(templateResult.rows[0].config) : templateResult.rows[0].config;
    const documents = config.event_documents || [];
    res.json(documents);

  } catch (error) {
    console.error("Error fetching template documents:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Event Document Management Routes
app.post("/events/:eventId/documents", authenticateToken, checkRole(["commander", "lead", "staff"]), upload.array('documents', 10), async (req, res) => {
  const { eventId } = req.params;
  
  try {
    // Verify event exists and user has access
    const eventResult = await pool.query("SELECT * FROM events WHERE id = $1", [eventId]);
    if (eventResult.rows.length === 0) {
      return res.status(404).json({ error: "Event not found" });
    }
    
    const files = req.files;
    if (!files || files.length === 0) {
      return res.status(400).json({ error: "No files uploaded" });
    }
    
    // Prepare documents data
    const documentsData = files.map(file => ({
      filename: file.filename,
      originalName: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      uploadedBy: req.user.id,
      uploadedAt: new Date()
    }));
    
    // Get existing documents from event
    const event = eventResult.rows[0];
    let existingDocuments = [];

    try {
      // event_documents is already parsed as a JavaScript object/array from JSONB field
      if (event.event_documents) {
        if (Array.isArray(event.event_documents)) {
          existingDocuments = event.event_documents;
        } else if (typeof event.event_documents === 'string') {
          // Fallback for string format
          existingDocuments = event.event_documents.trim() !== '' ? JSON.parse(event.event_documents) : [];
        } else {
          // If it's an object, wrap it in an array
          existingDocuments = [event.event_documents];
        }
      }
    } catch (parseError) {
      console.error("Error processing existing event_documents:", parseError.message);
      console.error("Invalid document content:", event.event_documents);
      // Reset to empty array if processing fails
      existingDocuments = [];
    }
    
    // Merge with new documents
    const updatedDocuments = [...existingDocuments, ...documentsData];
    
    // Update event with new documents
    const updateResult = await pool.query(
      "UPDATE events SET event_documents = $1 WHERE id = $2 RETURNING *",
      [JSON.stringify(updatedDocuments), eventId]
    );
    
    // Emit document update to WebSocket clients
    io.to(`event-${eventId}`).emit("documents-updated", {
      eventId: eventId,
      documents: updatedDocuments
    });
    
    res.json({
      message: "Documents uploaded successfully",
      documents: documentsData,
      totalDocuments: updatedDocuments.length
    });
    
  } catch (error) {
    console.error("Error uploading documents:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.get("/events/:eventId/documents", authenticateToken, async (req, res) => {
  const { eventId } = req.params;
  
  try {
    // Verify event exists and user has access
    const eventResult = await pool.query("SELECT event_documents FROM events WHERE id = $1", [eventId]);
    if (eventResult.rows.length === 0) {
      return res.status(404).json({ error: "Event not found" });
    }
    
    let documents = [];

    try {
      const eventDocuments = eventResult.rows[0].event_documents;
      // event_documents is already parsed as a JavaScript object/array from JSONB field
      if (eventDocuments) {
        if (Array.isArray(eventDocuments)) {
          documents = eventDocuments;
        } else if (typeof eventDocuments === 'string') {
          // Fallback for string format
          documents = eventDocuments.trim() !== '' ? JSON.parse(eventDocuments) : [];
        } else {
          // If it's an object, wrap it in an array
          documents = [eventDocuments];
        }
      }
    } catch (parseError) {
      console.error("Error processing event_documents:", parseError.message);
      console.error("Invalid document content:", eventResult.rows[0].event_documents);
      // Return empty array if processing fails
      documents = [];
    }

    res.json(documents);
    
  } catch (error) {
    console.error("Error fetching documents:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.get("/events/:eventId/documents/:filename", async (req, res) => {
  const { eventId, filename } = req.params;
  
  try {
    // Verify event exists
    const eventResult = await pool.query("SELECT event_documents FROM events WHERE id = $1", [eventId]);
    if (eventResult.rows.length === 0) {
      return res.status(404).json({ error: "Event not found" });
    }
    
    // Handle event_documents which is already parsed from JSONB
    const eventDocuments = eventResult.rows[0].event_documents;
    let documents = [];
    if (eventDocuments) {
      if (Array.isArray(eventDocuments)) {
        documents = eventDocuments;
      } else if (typeof eventDocuments === 'string') {
        documents = eventDocuments.trim() !== '' ? JSON.parse(eventDocuments) : [];
      } else {
        documents = [eventDocuments];
      }
    }
    const document = documents.find(doc => doc.filename === filename);
    
    if (!document) {
      return res.status(404).json({ error: "Document not found" });
    }
    
    const filePath = path.join(__dirname, 'uploads', 'events', eventId.toString(), filename);
    
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: "File not found on disk" });
    }
    
    // Set appropriate headers
    res.setHeader('Content-Type', document.mimetype);
    res.setHeader('Content-Disposition', `attachment; filename="${document.originalName}"`);
    
    // Stream the file
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
    
  } catch (error) {
    console.error("Error downloading document:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.delete("/events/:eventId/documents/:filename", authenticateToken, checkRole(["commander", "lead", "staff"]), async (req, res) => {
  const { eventId, filename } = req.params;
  
  try {
    // Verify event exists and user has access
    const eventResult = await pool.query("SELECT event_documents FROM events WHERE id = $1", [eventId]);
    if (eventResult.rows.length === 0) {
      return res.status(404).json({ error: "Event not found" });
    }
    
    // Handle event_documents which is already parsed from JSONB
    const eventDocuments = eventResult.rows[0].event_documents;
    let documents = [];
    if (eventDocuments) {
      if (Array.isArray(eventDocuments)) {
        documents = eventDocuments;
      } else if (typeof eventDocuments === 'string') {
        documents = eventDocuments.trim() !== '' ? JSON.parse(eventDocuments) : [];
      } else {
        documents = [eventDocuments];
      }
    }
    const documentIndex = documents.findIndex(doc => doc.filename === filename);
    
    if (documentIndex === -1) {
      return res.status(404).json({ error: "Document not found" });
    }
    
    // Remove from array
    const updatedDocuments = documents.filter(doc => doc.filename !== filename);
    
    // Update database
    await pool.query(
      "UPDATE events SET event_documents = $1 WHERE id = $2",
      [JSON.stringify(updatedDocuments), eventId]
    );
    
    // Delete file from disk
    const filePath = path.join(__dirname, 'uploads', 'events', eventId.toString(), filename);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    
    // Emit document update to WebSocket clients
    io.to(`event-${eventId}`).emit("documents-updated", {
      eventId: eventId,
      documents: updatedDocuments
    });
    
    res.json({ message: "Document deleted successfully" });
    
  } catch (error) {
    console.error("Error deleting document:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Resources Management Routes

// Get all resources for a company
app.get("/resources", authenticateToken, async (req, res) => {
  try {
    const companyId = req.user.company_id || 1; // Default to company 1

    const result = await pool.query(
      "SELECT * FROM resources WHERE company_id = $1 AND is_active = true ORDER BY name",
      [companyId]
    );

    res.json(result.rows);
  } catch (error) {
    console.error("Error fetching resources:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Create a new resource
app.post("/resources", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  try {
    const { name, description, category } = req.body;
    const companyId = req.user.company_id || 1;
    const createdBy = req.user.id;

    if (!name) {
      return res.status(400).json({ error: "Name is required" });
    }

    const result = await pool.query(
      "INSERT INTO resources (company_id, name, description, category, created_by) VALUES ($1, $2, $3, $4, $5) RETURNING *",
      [companyId, name, description, category || null, createdBy]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    if (error.code === '23505') { // Unique constraint violation
      return res.status(409).json({ error: "Resource with this name already exists" });
    }
    console.error("Error creating resource:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Update a resource
app.put("/resources/:id", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, category, is_active } = req.body;
    const companyId = req.user.company_id || 1;

    const result = await pool.query(
      "UPDATE resources SET name = $1, description = $2, category = $3, is_active = $4, updated_at = CURRENT_TIMESTAMP WHERE id = $5 AND company_id = $6 RETURNING *",
      [name, description, category, is_active, id, companyId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: "Resource not found" });
    }

    res.json(result.rows[0]);
  } catch (error) {
    if (error.code === '23505') { // Unique constraint violation
      return res.status(409).json({ error: "Resource with this name already exists" });
    }
    console.error("Error updating resource:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Delete a resource (soft delete)
app.delete("/resources/:id", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  try {
    const { id } = req.params;
    const companyId = req.user.company_id || 1;

    const result = await pool.query(
      "UPDATE resources SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1 AND company_id = $2 RETURNING *",
      [id, companyId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: "Resource not found" });
    }

    res.json({ message: "Resource deactivated successfully" });
  } catch (error) {
    console.error("Error deleting resource:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Role and Location Management Routes
app.put("/users/:id/main-location", authenticateToken, async (req, res) => {
  const { id } = req.params;
  const { main_location } = req.body;
  if (!main_location) {
    return res.status(400).json({ error: "Missing required field: main_location" });
  }
  
  try {
    const result = await pool.query(
      "UPDATE users SET main_location = $1 WHERE id = $2 RETURNING id, username, role, main_location",
      [main_location, id]
    );
    if (result.rows.length === 0) {
      return res.status(404).json({ error: "User not found" });
    }
    res.json(result.rows[0]);
  } catch (error) {
    console.error("Error updating user main location:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.put("/roles/:role/report-to-locations", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  const { role } = req.params;
  const { report_to_locations } = req.body;
  if (!Array.isArray(report_to_locations)) {
    return res.status(400).json({ error: "report_to_locations must be an array" });
  }
  if (role === "staff") {
    return res.status(403).json({ error: "Cannot set report-to locations for staff role" });
  }
  
  try {
    const result = await pool.query(
      "INSERT INTO roles_config (role, report_to_locations) VALUES ($1, $2) ON CONFLICT (role) DO UPDATE SET report_to_locations = $2 RETURNING *",
      [role, JSON.stringify(report_to_locations)]
    );
    res.json(result.rows[0]);
  } catch (error) {
    console.error("Error updating role report-to locations:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.get("/roles/:role/report-to-locations", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  const { role } = req.params;
  try {
    const result = await pool.query(
      "SELECT report_to_locations FROM roles_config WHERE role = $1",
      [role]
    );
    if (result.rows.length === 0) {
      return res.json({ report_to_locations: [] });
    }
    res.json(result.rows[0]);
  } catch (error) {
    console.error("Error fetching role report-to locations:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.get("/roles", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  try {
    const result = await pool.query(
      "SELECT DISTINCT jsonb_array_elements(roles) AS role FROM users"
    );
    const roles = result.rows.map((row) => row.role.replace(/"/g, ""));
    res.json([...new Set(roles)]);
  } catch (error) {
    console.error("Error in /roles:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Company Settings Routes
app.get("/company-settings", authenticateToken, async (req, res) => {
  try {
    const companyId = await getCompanyIdFromToken(req);

    if (!companyId) {
      return res.status(400).json({ error: "No company_id found" });
    }

    // Check if company_settings table structure is correct
    try {
      // Try to query the table structure first
      const tableInfo = await pool.query("SELECT column_name FROM information_schema.columns WHERE table_name = 'company_settings'");
      
      // Check if company_id column exists
      const hasCompanyIdColumn = tableInfo.rows.some(row => row.column_name === 'company_id');

      if (!hasCompanyIdColumn) {
        // If company_id is the primary key, we need to adjust our query
        const settingsResult = await pool.query("SELECT * FROM company_settings LIMIT 1");
      }
    } catch (err) {
      console.log('Error checking table structure:', err.message);
    }

    // Fetch actual company settings from database
    // Based on the dump, company_id is the primary key, so we use it directly
    let settingsResult = await pool.query(
      "SELECT * FROM company_settings WHERE company_id = $1",
      [companyId]
    );

    let settings;
    if (settingsResult.rows.length === 0) {
      // Skip company existence check since we're using a default company ID
      // console.log(`Creating default settings for company ID ${companyId}`);

      // Create default settings if none exist
      const defaultSettings = {
        company_id: companyId,
        active_modules: ["EMS", "Fire", "Police", "Medical"],
        form_configs: {},
        locations: []
      };

      try {
        await pool.query(
          "INSERT INTO company_settings (company_id, active_modules, form_configs, locations, ai_prompts, ai_preconditions) VALUES ($1, $2, $3, $4, $5, $6)",
          [companyId, defaultSettings.active_modules, defaultSettings.form_configs, defaultSettings.locations, JSON.stringify({}), JSON.stringify({})]
        );
      } catch (insertError) {
        console.error("Error creating company settings:", insertError);
        return res.status(500).json({ error: "Failed to create company settings", details: insertError.message });
      }

      settings = {
        id: companyId,
        company_name: "AlertComm1",
        active_modules: defaultSettings.active_modules,
        locations: defaultSettings.locations,
        form_configs: defaultSettings.form_configs,
        ai_prompts: {},
        ai_preconditions: {},
        notification_settings: {
          voice_call: true,
          sms: true,
          web_app: true,
          email: true
        }
      };
    } else {
      const dbSettings = settingsResult.rows[0];
      settings = {
        id: companyId,
        company_name: "AlertComm1",
        active_modules: dbSettings.active_modules || ["EMS", "Fire", "Police", "Medical"],
        locations: dbSettings.locations || [],
        form_configs: dbSettings.form_configs || {},
        ai_prompts: dbSettings.ai_prompts || {},
        ai_preconditions: dbSettings.ai_preconditions || {},
        notification_settings: {
          voice_call: true,
          sms: true,
          web_app: true,
          email: true
        }
      };
    }
    
    res.json(settings);
  } catch (err) {
    console.error("Error fetching company settings:", err);
    res.status(500).json({ error: err.message });
  }
});

app.post("/company-settings/form-config", authenticateToken, async (req, res) => {
  try {
    const companyId = await getCompanyIdFromToken(req);
    const { module, formConfig } = req.body;

    console.log(`Updating form config for company ${companyId}, module ${module}:`, formConfig);

    // Get current form configs
    let currentConfigs = {};
    const result = await pool.query(
      "SELECT form_configs FROM company_settings WHERE company_id = $1",
      [companyId]
    );

    if (result.rows.length > 0) {
      currentConfigs = result.rows[0].form_configs || {};
    }

    // Update the specific module config
    currentConfigs[module] = formConfig;

    // Save updated form configs to database
    await pool.query(
      `INSERT INTO company_settings (company_id, form_configs)
       VALUES ($1, $2)
       ON CONFLICT (company_id)
       DO UPDATE SET form_configs = $2, updated_at = CURRENT_TIMESTAMP`,
      [companyId, JSON.stringify(currentConfigs)]
    );

    console.log('Form config saved to database successfully');
    res.json({ message: "Form configuration updated successfully" });
  } catch (err) {
    console.error("Error updating form config:", err);
    res.status(500).json({ error: err.message });
  }
});

app.post("/company-settings/locations", authenticateToken, async (req, res) => {
  try {
    const companyId = await getCompanyIdFromToken(req);
    const { locations } = req.body;

    console.log(`Updating locations for company ${companyId}:`, locations);

    // Update locations in the database
    await pool.query(
      `INSERT INTO company_settings (company_id, locations)
       VALUES ($1, $2)
       ON CONFLICT (company_id)
       DO UPDATE SET locations = $2, updated_at = CURRENT_TIMESTAMP`,
      [companyId, JSON.stringify(locations)]
    );

    console.log('Locations saved to database successfully');
    res.json({ message: "Locations updated successfully" });
  } catch (err) {
    console.error("Error updating locations:", err);
    res.status(500).json({ error: err.message });
  }
});

// Update active modules
app.post("/company-settings/active-modules", authenticateToken, async (req, res) => {
  try {
    const companyId = await getCompanyIdFromToken(req);
    const { activeModules } = req.body;

    console.log(`Updating active modules for company ${companyId}:`, activeModules);

    // Update active modules in the database
    await pool.query(
      `INSERT INTO company_settings (company_id, active_modules)
       VALUES ($1, $2)
       ON CONFLICT (company_id)
       DO UPDATE SET active_modules = $2, updated_at = CURRENT_TIMESTAMP`,
      [companyId, JSON.stringify(activeModules)]
    );

    console.log('Active modules saved to database successfully');
    res.json({ message: "Active modules updated successfully" });
  } catch (err) {
    console.error("Error updating active modules:", err);
    res.status(500).json({ error: err.message });
  }
});

// AI Settings Management Routes (Super Admin only)
app.get("/ai-settings", authenticateToken, async (req, res) => {  
  try {
    // Check if user is Super Admin (ttornstrom or user ID 2)
    const isSupeAdmin = req.user.username === 'ttornstrom' || req.user.id === 2;
    if (!isSupeAdmin) {
      return res.status(403).json({
        error: "Access denied. Only Super Admin can access AI settings."
      });
    }

    const companyId = await getCompanyIdFromToken(req);

    const settingsResult = await pool.query(
      "SELECT ai_prompts, ai_preconditions FROM company_settings WHERE company_id = $1",
      [companyId]
    );

    let aiSettings = {
      ai_prompts: {},
      ai_preconditions: {}
    };

    if (settingsResult.rows.length > 0) {
      aiSettings = {
        ai_prompts: settingsResult.rows[0].ai_prompts || {},
        ai_preconditions: settingsResult.rows[0].ai_preconditions || {}
      };
    }

    // Set default prompts if not configured
    if (!aiSettings.ai_prompts.document_summary) {
      aiSettings.ai_prompts.document_summary = "You are an AI assistant helping emergency responders analyze documents. Provide a concise summary of the document focusing on key information relevant to emergency response, including procedures, contacts, resources, and safety guidelines. Format the summary with bullet points for easy reading.";
    }

    if (!aiSettings.ai_preconditions.document_qa_prompt) {
      aiSettings.ai_preconditions.document_qa_prompt = "You are an AI assistant helping emergency responders analyze documents. Provide accurate, concise answers based on the uploaded documents.";
    }

    if (!aiSettings.ai_preconditions.action_generation_prompt) {
      aiSettings.ai_preconditions.action_generation_prompt = "You are an AI assistant helping emergency responders. Generate specific, actionable tasks based on the user's role and event type.";
    }

    res.json(aiSettings);
  } catch (err) {
    console.error("Error fetching AI settings:", err);
    res.status(500).json({ error: err.message });
  }
});

app.put("/ai-settings", authenticateToken, async (req, res) => {
  try {
    // Check if user is Super Admin (ttornstrom or user ID 2)
    const isSupeAdmin = req.user.username === 'ttornstrom' || req.user.id === 2;
    console.log('AI Settings PUT - User:', req.user.username, 'ID:', req.user.id, 'Is Super Admin:', isSupeAdmin);
    console.log('Request body:', JSON.stringify(req.body, null, 2));

    if (!isSupeAdmin) {
      return res.status(403).json({
        error: "Access denied. Only Super Admin can modify AI settings."
      });
    }

    const { ai_prompts, ai_preconditions } = req.body;
    const companyId = await getCompanyIdFromToken(req);

    console.log('Saving AI settings for company ID:', companyId);
    console.log('AI Prompts to save:', JSON.stringify(ai_prompts, null, 2));
    console.log('AI Preconditions to save:', JSON.stringify(ai_preconditions, null, 2));

    // Update AI settings
    const updateResult = await pool.query(
      "UPDATE company_settings SET ai_prompts = $1, ai_preconditions = $2, updated_at = CURRENT_TIMESTAMP WHERE company_id = $3 RETURNING *",
      [JSON.stringify(ai_prompts || {}), JSON.stringify(ai_preconditions || {}), companyId]
    );

    console.log('Update result:', updateResult.rows.length, 'rows affected');

    if (updateResult.rows.length === 0) {
      // Create new settings if none exist
      console.log('No existing settings found, creating new record...');
      const insertResult = await pool.query(
        "INSERT INTO company_settings (company_id, ai_prompts, ai_preconditions) VALUES ($1, $2, $3) RETURNING *",
        [companyId, JSON.stringify(ai_prompts || {}), JSON.stringify(ai_preconditions || {})]
      );
      console.log('Insert result:', insertResult.rows.length, 'rows created');
    } else {
      console.log('Updated existing settings:', JSON.stringify(updateResult.rows[0], null, 2));
    }

    res.json({
      success: true,
      message: "AI settings updated successfully",
      ai_prompts: ai_prompts || {},
      ai_preconditions: ai_preconditions || {}
    });
  } catch (err) {
    console.error("Error updating AI settings:", err);
    res.status(500).json({ error: err.message });
  }
});

// Checklist Template Management Routes
app.get("/checklist-templates", authenticateToken, async (req, res) => {
  try {
    const result = await pool.query(
      "SELECT * FROM checklist_templates WHERE is_active = true ORDER BY created_at DESC"
    );
    res.json(result.rows);
  } catch (err) {
    console.error("Error fetching checklist templates:", err);
    res.status(500).json({ error: err.message });
  }
});

app.post("/checklist-templates", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  try {
    const { name, description, template_data } = req.body;

    if (!name || !template_data) {
      return res.status(400).json({
        error: "Missing required fields: name, template_data"
      });
    }

    const result = await pool.query(
      "INSERT INTO checklist_templates (name, description, template_data, created_by) VALUES ($1, $2, $3, $4) RETURNING *",
      [name, description, JSON.stringify(template_data), req.user.id]
    );

    res.json({
      success: true,
      message: "Checklist template created successfully",
      template: result.rows[0]
    });
  } catch (err) {
    console.error("Error creating checklist template:", err);
    res.status(500).json({ error: err.message });
  }
});

app.put("/checklist-templates/:id", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, template_data } = req.body;

    const result = await pool.query(
      "UPDATE checklist_templates SET name = $1, description = $2, template_data = $3, updated_at = CURRENT_TIMESTAMP WHERE id = $4 RETURNING *",
      [name, description, JSON.stringify(template_data), id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: "Template not found" });
    }

    res.json({
      success: true,
      message: "Checklist template updated successfully",
      template: result.rows[0]
    });
  } catch (err) {
    console.error("Error updating checklist template:", err);
    res.status(500).json({ error: err.message });
  }
});

app.delete("/checklist-templates/:id", authenticateToken, checkRole(["commander", "lead"]), async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(
      "UPDATE checklist_templates SET is_active = false WHERE id = $1 RETURNING *",
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: "Template not found" });
    }

    res.json({
      success: true,
      message: "Checklist template deleted successfully"
    });
  } catch (err) {
    console.error("Error deleting checklist template:", err);
    res.status(500).json({ error: err.message });
  }
});

// Action Items Status Routes
app.get("/events/:eventId/action-items", authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params;

    const result = await pool.query(
      "SELECT * FROM action_items WHERE event_id = $1 ORDER BY created_at DESC",
      [eventId]
    );

    res.json(result.rows);
  } catch (err) {
    console.error("Error fetching action items:", err);
    res.status(500).json({ error: err.message });
  }
});

app.put("/events/:eventId/action-items/:itemId", authenticateToken, async (req, res) => {
  try {
    const { eventId, itemId } = req.params;
    const { is_completed } = req.body;

    const result = await pool.query(
      "UPDATE action_items SET is_completed = $1, completed_at = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $3 AND event_id = $4 RETURNING *",
      [is_completed, is_completed ? new Date() : null, itemId, eventId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: "Action item not found" });
    }

    res.json({
      success: true,
      message: "Action item updated successfully",
      item: result.rows[0]
    });
  } catch (err) {
    console.error("Error updating action item:", err);
    res.status(500).json({ error: err.message });
  }
});

// User Main Locations Routes (for Settings page)
app.get("/user/main-locations", authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    // Create table if it doesn't exist
    await pool.query(`
      CREATE TABLE IF NOT EXISTS user_main_locations (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        common_name VARCHAR(255) NOT NULL,
        address TEXT,
        city VARCHAR(100),
        state VARCHAR(50),
        zip VARCHAR(20),
        latitude DECIMAL(10, 8),
        longitude DECIMAL(11, 8),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    const result = await pool.query(
      "SELECT * FROM user_main_locations WHERE user_id = $1 ORDER BY created_at DESC",
      [userId]
    );

    console.log(`Fetched ${result.rows.length} locations for user ${userId}`);
    res.json(result.rows);
  } catch (err) {
    console.error("Error fetching user main locations:", err);
    res.status(500).json({ error: err.message });
  }
});

app.post("/user/main-locations", authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { locations } = req.body;

    console.log(`Updating main locations for user ${userId}:`, locations);

    // Create table if it doesn't exist
    await pool.query(`
      CREATE TABLE IF NOT EXISTS user_main_locations (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        common_name VARCHAR(255) NOT NULL,
        address TEXT,
        city VARCHAR(100),
        state VARCHAR(50),
        zip VARCHAR(20),
        latitude DECIMAL(10, 8),
        longitude DECIMAL(11, 8),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Delete existing locations for this user
    await pool.query("DELETE FROM user_main_locations WHERE user_id = $1", [userId]);

    // Insert new locations
    for (const location of locations) {
      await pool.query(
        `INSERT INTO user_main_locations (user_id, common_name, address, city, state, zip, latitude, longitude)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
        [
          userId,
          location.commonName || '',
          location.address || '',
          location.city || '',
          location.state || '',
          location.zip || '',
          location.latitude || null,
          location.longitude || null
        ]
      );
    }

    console.log('User main locations saved to database successfully');
    res.json({ message: "User main locations updated successfully" });
  } catch (err) {
    console.error("Error updating user main locations:", err);
    res.status(500).json({ error: err.message });
  }
});

// Reporting and Dashboard Routes
app.get("/reporting-stats", authenticateToken, checkRole(["commander"]), async (req, res) => {
  try {
    const eventsResult = await pool.query(
      "SELECT status, urgency, scale, created_at, COUNT(*) as count FROM events GROUP BY status, urgency, scale, created_at"
    );
    const totalEvents = eventsResult.rows.reduce((sum, row) => sum + parseInt(row.count), 0);
    const activeEvents = eventsResult.rows.filter((row) => row.status !== "resolved").reduce((sum, row) => sum + parseInt(row.count), 0);
    const resolvedEvents = eventsResult.rows.find((row) => row.status === "resolved")?.count || 0;

    const eventsByUrgency = eventsResult.rows.reduce((acc, row) => {
      acc[row.urgency.toLowerCase()] = (acc[row.urgency.toLowerCase()] || 0) + parseInt(row.count);
      return acc;
    }, {});

    const eventsByScale = eventsResult.rows.reduce((acc, row) => {
      acc[row.scale] = (acc[row.scale] || 0) + parseInt(row.count);
      return acc;
    }, {});

    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const eventsByDayResult = await pool.query(
      "SELECT DATE(created_at) as date, COUNT(*) as count FROM events WHERE created_at >= $1 GROUP BY DATE(created_at) ORDER BY date DESC",
      [sevenDaysAgo]
    );
    const eventsByDay = eventsByDayResult.rows.map((row) => ({
      date: row.date.toISOString().split("T")[0],
      count: parseInt(row.count),
    }));

    const tasksResult = await pool.query(`
      SELECT DISTINCT t.assigned_to, t.status, t.created_at, e.created_at AS event_created_at, u.username
      FROM tasks t JOIN events e ON t.event_id = e.id JOIN users u ON t.assigned_to = u.id
    `);
    
    const notifiedResponders = new Set(tasksResult.rows.map((task) => task.assigned_to)).size;
    const respondedTasks = tasksResult.rows.filter((task) =>
      ["acknowledged", "enroute", "arrived"].includes(task.status.toLowerCase())
    );
    const responsePercentage = notifiedResponders > 0 ? Math.round((respondedTasks.length / notifiedResponders) * 100) : 0;

    const acknowledgeTimes = tasksResult.rows
      .filter((task) => task.status.toLowerCase() === "acknowledged")
      .map((task) => (new Date(task.created_at) - new Date(task.event_created_at)) / (1000 * 60));
    const avgAcknowledgeTime = acknowledgeTimes.length > 0 ? Math.round(acknowledgeTimes.reduce((a, b) => a + b, 0) / acknowledgeTimes.length) : "N/A";

    const responseTimes = tasksResult.rows
      .filter((task) => task.status.toLowerCase() === "enroute")
      .map((task) => (new Date(task.created_at) - new Date(task.event_created_at)) / (1000 * 60));
    const avgResponseTime = responseTimes.length > 0 ? Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length) : "N/A";

    const topResponders = tasksResult.rows
      .filter((task) => task.status.toLowerCase() === "enroute")
      .reduce((acc, task) => {
        const time = (new Date(task.created_at) - new Date(task.event_created_at)) / (1000 * 60);
        if (!acc[task.assigned_to]) {
          acc[task.assigned_to] = { username: task.username, times: [] };
        }
        acc[task.assigned_to].times.push(time);
        return acc;
      }, {});
      
    const topRespondersArray = Object.entries(topResponders)
      .map(([id, { username, times }]) => ({
        username,
        avgResponseTime: Math.round(times.reduce((a, b) => a + b, 0) / times.length),
      }))
      .sort((a, b) => a.avgResponseTime - b.avgResponseTime)
      .slice(0, 5);

    const responderStatus = tasksResult.rows.reduce((acc, task) => {
      const status = task.status.toLowerCase();
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});

    const locationsResult = await pool.query(
      "SELECT DISTINCT report_to_location FROM tasks WHERE report_to_location IS NOT NULL"
    );
    const assignedLocations = locationsResult.rows.length;

    const stats = {
      totalEvents, activeEvents, resolvedEvents, notifiedResponders, responsePercentage,
      avgAcknowledgeTime, avgResponseTime, assignedLocations, eventsByUrgency,
      eventsByScale, eventsByDay, topResponders: topRespondersArray, responderStatus,
    };
    res.json(stats);
  } catch (error) {
    console.error("Error in /reporting-stats:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.get("/dashboard", authenticateToken, checkRole(["commander", "lead", "staff"]), (req, res) => {
  res.send(`Welcome, ${req.user.role}!`);
});

app.get("/view-only", authenticateToken, checkRole(["commander", "lead", "staff", "viewer"]), (req, res) => {
  res.send("View-only data");
});

// Action Items Management Routes
app.get("/events/:eventId/actions", authenticateToken, async (req, res) => {
  const { eventId } = req.params;
  const userId = req.user.id;

  try {
    const result = await pool.query(
      "SELECT * FROM action_items WHERE event_id = $1 AND user_id = $2 ORDER BY action_index",
      [eventId, userId]
    );
    res.json(result.rows);
  } catch (error) {
    console.error("Error fetching action items:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.post("/events/:eventId/actions", authenticateToken, async (req, res) => {
  const { eventId } = req.params;
  const { actions, userRole } = req.body;
  const userId = req.user.id;

  try {
    // Clear existing actions for this user and event
    await pool.query(
      "DELETE FROM action_items WHERE event_id = $1 AND user_id = $2",
      [eventId, userId]
    );

    // Insert new actions
    const insertPromises = actions.map((action, index) => {
      return pool.query(
        "INSERT INTO action_items (event_id, user_id, action_text, user_role, action_index) VALUES ($1, $2, $3, $4, $5)",
        [eventId, userId, action, userRole, index]
      );
    });

    await Promise.all(insertPromises);
    res.json({ message: "Actions saved successfully" });
  } catch (error) {
    console.error("Error saving action items:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

app.put("/events/:eventId/actions/:actionIndex", authenticateToken, async (req, res) => {
  const { eventId, actionIndex } = req.params;
  const { isCompleted } = req.body;
  const userId = req.user.id;

  try {
    const completedAt = isCompleted ? new Date() : null;

    const result = await pool.query(
      "UPDATE action_items SET is_completed = $1, completed_at = $2, updated_at = CURRENT_TIMESTAMP WHERE event_id = $3 AND user_id = $4 AND action_index = $5 RETURNING *",
      [isCompleted, completedAt, eventId, userId, actionIndex]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: "Action item not found" });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error("Error updating action item:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Configuration endpoint for frontend URLs
app.get("/config", (req, res) => {
  res.json({
    apiBaseUrl: API_BASE_URL,
    frontendBaseUrl: FRONTEND_BASE_URL,
    features: {
      signupEnabled: false,
      mainSiteUrl: "https://alertcomm1.com"
    }
  });
});

app.get("/common-locations", authenticateToken, async (req, res) => {
  try {
    // First, try to create the table if it doesn't exist
    await pool.query(`
      CREATE TABLE IF NOT EXISTS common_locations (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        address VARCHAR(500),
        city VARCHAR(100),
        state VARCHAR(50),
        zip VARCHAR(20),
        lat DECIMAL(10, 8),
        lng DECIMAL(11, 8),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Check if table has any data, if not, insert some default locations
    const countResult = await pool.query("SELECT COUNT(*) FROM common_locations");
    const count = parseInt(countResult.rows[0].count);

    if (count === 0) {
      // Insert some default common locations
      await pool.query(`
        INSERT INTO common_locations (name, address, city, state, zip) VALUES
        ('Fire Station 1', '123 Main St', 'Downtown', 'CA', '90210'),
        ('Police Station', '456 Oak Ave', 'Midtown', 'CA', '90211'),
        ('Hospital Emergency', '789 Health Blvd', 'Medical District', 'CA', '90212'),
        ('City Hall', '321 Government Way', 'Civic Center', 'CA', '90213'),
        ('Emergency Operations Center', '654 Response Dr', 'Emergency District', 'CA', '90214')
      `);
      console.log("Inserted default common locations");
    }

    const result = await pool.query("SELECT * FROM common_locations ORDER BY name ASC");
    res.json(result.rows);
  } catch (error) {
    console.error("Error fetching common locations:", error.message);
    res.status(500).json({ error: "Server error", details: error.message });
  }
});

// Short URL route
app.get("/s/:shortCode", async (req, res) => {
  try {
    const { shortCode } = req.params;
    
    const result = await pool.query(
      "SELECT original_url, expires_at FROM short_urls WHERE short_code = $1",
      [shortCode]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).send("Short URL not found");
    }
    
    const { original_url, expires_at } = result.rows[0];
    
    if (expires_at && new Date() > new Date(expires_at)) {
      return res.status(410).send("This link has expired");
    }
    
    await pool.query(
      "UPDATE short_urls SET click_count = click_count + 1 WHERE short_code = $1",
      [shortCode]
    );
    
    res.redirect(original_url);
  } catch (error) {
    console.error("Error redirecting short URL:", error.message);
    res.status(500).send("Server error");
  }
});

// WebSocket events
io.on("connection", (socket) => {
  console.log("A user connected");
  
  socket.on("join", (room) => {
    socket.join(room);
    console.log(`User joined room: ${room}`);
  });
  
  socket.on("leave", (room) => {
    socket.leave(room);
    console.log(`User left room: ${room}`);
  });
  
  socket.on("chat", async (message) => {
    console.log("Chat message received via Socket.IO:", message);

    try {
      // Save to database if it has the required fields
      if (message.eventId && message.text && message.userId) {
        // Ensure chat_messages table exists
        await pool.query(`
          CREATE TABLE IF NOT EXISTS chat_messages (
            id SERIAL PRIMARY KEY,
            event_id INTEGER REFERENCES events(id) ON DELETE CASCADE,
            user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
            username VARCHAR(255) NOT NULL,
            text TEXT NOT NULL,
            timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
          )
        `);

        // Insert message into database
        const result = await pool.query(`
          INSERT INTO chat_messages (event_id, user_id, username, text, timestamp)
          VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
          RETURNING *
        `, [message.eventId, message.userId, message.username || `User ${message.userId}`, message.text]);

        const savedMessage = result.rows[0];

        // Create standardized message format
        const standardMessage = {
          id: savedMessage.id,
          eventId: parseInt(message.eventId),
          text: savedMessage.text,
          username: savedMessage.username,
          userId: savedMessage.user_id,
          timestamp: savedMessage.timestamp
        };

        // Broadcast standardized message
        io.to(`event-${message.eventId}`).emit("chat-message", standardMessage);
        console.log("Chat message saved and broadcasted:", standardMessage);
      } else {
        // Just broadcast the original message if missing required fields
        io.to(`event-${message.eventId}`).emit("chat", message);
      }
    } catch (error) {
      console.error("Error handling Socket.IO chat message:", error);
      // Fallback to original behavior
      io.to(`event-${message.eventId}`).emit("chat", message);
    }
  });
  
  socket.on("chat-message", (message) => {
    console.log("Chat message received (new format):", message);
    
    const chatMessage = {
      ...message,
      timestamp: message.timestamp || new Date().toISOString(),
      id: Date.now()
    };
    
    if (message.eventId) {
      io.to(`event-${message.eventId}`).emit("chat-message", chatMessage);
    }
  });
  
  socket.on("task-assigned", (data) => {
    console.log("Task assigned:", data);
    
    if (data.task && data.task.assigned_to) {
      io.to(`user-${data.task.assigned_to}`).emit("task-assigned", data);
    }
  });
  
  socket.on("responder-update", (responder) => {
    console.log("Responder update:", responder);
    socket.broadcast.emit("responder-update", responder);
  });
  
  socket.on("event-closed", (data) => {
    console.log("Event closed broadcast:", data);
    io.emit("new-event", { id: data.id, status: "resolved" });
  });
  
  socket.on("disconnect", () => {
    console.log("User disconnected");
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    code: 'INTERNAL_ERROR'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    code: 'NOT_FOUND'
  });
});

// Start server
server.listen(PORT, () => {
  console.log(`🚀 AlertComm1 Server running on port ${PORT}`);
  console.log(`📱 Landing page: http://localhost:${PORT}`);
  console.log(`👨‍🚒 Responder interface: http://localhost:${PORT}/responder.html?token=test&eventId=1`);
  console.log(`🔗 Test NOE mode: http://localhost:${PORT}/responder.html?token=test&eventId=1&type=notification_only`);
});

// Debug endpoint to test all settings functionality
app.get("/debug/settings/:userId", authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;
    const companyId = await getCompanyIdFromToken(req);

    console.log(`Debug settings for user ${userId}, company ${companyId}`);

    // Get company settings
    const settingsResult = await pool.query(
      "SELECT * FROM company_settings WHERE company_id = $1",
      [companyId]
    );

    // Get company codes
    const codesResult = await pool.query(
      "SELECT * FROM companys WHERE user_id = $1",
      [userId]
    );

    // Get user info
    const userResult = await pool.query(
      "SELECT id, email, company_id FROM users WHERE id = $1",
      [userId]
    );

    const debugInfo = {
      userId,
      companyId,
      user: userResult.rows[0] || null,
      companySettings: settingsResult.rows[0] || null,
      companyCodes: codesResult.rows || [],
      tableStructure: {
        company_settings: await pool.query("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'company_settings'"),
        companys: await pool.query("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'companys'"),
        users: await pool.query("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'users'")
      }
    };

    res.json(debugInfo);
  } catch (err) {
    console.error("Debug endpoint error:", err);
    res.status(500).json({ error: err.message });
  }
});

module.exports = { app, server, io };