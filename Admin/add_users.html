<!DOCTYPE html>
<html>
  <head>
    <title>AlertComm1 Add Users</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
      }
      nav {
        background: #333;
        padding: 10px;
        margin-bottom: 20px;
      }
      nav a {
        color: white;
        margin-right: 15px;
        text-decoration: none;
      }
      nav a:hover {
        text-decoration: underline;
      }
      .container {
        max-width: 400px;
        margin: auto;
      }
      input,
      select,
      button,
      textarea {
        display: block;
        width: 100%;
        margin: 10px 0;
        padding: 8px;
        box-sizing: border-box;
      }
      textarea {
        height: 100px;
      }
      button {
        background: #4caf50;
        color: white;
        border: none;
        cursor: pointer;
      }
      button:hover {
        background: #45a049;
      }
      #message {
        color: red;
        text-align: center;
      }
    </style>
  </head>
  <body>
    <nav>
      <a href="/signup.html">Signup</a>
      <a href="/add-users.html">Add Users</a>
      <a href="/index.html">Dashboard</a>
      <a href="/responder.html">Responder</a>
    </nav>
    <div class="container">
      <h1>Add Users (Admin Only)</h1>
      <h2>Add Single User</h2>
      <input id="email" type="email" placeholder="Email" required />
      <input id="username" type="text" placeholder="Username" required />
      <input id="password" type="password" placeholder="Password" required />
      <select id="role" required>
        <option value="" disabled selected>Select Role</option>
        <option value="staff">Staff</option>
        <option value="lead">Lead</option>
        <option value="commander">Commander</option>
        <option value="viewer">Viewer</option>
      </select>
      <button onclick="addUser()">Add User</button>
      <h2>Import Users</h2>
      <textarea
        id="csv-input"
        placeholder="CSV: username,password,role,email
e.g., staff2,pass123,staff,<EMAIL>"
      ></textarea>
      <button onclick="importUsers()">Import</button>
      <div id="message"></div>
    </div>
    <script>
      const token =
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwicm9sZSI6ImNvbW1hbmRlciIsImlhdCI6MTc0MTU3MjA0NywiZXhwIjoxNzQxNTc1NjQ3fQ.L4G4k1CGoI312UnoCF5IHqV4MibAsqGRIo3gCVGoreM"; // Replace with fresh admin token

      function addUser() {
        const email = document.getElementById("email").value;
        const username = document.getElementById("username").value;
        const password = document.getElementById("password").value;
        const role = document.getElementById("role").value;
        const messageDiv = document.getElementById("message");

        if (!email || !username || !password || !role) {
          messageDiv.innerText = "Please fill in all fields";
          return;
        }

        fetch(`${window.location.origin}/add-user`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ email, username, password, role }),
        })
          .then((response) => {
            if (!response.ok)
              return response.json().then((data) => {
                throw new Error(data.error);
              });
            return response.json();
          })
          .then((data) => {
            messageDiv.style.color = "green";
            messageDiv.innerText = `User added: ${username}`;
            document.getElementById("email").value = "";
            document.getElementById("username").value = "";
            document.getElementById("password").value = "";
            document.getElementById("role").value = "";
          })
          .catch((error) => {
            messageDiv.style.color = "red";
            messageDiv.innerText = `Error: ${error.message}`;
          });
      }

      function importUsers() {
        const csv = document.getElementById("csv-input").value;
        const messageDiv = document.getElementById("message");

        if (!csv.trim()) {
          messageDiv.innerText = "Please enter CSV data";
          return;
        }

        fetch(`${window.location.origin}/import-users`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ csv }),
        })
          .then((response) => {
            if (!response.ok)
              return response.json().then((data) => {
                throw new Error(data.error);
              });
            return response.json();
          })
          .then((data) => {
            messageDiv.style.color = "green";
            messageDiv.innerText = `Imported ${data.count} users`;
            document.getElementById("csv-input").value = "";
          })
          .catch((error) => {
            messageDiv.style.color = "red";
            messageDiv.innerText = `Error: ${error.message}`;
          });
      }
    </script>
  </body>
</html>
