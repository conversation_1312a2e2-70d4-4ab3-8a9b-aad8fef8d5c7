<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - AlertComm1</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        color: #333;
      }
      
      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px 0;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      }
      
      .header-content {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .logo {
        display: flex;
        align-items: center;
        gap: 15px;
      }
      
      .logo-icon {
        font-size: 32px;
      }
      
      .logo-text {
        font-size: 24px;
        font-weight: bold;
      }
      
      .user-info {
        display: flex;
        align-items: center;
        gap: 20px;
      }
      
      .user-details {
        text-align: right;
      }
      
      .user-name {
        font-weight: 600;
        font-size: 16px;
      }
      
      .user-role {
        font-size: 14px;
        opacity: 0.9;
        text-transform: capitalize;
      }
      
      .logout-btn {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        padding: 8px 16px;
        border-radius: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        font-size: 14px;
      }
      
      .logout-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
      }
      
      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 40px 20px;
      }
      
      .welcome-section {
        text-align: center;
        margin-bottom: 40px;
      }
      
      .welcome-title {
        font-size: 32px;
        font-weight: bold;
        color: #333;
        margin-bottom: 10px;
      }
      
      .welcome-subtitle {
        font-size: 18px;
        color: #666;
        margin-bottom: 30px;
      }
      
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 25px;
        margin-bottom: 40px;
      }
      
      .stat-card {
        background: white;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        text-align: center;
        transition: all 0.3s ease;
        border: 1px solid rgba(0, 0, 0, 0.05);
      }
      
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
      }
      
      .stat-icon {
        font-size: 48px;
        margin-bottom: 15px;
        display: block;
      }
      
      .stat-number {
        font-size: 36px;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 10px;
      }
      
      .stat-label {
        font-size: 16px;
        color: #666;
        font-weight: 500;
      }
      
      .actions-section {
        background: white;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
      }
      
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #333;
        margin-bottom: 20px;
        text-align: center;
      }
      
      .action-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
      }
      
      .action-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border: none;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
      }
      
      .action-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
      }
      
      .action-btn-icon {
        font-size: 24px;
      }
      
      .loading {
        text-align: center;
        padding: 20px;
        color: #666;
      }
      
      .error {
        background: #f8d7da;
        color: #721c24;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        text-align: center;
      }
      
      @media (max-width: 768px) {
        .header-content {
          flex-direction: column;
          gap: 15px;
          text-align: center;
        }
        
        .user-info {
          flex-direction: column;
          gap: 10px;
        }
        
        .welcome-title {
          font-size: 24px;
        }
        
        .welcome-subtitle {
          font-size: 16px;
        }
        
        .stats-grid {
          grid-template-columns: 1fr;
        }
        
        .action-buttons {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <div class="header">
      <div class="header-content">
        <div class="logo">
          <span class="logo-icon">🚨</span>
          <span class="logo-text">AlertComm1</span>
        </div>
        <div class="user-info">
          <div class="user-details">
            <div class="user-name" id="userName">Loading...</div>
            <div class="user-role" id="userRole">Loading...</div>
          </div>
          <button class="logout-btn" onclick="logout()">Sign Out</button>
        </div>
      </div>
    </div>

    <div class="container">
      <div class="welcome-section">
        <h1 class="welcome-title">Welcome to Your Dashboard</h1>
        <p class="welcome-subtitle">Emergency Response Command Center</p>
      </div>

      <div id="errorMessage" class="error" style="display: none;"></div>
      
      <div id="loadingMessage" class="loading">Loading dashboard data...</div>
      
      <div id="dashboardContent" style="display: none;">
        <div class="stats-grid">
          <div class="stat-card">
            <span class="stat-icon">📊</span>
            <div class="stat-number" id="totalEvents">0</div>
            <div class="stat-label">Total Events</div>
          </div>
          
          <div class="stat-card">
            <span class="stat-icon">🚨</span>
            <div class="stat-number" id="activeEvents">0</div>
            <div class="stat-label">Active Events</div>
          </div>
          
          <div class="stat-card">
            <span class="stat-icon">👥</span>
            <div class="stat-number" id="totalResponders">0</div>
            <div class="stat-label">Total Responders</div>
          </div>
          
          <div class="stat-card">
            <span class="stat-icon">✅</span>
            <div class="stat-number" id="myTasks">0</div>
            <div class="stat-label">My Tasks</div>
          </div>
        </div>

        <div class="actions-section">
          <h2 class="section-title">Quick Actions</h2>
          <div class="action-buttons">
            <a href="#" class="action-btn" onclick="createEvent()">
              <span class="action-btn-icon">➕</span>
              Create New Event
            </a>
            <a href="#" class="action-btn" onclick="viewEvents()">
              <span class="action-btn-icon">📋</span>
              View All Events
            </a>
            <a href="#" class="action-btn" onclick="viewResponders()">
              <span class="action-btn-icon">👥</span>
              Manage Responders
            </a>
            <a href="#" class="action-btn" onclick="viewReports()">
              <span class="action-btn-icon">📈</span>
              View Reports
            </a>
          </div>
        </div>
      </div>
    </div>

    <script>
      let userToken = null;
      let userData = null;
      let config = null;

      // Check authentication on page load
      document.addEventListener('DOMContentLoaded', function() {
        loadConfig().then(() => {
          checkAuthentication();
        });
      });

      async function loadConfig() {
        try {
          const response = await fetch(`${window.location.origin}/config`);
          if (response.ok) {
            config = await response.json();
          }
        } catch (error) {
          console.error('Error loading config:', error);
          // Use defaults if config fails to load
          config = {
            frontendBaseUrl: 'http://localhost:3001',
            apiBaseUrl: window.location.origin
          };
        }
      }

      function checkAuthentication() {
        userToken = localStorage.getItem('authToken');
        
        if (!userToken) {
          redirectToLogin();
          return;
        }

        // Verify token and load dashboard data
        loadDashboardData();
      }

      function redirectToLogin() {
        window.location.href = '/login.html';
      }

      async function loadDashboardData() {
        try {
          // Decode token to get user info
          const tokenPayload = JSON.parse(atob(userToken.split('.')[1]));
          userData = tokenPayload;
          
          // Update user info in header
          document.getElementById('userName').textContent = userData.username || 'User';
          document.getElementById('userRole').textContent = userData.role || 'staff';

          // Load dashboard statistics
          await loadStatistics();
          
          // Show dashboard content
          document.getElementById('loadingMessage').style.display = 'none';
          document.getElementById('dashboardContent').style.display = 'block';
          
        } catch (error) {
          console.error('Error loading dashboard:', error);
          showError('Failed to load dashboard data. Please try refreshing the page.');
        }
      }

      async function loadStatistics() {
        try {
          // Load statistics based on user role
          const userRole = userData.role;
          const promises = [];

          // All users can see their own tasks
          promises.push(
            fetch(`${window.location.origin}/my-tasks`, {
              headers: { 'Authorization': `Bearer ${userToken}` }
            }).catch(() => null)
          );

          // Only commanders and staff can see all events
          if (['commander', 'staff'].includes(userRole)) {
            promises.push(
              fetch(`${window.location.origin}/all-events`, {
                headers: { 'Authorization': `Bearer ${userToken}` }
              }).catch(() => null)
            );
          } else {
            promises.push(Promise.resolve(null));
          }

          // Commanders, leads, and staff can see responders
          if (['commander', 'lead', 'staff'].includes(userRole)) {
            promises.push(
              fetch(`${window.location.origin}/responders`, {
                headers: { 'Authorization': `Bearer ${userToken}` }
              }).catch(() => null)
            );
          } else {
            promises.push(Promise.resolve(null));
          }

          const [tasksRes, eventsRes, respondersRes] = await Promise.all(promises);

          // Handle tasks response
          if (tasksRes && tasksRes.ok) {
            const tasksData = await tasksRes.json();
            let taskCount = 0;

            if (tasksData.created_events) {
              taskCount += tasksData.created_events.length;
            }
            if (tasksData.assigned_tasks) {
              taskCount += tasksData.assigned_tasks.length;
            }

            document.getElementById('myTasks').textContent = taskCount;
          }

          // Handle events response
          if (eventsRes && eventsRes.ok) {
            const events = await eventsRes.json();
            document.getElementById('totalEvents').textContent = events.length;

            const activeEvents = events.filter(e => e.status !== 'resolved');
            document.getElementById('activeEvents').textContent = activeEvents.length;
          } else {
            // Hide event stats for users who can't access them
            document.querySelector('.stat-card:nth-child(1)').style.display = 'none';
            document.querySelector('.stat-card:nth-child(2)').style.display = 'none';
          }

          // Handle responders response
          if (respondersRes && respondersRes.ok) {
            const responders = await respondersRes.json();
            document.getElementById('totalResponders').textContent = responders.length;
          } else {
            // Hide responder stats for users who can't access them
            document.querySelector('.stat-card:nth-child(3)').style.display = 'none';
          }

        } catch (error) {
          console.error('Error loading statistics:', error);
          // Don't show error for statistics, just keep default values
        }
      }

      function showError(message) {
        const errorDiv = document.getElementById('errorMessage');
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
        document.getElementById('loadingMessage').style.display = 'none';
      }

      function logout() {
        localStorage.removeItem('authToken');
        window.location.href = '/login.html';
      }

      // Quick action functions
      function createEvent() {
        const frontendUrl = config?.frontendBaseUrl || 'http://localhost:3001';
        window.location.href = `${frontendUrl}/events/new`;
      }

      function viewEvents() {
        const frontendUrl = config?.frontendBaseUrl || 'http://localhost:3001';
        window.location.href = `${frontendUrl}/events`;
      }

      function viewResponders() {
        const frontendUrl = config?.frontendBaseUrl || 'http://localhost:3001';
        window.location.href = `${frontendUrl}/users`;
      }

      function viewReports() {
        const frontendUrl = config?.frontendBaseUrl || 'http://localhost:3001';
        window.location.href = `${frontendUrl}/reporting`;
      }
    </script>
  </body>
</html>
