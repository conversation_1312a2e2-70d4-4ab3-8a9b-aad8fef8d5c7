# 🤖 AI Agent Preconditions - Complete Guide

## 📖 **What is AI Agent Preconditions?**

The AI Agent Preconditions section allows **Super Admin users** to customize how AI behaves across the AlertComm system. This includes configuring prompts for document summarization, question-answering, and action generation.

## 🔐 **Access Control**

- **Who can access:** Only users with username `ttornstrom` (Super Admin)
- **Where to find:** Settings page → AI Agent Preconditions section
- **Visibility:** Section only appears for authorized users

## ⚙️ **How It Works**

### **1. Data Flow Architecture**

```
Settings Page (Frontend) 
    ↓ Save AI Settings
Database (company_settings table)
    ↓ Load Custom Prompts  
Backend AI Endpoints
    ↓ Use Custom Prompts
OpenAI API Calls
    ↓ Generate Responses
Frontend Display (Dashboard)
```

### **2. Database Storage**

**Table:** `company_settings`
**Columns:**
- `ai_prompts` (JSONB) - Stores custom prompts for AI features
- `ai_preconditions` (JSONB) - Stores AI behavior configurations
- `updated_at` (TIMESTAMP) - Tracks when settings were last modified

**Example Data Structure:**
```json
{
  "ai_prompts": {
    "document_summary": "Create emergency response summary with numbered steps..."
  },
  "ai_preconditions": {
    "document_qa_prompt": "Answer emergency questions with specific details...",
    "action_generation_prompt": "Generate role-specific emergency actions..."
  }
}
```

### **3. Frontend-Backend Integration**

**Save Process:**
1. User modifies prompts in Settings page
2. Frontend sends `PUT /ai-settings` request
3. Backend validates user permissions (`ttornstrom` only)
4. Data saved to `company_settings` table
5. Success confirmation returned to frontend

**Load Process:**
1. Settings page loads for authorized user
2. Frontend sends `GET /ai-settings` request
3. Backend retrieves custom prompts from database
4. Data populated in form fields
5. User can view/edit existing configurations

## 🎛️ **Configuration Options**

### **Document Summary Prompt**
- **Purpose:** Controls how AI summarizes uploaded documents
- **Default:** "You are an AI assistant helping emergency responders..."
- **Usage:** Applied when users click "Generate AI Summary" on documents
- **Output:** Affects the style and content of document summaries

### **Document Q&A Prompt**
- **Purpose:** Controls how AI answers questions about documents
- **Default:** "You are an AI assistant helping emergency responders..."
- **Usage:** Applied when users ask questions in "Ask AI About Documents"
- **Output:** Affects the tone and detail level of AI answers

### **Action Generation Prompt**
- **Purpose:** Controls how AI generates action checklists
- **Default:** "You are an AI assistant helping emergency responders..."
- **Usage:** Applied when generating action items for users
- **Output:** Affects the specificity and relevance of generated actions

## 🔧 **Technical Implementation**

### **Backend Endpoints**

**GET /ai-settings**
- **Purpose:** Retrieve current AI configuration
- **Access:** Super Admin only (`ttornstrom`)
- **Response:** JSON with ai_prompts and ai_preconditions

**PUT /ai-settings**
- **Purpose:** Update AI configuration
- **Access:** Super Admin only (`ttornstrom`)
- **Body:** JSON with updated prompts and preconditions
- **Validation:** Checks user permissions and data format

### **Frontend Components**

**Settings.js - AI Agent Preconditions Section**
- **State Management:** `aiSettings` state for form data
- **Functions:** `loadAiSettings()`, `saveAiSettings()`
- **UI Features:** Collapsible section, form validation, loading states

**Dashboard.js - AI Integration**
- **Document Summary:** Uses custom prompts from settings
- **Q&A Interface:** Applies configured Q&A prompts
- **Visual Indicators:** Shows when custom settings are active

## 🐛 **Troubleshooting**

### **Issue: Data Not Persisting After Save**

**Symptoms:**
- Settings save successfully but disappear on reload
- "Successfully saved" message appears but data is lost

**Possible Causes:**
1. **Database Error:** `updated_at` column missing
2. **Permission Issue:** User not recognized as Super Admin
3. **Loading Error:** Frontend not retrieving saved data

**Solutions:**
```bash
# 1. Fix database schema
psql -U postgres -d alertcom -f fix-updated-at.sql

# 2. Verify user permissions
SELECT username FROM users WHERE username = 'ttornstrom';

# 3. Check saved data
SELECT ai_prompts, ai_preconditions FROM company_settings WHERE company_id = 1;
```

### **Issue: Section Not Visible**

**Check:**
- Login as exact username `ttornstrom` (case-sensitive)
- No JavaScript errors in browser console
- Database migration completed successfully

### **Issue: Custom Prompts Not Working**

**Verify:**
- Settings saved successfully in database
- Backend logs show custom prompts being loaded
- AI responses reflect custom prompt style

## 📊 **Monitoring & Verification**

### **Database Queries**
```sql
-- Check if AI settings exist
SELECT ai_prompts, ai_preconditions 
FROM company_settings 
WHERE company_id = 1;

-- Verify data structure
SELECT 
    ai_prompts->'document_summary' as summary_prompt,
    ai_preconditions->'document_qa_prompt' as qa_prompt
FROM company_settings 
WHERE company_id = 1;
```

### **Frontend Debugging**
```javascript
// Check if settings loaded
console.log('AI Settings:', aiSettings);

// Verify user permissions
console.log('User ID:', userId);
console.log('Is Super Admin:', userId === 'ttornstrom');
```

### **Backend Logging**
```javascript
// In server.js - AI endpoints
console.log('Loading AI settings for user:', req.user.username);
console.log('Custom prompts:', settingsResult.rows[0].ai_prompts);
```

## 🎯 **Best Practices**

### **Prompt Writing Guidelines**
1. **Be Specific:** Include context about emergency response
2. **Set Tone:** Professional, clear, actionable
3. **Include Format:** Specify bullet points, numbered lists, etc.
4. **Add Context:** Mention the user's role and situation

### **Example Effective Prompts**

**Document Summary:**
```
"You are an AI assistant for emergency responders. Create a concise summary focusing on:
1. Key procedures and protocols
2. Emergency contact information  
3. Safety guidelines and warnings
4. Resource requirements
Format as numbered bullet points for quick reference."
```

**Q&A Prompt:**
```
"You are an emergency response AI assistant. Answer questions with:
- Specific, actionable information
- Reference to document sections when possible
- Clear, professional tone
- Emergency-relevant context
Always prioritize safety and accuracy."
```

## 🚀 **Future Enhancements**

### **Planned Features**
- **Role-Based Prompts:** Different prompts for different user roles
- **Template Library:** Pre-built prompt templates for common scenarios
- **A/B Testing:** Compare effectiveness of different prompts
- **Analytics:** Track AI usage and effectiveness metrics

### **Integration Opportunities**
- **Training Mode:** Special prompts for training scenarios
- **Multi-Language:** Prompts in different languages
- **Industry-Specific:** Customized prompts for different emergency types

---

## 📞 **Support**

For technical issues or questions about AI Agent Preconditions:
1. Check this documentation first
2. Verify database and permissions
3. Review browser console for errors
4. Contact system administrator if issues persist

**Remember:** Only Super Admin (`ttornstrom`) can access and modify these settings. All changes affect the entire AlertComm system's AI behavior.
