# 🔍 Debug Guide: Settings Page New Fields Not Saving

## 🚨 **Issue Description**
New fields added to the Settings page are not being saved to the database, even though the save operation appears successful.

## ✅ **Debugging Tools Added**

### **Frontend Debugging**
1. **Enhanced Console Logging:**
   - `updateAiPrompt` and `updateAiPrecondition` functions now log when called
   - `saveAiSettings` logs the complete data being sent
   - State changes are tracked in real-time

2. **Debug Buttons Added:**
   - 🔄 **Load Settings**: Manually trigger `loadAiSettings()`
   - 📊 **Show State**: Display current `aiSettings` state in console
   - 💾 **Save Now**: Manually trigger `saveAiSettings()`

### **Backend Debugging**
- Server logs show detailed save operations
- Database query results are logged
- Permission checks are logged

## 🔧 **Testing Steps**

### **Step 1: Test State Updates**
1. Open Settings page
2. Open browser DevTools → Console
3. Type in any AI prompt field
4. Check console for:
   ```
   🔄 updateAiPrompt called: document_summary value length: [number]
   📝 Updated aiSettings: {ai_prompts: {...}, ai_preconditions: {...}}
   ```

### **Step 2: Test Save Operation**
1. Click "📊 Show State" button
2. Verify `aiSettings` contains your changes
3. Click "💾 Save Now" button
4. Check console for:
   ```
   💾 Saving AI settings: {complete JSON object}
   📡 Save response status: 200
   ✅ Save result: {success: true, ...}
   ```

### **Step 3: Test Database Persistence**
1. Run the database test script:
   ```bash
   psql -U postgres -d alertcom -f test-ai-settings-save.sql
   ```
2. Check for any database errors or constraints

### **Step 4: Test Load After Save**
1. After saving, check console for:
   ```
   🔄 Reloading settings to verify save...
   🔄 loadAiSettings called
   ```
2. Refresh page manually
3. Verify data persists

## 🐛 **Common Issues & Solutions**

### **Issue 1: State Not Updating**
**Symptoms:** No console logs when typing in fields
**Causes:**
- Form fields not connected to update functions
- Event handlers not firing
- State update functions broken

**Debug:**
- Check if `onChange` handlers are attached
- Verify `updateAiPrompt`/`updateAiPrecondition` functions exist
- Test with debug buttons

### **Issue 2: Save Request Fails**
**Symptoms:** Error status codes (403, 500, etc.)
**Causes:**
- Permission issues (user not Super Admin)
- Authentication token problems
- Server errors

**Debug:**
- Check Network tab for actual HTTP status
- Verify user ID is 2 or username is 'ttornstrom'
- Check server logs for errors

### **Issue 3: Database Not Updating**
**Symptoms:** Save succeeds but data doesn't persist
**Causes:**
- Database constraints
- Transaction rollbacks
- JSON serialization issues
- Missing `updated_at` column

**Debug:**
- Run `test-ai-settings-save.sql`
- Check database logs
- Verify table structure

### **Issue 4: Data Loads But Doesn't Display**
**Symptoms:** API returns data but form fields remain empty
**Causes:**
- State not updating after load
- Form fields not reading from state
- Data format mismatch

**Debug:**
- Check `loadAiSettings` console logs
- Verify `setAiSettings` is called
- Check form field `value` attributes

## 📊 **Debugging Checklist**

### **Frontend Checks**
- [ ] Console shows state updates when typing
- [ ] "📊 Show State" displays current form data
- [ ] Save request shows complete data payload
- [ ] No JavaScript errors in console
- [ ] Form fields are controlled components

### **Backend Checks**
- [ ] Server receives PUT request to `/ai-settings`
- [ ] User permission check passes
- [ ] Database query executes successfully
- [ ] Response returns success status
- [ ] Server logs show data being saved

### **Database Checks**
- [ ] `company_settings` table exists
- [ ] `updated_at` column exists
- [ ] Record exists for company_id = 1
- [ ] JSON data is valid format
- [ ] No constraint violations

### **Integration Checks**
- [ ] Save → Load cycle works correctly
- [ ] Page refresh preserves data
- [ ] Multiple save operations work
- [ ] Different field types save correctly

## 🎯 **Expected Debug Output**

### **When Typing in Fields:**
```
🔄 updateAiPrompt called: document_summary value length: 45
📝 Updated aiSettings: {
  "ai_prompts": {
    "document_summary": "New prompt text here..."
  },
  "ai_preconditions": {...}
}
```

### **When Saving:**
```
💾 Saving AI settings: {
  "ai_prompts": {
    "document_summary": "New prompt text here..."
  },
  "ai_preconditions": {
    "document_qa_prompt": "Q&A prompt...",
    "action_generation_prompt": "Action prompt..."
  }
}
📡 Save response status: 200
✅ Save result: {success: true, message: "AI settings updated successfully"}
```

### **Server Logs:**
```
AI Settings PUT - User: [username] ID: 2 Is Super Admin: true
Saving AI settings for company ID: 1
AI Prompts to save: {"document_summary": "New prompt text here..."}
Update result: 1 rows affected
```

## 🚀 **Resolution Steps**

1. **Identify the Break Point:**
   - Use debug buttons to isolate where the process fails
   - Check each step: Type → State Update → Save → Database → Load

2. **Fix the Root Cause:**
   - State issues: Fix update functions
   - Save issues: Fix API or permissions
   - Database issues: Fix schema or constraints
   - Load issues: Fix data retrieval

3. **Verify the Fix:**
   - Test complete save/load cycle
   - Test with page refresh
   - Remove debug tools after confirmation

## 📞 **Next Steps**

1. **Run the debugging tools** and identify where the process breaks
2. **Check the specific error messages** in console and server logs
3. **Run the database test script** to verify database functionality
4. **Report findings** with specific error messages and console output

---

**🎉 Success Criteria:** New fields should save successfully and persist after page reload, with all debug logs showing successful operations at each step.
