# 🔍 Debug Guide: loadAiSettings Not Being Called

## 🚨 **Issue Identified**

The `loadAiSettings` function is not being called when the Settings page loads, which is why saved AI settings don't appear initially.

## ✅ **Fixes Applied**

### **1. Updated useEffect Condition**
```javascript
// OLD - only checked for exact string match
if (userId === 'ttornstrom' && token) {

// NEW - handles multiple formats
const isSupeAdmin = userId === 2 || userId === '2' || userId === 'ttornstrom';
if (isSupeAdmin && token) {
```

### **2. Enhanced Debugging**
- Added console logs to track useEffect execution
- Added type checking for userId
- Added manual test button for loadAiSettings

### **3. Updated Section Visibility**
```javascript
// OLD
{userId === 2 && (

// NEW  
{(userId === 2 || userId === '2' || userId === 'ttornstrom') && (
```

## 🔧 **Testing Steps**

### **Step 1: Check Console Logs**
1. Open browser DevTools → Console
2. Navigate to Settings page
3. Look for these logs:

```
useEffect triggered - userId: [value] type: [string/number] token exists: true
Is Super Admin check: true/false
Loading AI settings for user: [userId]
🔄 loadAiSettings called - token: true baseUrl: true
```

### **Step 2: Use Debug Button**
1. Go to Settings page
2. Scroll to AI Agent Preconditions section
3. Click "🔧 Test Load AI Settings" button
4. Check console for manual trigger logs

### **Step 3: Verify API Call**
1. Open DevTools → Network tab
2. Trigger loadAiSettings (page load or debug button)
3. Look for GET request to `/ai-settings`
4. Check response status and data

## 🐛 **Common Issues & Solutions**

### **Issue: userId is undefined**
**Symptoms:** `useEffect triggered - userId: undefined`
**Solution:** Check how userId is passed to Settings component

### **Issue: userId is string instead of number**
**Symptoms:** `useEffect triggered - userId: "2" type: string`
**Solution:** ✅ Already handled with multiple format check

### **Issue: Token is missing**
**Symptoms:** `token exists: false`
**Solution:** Check authentication and token storage

### **Issue: useEffect not triggering**
**Symptoms:** No console logs at all
**Solution:** Check component mounting and dependencies

## 📊 **Debugging Checklist**

### **Frontend Debugging**
- [ ] Console shows useEffect triggered
- [ ] userId value is correct (2, "2", or "ttornstrom")
- [ ] Token exists and is valid
- [ ] isSupeAdmin evaluates to true
- [ ] loadAiSettings function is called
- [ ] API request is made to /ai-settings

### **Backend Debugging**
- [ ] Server logs show "=== AI SETTINGS REQUEST ===" 
- [ ] User authentication is successful
- [ ] Permission check passes (user ID 2 or username ttornstrom)
- [ ] Database query executes successfully
- [ ] Response is sent back to frontend

### **Data Flow Verification**
- [ ] Settings component receives correct props
- [ ] useEffect dependencies trigger correctly
- [ ] API call completes successfully
- [ ] Response data is processed
- [ ] State is updated with loaded settings
- [ ] Form fields populate with data

## 🔍 **Manual Testing Commands**

### **Test in Browser Console**
```javascript
// Check current userId
console.log('Current userId:', userId, 'type:', typeof userId);

// Manually trigger loadAiSettings
loadAiSettings();

// Check current AI settings state
console.log('Current aiSettings:', aiSettings);
```

### **Test API Endpoint Directly**
```bash
# Replace [token] with actual JWT token
curl -H "Authorization: Bearer [token]" \
     -H "Content-Type: application/json" \
     http://localhost:5000/ai-settings
```

## 🎯 **Expected Behavior After Fix**

### **On Page Load:**
1. Settings component mounts
2. useEffect triggers with correct userId and token
3. isSupeAdmin evaluates to true
4. loadAiSettings is called
5. API request sent to /ai-settings
6. Server validates permissions
7. Database query retrieves saved settings
8. Response sent back with AI settings data
9. Frontend updates state and populates form fields
10. User sees previously saved AI settings

### **Console Output Should Show:**
```
useEffect triggered - userId: 2 type: number token exists: true
Is Super Admin check: true
Loading AI settings for user: 2
🔄 loadAiSettings called - token: true baseUrl: true
Fetching AI settings from: http://localhost:5000/ai-settings
AI settings response status: 200
AI settings loaded successfully: {ai_prompts: {...}, ai_preconditions: {...}}
```

## 🚀 **Next Steps**

1. **Test the Fix:**
   - Refresh Settings page
   - Check console logs
   - Verify AI settings load automatically

2. **Remove Debug Code:**
   - Remove the "🔧 Test Load AI Settings" button after confirming fix works
   - Clean up excessive console logs

3. **Verify Complete Flow:**
   - Load settings ✅
   - Modify settings
   - Save settings
   - Refresh page
   - Verify settings persist

## 📞 **If Issues Persist**

1. **Check userId Source:**
   - Verify how userId is passed to Settings component
   - Check parent component or routing logic

2. **Check Token Validity:**
   - Verify JWT token is not expired
   - Check token format and content

3. **Check Component Dependencies:**
   - Verify useEffect dependencies are correct
   - Check for any blocking conditions

4. **Check API Endpoint:**
   - Verify backend server is running
   - Check API endpoint is accessible
   - Verify database connection

---

**🎉 Success Criteria:** loadAiSettings should be called automatically when Settings page loads, and saved AI settings should appear in the form fields without manual intervention.
