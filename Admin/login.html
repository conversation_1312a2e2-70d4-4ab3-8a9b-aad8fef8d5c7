<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - AlertComm1</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
      }

      .login-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 40px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        width: 100%;
        max-width: 400px;
        text-align: center;
      }

      .logo {
        margin-bottom: 30px;
      }

      .logo-icon {
        font-size: 48px;
        margin-bottom: 10px;
      }

      .logo-text {
        font-size: 28px;
        font-weight: bold;
        color: #333;
        margin-bottom: 10px;
      }

      .subtitle {
        color: #666;
        font-size: 14px;
        margin-bottom: 30px;
      }

      .form-group {
        margin-bottom: 20px;
        text-align: left;
      }

      .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #333;
        font-weight: 500;
      }

      .form-group input {
        width: 100%;
        padding: 15px;
        border: 2px solid #e1e5e9;
        border-radius: 10px;
        font-size: 16px;
        transition: all 0.3s ease;
        background: #fff;
      }

      .form-group input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .login-btn {
        width: 100%;
        padding: 15px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 10px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 20px;
      }

      .login-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
      }

      .login-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }

      .message {
        padding: 12px;
        border-radius: 8px;
        margin-bottom: 20px;
        font-weight: 500;
        display: none;
      }

      .message.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .message.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      .links {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #e1e5e9;
      }

      .links a {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
        margin: 0 10px;
      }

      .links a:hover {
        text-decoration: underline;
      }

      .loading {
        display: none;
        margin-left: 10px;
      }

      .spinner {
        width: 20px;
        height: 20px;
        border: 2px solid #ffffff;
        border-top: 2px solid transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      @media (max-width: 480px) {
        .login-container {
          padding: 30px 20px;
        }

        .logo-text {
          font-size: 24px;
        }

        .form-group input {
          padding: 12px;
        }

        .login-btn {
          padding: 12px;
        }
      }
    </style>
  </head>
  <body>
    <div class="login-container">
      <div class="logo">
        <div class="logo-icon">🚨</div>
        <h1 class="logo-text">AlertComm1</h1>
        <p class="subtitle">Emergency Response System</p>
      </div>

      <form id="loginForm">
        <div class="form-group">
          <label for="username">Username</label>
          <input type="text" id="username" name="username" required autocomplete="username">
        </div>

        <div class="form-group">
          <label for="password">Password</label>
          <input type="password" id="password" name="password" required autocomplete="current-password">
        </div>

        <button type="submit" class="login-btn" id="loginBtn">
          Sign In
          <div class="loading" id="loading">
            <div class="spinner"></div>
          </div>
        </button>

        <div id="message" class="message"></div>
      </form>

      <div class="links">
        <a href="/signup.html">Create Account</a>
        <a href="/">Back to Home</a>
      </div>
    </div>

    <script>
      const loginForm = document.getElementById('loginForm');
      const loginBtn = document.getElementById('loginBtn');
      const loading = document.getElementById('loading');
      const messageDiv = document.getElementById('message');

      loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        await login();
      });

      async function login() {
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;

        if (!username || !password) {
          showMessage('Please fill in all fields', 'error');
          return;
        }

        // Show loading state
        loginBtn.disabled = true;
        loading.style.display = 'inline-block';
        hideMessage();

        try {
          const response = await fetch(`${window.location.origin}/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username, password }),
          });

          const data = await response.json();

          if (data.token) {
            // Store token in localStorage
            localStorage.setItem('authToken', data.token);
            showMessage('Login successful! Redirecting...', 'success');

            // Redirect to dashboard after short delay
            setTimeout(() => {
              window.location.href = '/dashboard.html';
            }, 1500);
          } else {
            showMessage(data.error || 'Login failed. Please check your credentials.', 'error');
          }
        } catch (error) {
          console.error('Login error:', error);
          showMessage('Connection error. Please try again.', 'error');
        } finally {
          // Hide loading state
          loginBtn.disabled = false;
          loading.style.display = 'none';
        }
      }

      function showMessage(text, type) {
        messageDiv.textContent = text;
        messageDiv.className = `message ${type}`;
        messageDiv.style.display = 'block';
      }

      function hideMessage() {
        messageDiv.style.display = 'none';
      }

      // Clear message when user starts typing
      document.getElementById('username').addEventListener('input', hideMessage);
      document.getElementById('password').addEventListener('input', hideMessage);
    </script>
  </body>
</html>
