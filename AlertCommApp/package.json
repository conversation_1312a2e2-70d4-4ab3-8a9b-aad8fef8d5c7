{"name": "alertcommapp", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "build:web": "expo export:web", "build:android": "eas build --platform android --profile production", "build:android-preview": "eas build --platform android --profile preview", "build:ios": "eas build --platform ios --profile production", "build:ios-preview": "eas build --platform ios --profile preview", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "serve": "npx serve dist", "clean": "rm -rf node_modules && npm install", "clean:android": "cd android && ./gradlew clean", "prebuild": "expo prebuild --clean"}, "dependencies": {"@expo/cli": "^0.24.20", "@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "expo": "~53.0.17", "expo-location": "^18.1.6", "expo-notifications": "^0.31.4", "expo-status-bar": "~2.2.3", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-maps": "^1.24.5", "react-native-push-notification": "^8.1.1", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "socket.io-client": "^4.8.1"}, "devDependencies": {"@babel/core": "^7.20.0", "jest": "^29.7.0", "jest-expo": "~53.0.0", "eslint": "^8.57.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-native": "^4.1.0"}, "jest": {"preset": "jest-expo", "testMatch": ["**/__tests__/**/*.(js|jsx|ts|tsx)", "**/*.(test|spec).(js|jsx|ts|tsx)"], "collectCoverageFrom": ["src/**/*.{js,jsx}", "!src/**/*.test.{js,jsx}", "!src/tests/**"]}, "private": true, "packageManager": "yarn@4.9.1+sha512.f95ce356460e05be48d66401c1ae64ef84d163dd689964962c6888a9810865e39097a5e9de748876c2e0bf89b232d583c33982773e9903ae7a76257270986538"}