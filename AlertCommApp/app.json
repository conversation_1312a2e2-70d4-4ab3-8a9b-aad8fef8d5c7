{"expo": {"name": "AlertComm Emergency Response", "slug": "alertcomm", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": false, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#1976d2"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.alertcomm.app", "buildNumber": "1", "deploymentTarget": "13.0", "requireFullScreen": false, "userInterfaceStyle": "automatic", "infoPlist": {"NSLocationWhenInUseUsageDescription": "AlertComm needs location access to coordinate emergency response and track responder positions.", "NSLocationAlwaysAndWhenInUseUsageDescription": "AlertComm needs location access to coordinate emergency response and track responder positions.", "NSCameraUsageDescription": "AlertComm needs camera access to capture incident photos and documentation.", "NSMicrophoneUsageDescription": "AlertComm needs microphone access for voice communications during emergencies.", "NSPhotoLibraryUsageDescription": "AlertComm needs photo library access to attach images to incident reports.", "UIBackgroundModes": ["location", "remote-notification", "background-fetch"]}, "associatedDomains": ["applinks:app.alertcomm1.com"], "config": {"usesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#1976d2"}, "edgeToEdgeEnabled": true, "package": "com.alertcomm.app", "versionCode": 1, "compileSdkVersion": 35, "targetSdkVersion": 35, "minSdkVersion": 24, "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "ACCESS_BACKGROUND_LOCATION", "CAMERA", "RECORD_AUDIO", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "VIBRATE", "RECEIVE_BOOT_COMPLETED", "WAKE_LOCK", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "intentFilters": [{"action": "VIEW", "data": [{"scheme": "https", "host": "app.alertcomm1.com"}], "category": ["BROWSABLE", "DEFAULT"]}]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-notifications", "expo-location"], "notification": {"icon": "./assets/icon.png", "color": "#1976d2"}, "extra": {"eas": {"projectId": "1911639d-6fa9-42e6-9c34-a45b7e26d778"}}, "owner": "sajib155"}}