const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Ensure proper entry point
config.resolver.platforms = ['native', 'android', 'ios', 'web'];

// Add support for additional file extensions
config.resolver.assetExts.push(
  // Fonts
  'otf', 'ttf',
  // Images
  'png', 'jpg', 'jpeg', 'gif', 'webp', 'svg',
  // Audio
  'mp3', 'wav', 'm4a', 'aac',
  // Video
  'mp4', 'mov', 'avi', 'mkv',
  // Documents
  'pdf', 'doc', 'docx'
);

// Configure source extensions
config.resolver.sourceExts.push('jsx', 'js', 'ts', 'tsx', 'json');

// Configure transformer
config.transformer.babelTransformerPath = require.resolve('metro-react-native-babel-transformer');

module.exports = config;
