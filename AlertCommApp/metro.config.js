const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Ensure proper entry point
config.resolver.platforms = ['native', 'android', 'ios', 'web'];

// Add support for additional file extensions
config.resolver.assetExts.push(
  // Fonts
  'otf', 'ttf',
  // Images
  'png', 'jpg', 'jpeg', 'gif', 'webp', 'svg',
  // Audio
  'mp3', 'wav', 'm4a', 'aac',
  // Video
  'mp4', 'mov', 'avi', 'mkv',
  // Documents
  'pdf', 'doc', 'docx'
);

// Configure source extensions
config.resolver.sourceExts = ['js', 'jsx', 'ts', 'tsx', 'json'];

// Exclude test files from bundling
config.resolver.blockList = [
  /.*\.test\.js$/,
  /.*\.test\.jsx$/,
  /.*\.test\.ts$/,
  /.*\.test\.tsx$/,
  /.*\.spec\.js$/,
  /.*\.spec\.jsx$/,
  /.*\.spec\.ts$/,
  /.*\.spec\.tsx$/,
  /__tests__\/.*/,
];

// Configure transformer with TypeScript support
config.transformer = {
  ...config.transformer,
  babelTransformerPath: require.resolve('metro-react-native-babel-transformer'),
  unstable_allowRequireContext: true,
};

// Configure serializer
config.serializer = {
  ...config.serializer,
  getModulesRunBeforeMainModule: () => [
    require.resolve('react-native/Libraries/Core/InitializeCore'),
  ],
};

module.exports = config;
