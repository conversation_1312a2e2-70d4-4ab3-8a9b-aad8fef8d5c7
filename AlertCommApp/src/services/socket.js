import io from 'socket.io-client';
import AsyncStorage from '@react-native-async-storage/async-storage';
import config from '../config/config';

class SocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.eventListeners = new Map();
  }

  async connect() {
    try {
      const token = await AsyncStorage.getItem('authToken');
      
      if (!token) {
        console.log('No auth token found, cannot connect to socket');
        return;
      }

      this.socket = io(config.SOCKET_URL, {
        transports: ['websocket'],
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        auth: {
          token,
        },
      });

      this.setupEventListeners();
      
      return new Promise((resolve) => {
        this.socket.on('connect', () => {
          console.log('Socket connected to server');
          this.isConnected = true;
          resolve();
        });
      });
    } catch (error) {
      console.error('Socket connection error:', error);
    }
  }

  setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Socket connected');
      this.isConnected = true;
    });

    this.socket.on('disconnect', () => {
      console.log('Socket disconnected');
      this.isConnected = false;
    });

    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      this.isConnected = false;
    });

    // Handle new events
    this.socket.on(config.SOCKET_EVENTS.NEW_EVENT, (event) => {
      console.log('New event received:', event);
      this.emit('newEvent', event);
    });

    // Handle chat messages
    this.socket.on(config.SOCKET_EVENTS.CHAT_MESSAGE, (message) => {
      console.log('Chat message received:', message);
      this.emit('chatMessage', message);
    });

    // Handle status updates
    this.socket.on(config.SOCKET_EVENTS.STATUS_UPDATE, (update) => {
      console.log('Status update received:', update);
      this.emit('statusUpdate', update);
    });

    // Handle location updates
    this.socket.on(config.SOCKET_EVENTS.LOCATION_UPDATE, (update) => {
      console.log('Location update received:', update);
      this.emit('locationUpdate', update);
    });
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.eventListeners.clear();
    }
  }

  // Join event room
  joinEventRoom(eventId) {
    if (this.socket && this.isConnected) {
      this.socket.emit(config.SOCKET_EVENTS.JOIN_ROOM, `event-${eventId}`);
      console.log(`Joined event room: event-${eventId}`);
    }
  }

  // Join user room
  joinUserRoom(userId) {
    if (this.socket && this.isConnected) {
      this.socket.emit(config.SOCKET_EVENTS.JOIN_ROOM, `user-${userId}`);
      console.log(`Joined user room: user-${userId}`);
    }
  }

  // Send chat message
  sendChatMessage(eventId, userId, username, text) {
    if (this.socket && this.isConnected) {
      const message = {
        eventId,
        userId,
        username,
        text,
        timestamp: new Date().toISOString(),
      };
      
      this.socket.emit(config.SOCKET_EVENTS.CHAT_MESSAGE, message);
      console.log('Chat message sent:', message);
    }
  }

  // Send status update
  sendStatusUpdate(eventId, userId, status) {
    if (this.socket && this.isConnected) {
      const update = {
        eventId,
        userId,
        status,
        timestamp: new Date().toISOString(),
      };
      
      this.socket.emit(config.SOCKET_EVENTS.STATUS_UPDATE, update);
      console.log('Status update sent:', update);
    }
  }

  // Send location update
  sendLocationUpdate(userId, latitude, longitude) {
    if (this.socket && this.isConnected) {
      const update = {
        userId,
        latitude,
        longitude,
        timestamp: new Date().toISOString(),
      };
      
      this.socket.emit(config.SOCKET_EVENTS.LOCATION_UPDATE, update);
      console.log('Location update sent:', update);
    }
  }

  // Event listener management
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  off(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in socket event listener for ${event}:`, error);
        }
      });
    }
  }
}

export default new SocketService();
