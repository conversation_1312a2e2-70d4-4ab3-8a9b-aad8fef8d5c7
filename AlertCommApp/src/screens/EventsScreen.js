import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import { usePermission } from '../components/PermissionGuard';
import { PERMISSIONS } from '../utils/permissions';
import apiService from '../services/api';
import socketService from '../services/socket';
import config from '../config/config';

const EventsScreen = ({ navigation }) => {
  const { user } = useAuth();
  const canCreateEvent = usePermission(PERMISSIONS.CREATE_EVENT);
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState('all'); // all, assigned, open

  useEffect(() => {
    loadEvents();
    setupSocketListeners();
    
    return () => {
      socketService.off('newEvent', handleNewEvent);
    };
  }, [filter]);

  const loadEvents = async () => {
    try {
      const eventsData = await apiService.getEvents();
      let filteredEvents = eventsData;
      
      if (filter === 'assigned') {
        filteredEvents = eventsData.filter(event => 
          event.assigned_ids && event.assigned_ids.includes(user.id)
        );
      } else if (filter === 'open') {
        filteredEvents = eventsData.filter(event => event.status === 'open');
      }
      
      setEvents(filteredEvents.sort((a, b) => new Date(b.created_at) - new Date(a.created_at)));
    } catch (error) {
      console.error('Error loading events:', error);
      Alert.alert('Error', 'Failed to load events');
    } finally {
      setLoading(false);
    }
  };

  const setupSocketListeners = () => {
    socketService.on('newEvent', handleNewEvent);
  };

  const handleNewEvent = (event) => {
    setEvents(prev => [event, ...prev]);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadEvents();
    setRefreshing(false);
  };

  const getUrgencyColor = (urgency) => {
    return config.URGENCY_COLORS[urgency] || config.COLORS.textSecondary;
  };

  const getStatusColor = (status) => {
    const statusColors = {
      open: config.COLORS.warning,
      resolved: config.COLORS.success,
      closed: config.COLORS.disabled,
    };
    return statusColors[status] || config.COLORS.textSecondary;
  };

  const formatTimeAgo = (dateString) => {
    const now = new Date();
    const eventTime = new Date(dateString);
    const diffInMinutes = Math.floor((now - eventTime) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const renderFilterButtons = () => (
    <View style={styles.filterContainer}>
      {[
        { key: 'all', label: 'All Events' },
        { key: 'assigned', label: 'Assigned to Me' },
        { key: 'open', label: 'Open Events' },
      ].map((filterOption) => (
        <TouchableOpacity
          key={filterOption.key}
          style={[
            styles.filterButton,
            filter === filterOption.key && styles.activeFilterButton
          ]}
          onPress={() => setFilter(filterOption.key)}
        >
          <Text style={[
            styles.filterButtonText,
            filter === filterOption.key && styles.activeFilterButtonText
          ]}>
            {filterOption.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderEventItem = ({ item: event }) => {
    const isAssigned = event.assigned_ids && event.assigned_ids.includes(user.id);
    
    return (
      <TouchableOpacity
        style={[styles.eventCard, isAssigned && styles.assignedEventCard]}
        onPress={() => navigation.navigate('EventDetail', { eventId: event.id })}
      >
        <View style={styles.eventHeader}>
          <View style={styles.eventTitleContainer}>
            <Text style={styles.eventTitle} numberOfLines={1}>
              {event.title}
            </Text>
            {isAssigned && (
              <Ionicons 
                name="person-circle" 
                size={16} 
                color={config.COLORS.primary}
                style={styles.assignedIcon}
              />
            )}
          </View>
          <View style={styles.eventBadges}>
            <View style={[styles.urgencyBadge, { backgroundColor: getUrgencyColor(event.urgency) }]}>
              <Text style={styles.badgeText}>{event.urgency}</Text>
            </View>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(event.status) }]}>
              <Text style={styles.badgeText}>{event.status}</Text>
            </View>
          </View>
        </View>

        <Text style={styles.eventInfo} numberOfLines={2}>
          {event.info || event.description || 'No additional information'}
        </Text>

        {event.location && (
          <View style={styles.locationContainer}>
            <Ionicons name="location-outline" size={14} color={config.COLORS.textSecondary} />
            <Text style={styles.locationText} numberOfLines={1}>
              {event.location.commonName || event.location.address || 'Location not specified'}
            </Text>
          </View>
        )}

        <View style={styles.eventFooter}>
          <Text style={styles.eventTime}>
            {formatTimeAgo(event.created_at)}
          </Text>
          <Text style={styles.eventModule}>
            {event.module || 'General'}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="alert-circle-outline" size={64} color={config.COLORS.disabled} />
      <Text style={styles.emptyTitle}>No Events Found</Text>
      <Text style={styles.emptyText}>
        {filter === 'assigned' 
          ? 'You have no assigned events at the moment.'
          : filter === 'open'
          ? 'There are no open events currently.'
          : 'No events available.'
        }
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {renderFilterButtons()}

      <FlatList
        data={events}
        renderItem={renderEventItem}
        keyExtractor={(item) => item.id.toString()}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={events.length === 0 ? styles.emptyListContainer : styles.listContainer}
        ListEmptyComponent={!loading ? renderEmptyState : null}
        showsVerticalScrollIndicator={false}
      />

      {/* Create Event Floating Action Button */}
      {canCreateEvent && (
        <TouchableOpacity
          style={styles.createEventButton}
          onPress={() => navigation.navigate('CreateEvent')}
        >
          <Ionicons name="add" size={28} color="#fff" />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: config.COLORS.background,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: config.COLORS.surface,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
  },
  filterButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginHorizontal: 4,
    borderRadius: 20,
    backgroundColor: config.COLORS.background,
    alignItems: 'center',
  },
  activeFilterButton: {
    backgroundColor: config.COLORS.primary,
  },
  filterButtonText: {
    fontSize: 12,
    color: config.COLORS.text,
    fontWeight: '500',
  },
  activeFilterButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  listContainer: {
    padding: 16,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  eventCard: {
    backgroundColor: config.COLORS.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  assignedEventCard: {
    borderLeftWidth: 4,
    borderLeftColor: config.COLORS.primary,
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  eventTitleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: config.COLORS.text,
    flex: 1,
  },
  assignedIcon: {
    marginLeft: 8,
  },
  eventBadges: {
    flexDirection: 'row',
    gap: 8,
  },
  urgencyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  badgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  eventInfo: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    marginBottom: 8,
    lineHeight: 20,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  locationText: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
    marginLeft: 4,
    flex: 1,
  },
  eventFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  eventTime: {
    fontSize: 12,
    color: config.COLORS.disabled,
  },
  eventModule: {
    fontSize: 12,
    color: config.COLORS.primary,
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: config.COLORS.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    textAlign: 'center',
    paddingHorizontal: 32,
  },
  createEventButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: config.COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
});

export default EventsScreen;
