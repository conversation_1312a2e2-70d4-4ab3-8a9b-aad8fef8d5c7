import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import apiService from '../services/api';
import socketService from '../services/socket';
import config from '../config/config';

const ChatScreen = ({ route }) => {
  const { eventId } = route.params || {};
  const { user } = useAuth();
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [activeEventId, setActiveEventId] = useState(eventId);
  const flatListRef = useRef(null);

  useEffect(() => {
    // If no eventId provided, get the most recent active event
    if (!eventId) {
      loadActiveEvent();
    } else {
      setActiveEventId(eventId);
    }
  }, [eventId]);

  useEffect(() => {
    if (activeEventId) {
      loadChatMessages();
      setupSocketListeners();
      socketService.joinEventRoom(activeEventId);
    }

    return () => {
      socketService.off('chatMessage', handleNewMessage);
    };
  }, [activeEventId]);

  const loadActiveEvent = async () => {
    try {
      const events = await apiService.getEvents();
      if (events && events.length > 0) {
        // Get the most recent active event
        const activeEvent = events.find(event => event.status === 'active') || events[0];
        setActiveEventId(activeEvent.id);
      }
    } catch (error) {
      console.error('Error loading active event:', error);
      setLoading(false);
    }
  };

  const loadChatMessages = async () => {
    if (!activeEventId) return;

    try {
      const messagesData = await apiService.getChatMessages(activeEventId);
      setMessages(messagesData.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp)));
    } catch (error) {
      console.error('Error loading chat messages:', error);
    } finally {
      setLoading(false);
    }
  };

  const setupSocketListeners = () => {
    socketService.on('chatMessage', handleNewMessage);
  };

  const handleNewMessage = (message) => {
    if (message.eventId === eventId) {
      setMessages(prev => [...prev, message]);
      // Auto-scroll to bottom
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || !activeEventId) return;

    const messageText = newMessage.trim();
    setNewMessage('');

    try {
      // Send via API
      await apiService.sendChatMessage(activeEventId, messageText);

      // Send via socket for real-time update
      socketService.sendChatMessage(activeEventId, user.id, user.username, messageText);

    } catch (error) {
      console.error('Error sending message:', error);
      // Re-add message to input if failed
      setNewMessage(messageText);

      // Show user-friendly error message
      Alert.alert(
        'Message Failed',
        'Failed to send message. Please check your connection and try again.',
        [
          { text: 'OK' },
          {
            text: 'Retry',
            onPress: () => {
              // Retry sending the message
              sendMessage();
            }
          }
        ]
      );
    }
  };

  const formatMessageTime = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderMessage = ({ item: message }) => {
    const isMyMessage = message.userId === user.id;
    
    return (
      <View style={[
        styles.messageContainer,
        isMyMessage ? styles.myMessageContainer : styles.otherMessageContainer
      ]}>
        <View style={[
          styles.messageBubble,
          isMyMessage ? styles.myMessageBubble : styles.otherMessageBubble
        ]}>
          {!isMyMessage && (
            <Text style={styles.senderName}>{message.username}</Text>
          )}
          <Text style={[
            styles.messageText,
            isMyMessage ? styles.myMessageText : styles.otherMessageText
          ]}>
            {message.text}
          </Text>
          <Text style={[
            styles.messageTime,
            isMyMessage ? styles.myMessageTime : styles.otherMessageTime
          ]}>
            {formatMessageTime(message.timestamp)}
          </Text>
        </View>
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="chatbubbles-outline" size={64} color={config.COLORS.disabled} />
      <Text style={styles.emptyTitle}>No Messages Yet</Text>
      <Text style={styles.emptyText}>
        Start the conversation by sending a message below.
      </Text>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading chat...</Text>
      </View>
    );
  }

  if (!activeEventId) {
    return (
      <View style={styles.noEventContainer}>
        <Ionicons name="alert-circle-outline" size={64} color={config.COLORS.disabled} />
        <Text style={styles.noEventTitle}>No Active Events</Text>
        <Text style={styles.noEventText}>
          No active events available for chat.
        </Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item, index) => `${item.id || index}`}
        contentContainerStyle={messages.length === 0 ? styles.emptyListContainer : styles.listContainer}
        ListEmptyComponent={!loading ? renderEmptyState : null}
        onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: false })}
        showsVerticalScrollIndicator={false}
      />
      
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.textInput}
          value={newMessage}
          onChangeText={setNewMessage}
          placeholder="Type a message..."
          placeholderTextColor={config.COLORS.textSecondary}
          multiline
          maxLength={500}
        />
        <TouchableOpacity
          style={[
            styles.sendButton,
            newMessage.trim() ? styles.sendButtonActive : styles.sendButtonInactive
          ]}
          onPress={sendMessage}
          disabled={!newMessage.trim()}
        >
          <Ionicons 
            name="send" 
            size={20} 
            color={newMessage.trim() ? '#fff' : config.COLORS.disabled}
          />
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: config.COLORS.background,
  },
  listContainer: {
    padding: 16,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  messageContainer: {
    marginBottom: 12,
  },
  myMessageContainer: {
    alignItems: 'flex-end',
  },
  otherMessageContainer: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 16,
  },
  myMessageBubble: {
    backgroundColor: config.COLORS.primary,
    borderBottomRightRadius: 4,
  },
  otherMessageBubble: {
    backgroundColor: config.COLORS.surface,
    borderBottomLeftRadius: 4,
    borderWidth: 1,
    borderColor: config.COLORS.border,
  },
  senderName: {
    fontSize: 12,
    fontWeight: 'bold',
    color: config.COLORS.primary,
    marginBottom: 4,
  },
  messageText: {
    fontSize: 14,
    lineHeight: 20,
  },
  myMessageText: {
    color: '#fff',
  },
  otherMessageText: {
    color: config.COLORS.text,
  },
  messageTime: {
    fontSize: 10,
    marginTop: 4,
  },
  myMessageTime: {
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'right',
  },
  otherMessageTime: {
    color: config.COLORS.textSecondary,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    padding: 16,
    backgroundColor: config.COLORS.surface,
    borderTopWidth: 1,
    borderTopColor: config.COLORS.border,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: config.COLORS.border,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 12,
    maxHeight: 100,
    fontSize: 14,
    color: config.COLORS.text,
    backgroundColor: config.COLORS.background,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonActive: {
    backgroundColor: config.COLORS.primary,
  },
  sendButtonInactive: {
    backgroundColor: config.COLORS.border,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: config.COLORS.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    textAlign: 'center',
    paddingHorizontal: 32,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: config.COLORS.background,
  },
  loadingText: {
    fontSize: 16,
    color: config.COLORS.textSecondary,
  },
  noEventContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: config.COLORS.background,
  },
  noEventTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: config.COLORS.text,
    marginTop: 16,
    marginBottom: 8,
  },
  noEventText: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    textAlign: 'center',
    paddingHorizontal: 32,
  },
});

export default ChatScreen;
