import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Picker } from '@react-native-picker/picker';
import { useAuth } from '../context/AuthContext';
import { useNotifications } from '../context/NotificationContext';
import apiService from '../services/api';
import config from '../config/config';

const CreateEventScreen = ({ navigation }) => {
  const { user } = useAuth();
  const { showLocalNotification } = useNotifications();
  const [loading, setLoading] = useState(false);
  const [availableResponders, setAvailableResponders] = useState([]);
  
  // Event form data
  const [eventData, setEventData] = useState({
    title: '',
    description: '',
    priority: 'medium',
    urgency: 'medium',
    location: '',
    latitude: '',
    longitude: '',
    status: 'active',
    info: '',
  });

  // Selected responders
  const [selectedResponders, setSelectedResponders] = useState([]);

  useEffect(() => {
    loadAvailableResponders();
  }, []);

  const loadAvailableResponders = async () => {
    try {
      const users = await apiService.getUsers();
      // Filter for staff, EMT, medics, etc. (not commanders or dispatchers)
      const responders = users.filter(user => 
        ['staff', 'supervisor'].includes(user.role) && user.status === 1
      );
      setAvailableResponders(responders);
    } catch (error) {
      console.error('Error loading responders:', error);
      Alert.alert('Error', 'Failed to load available responders');
    }
  };

  const handleResponderToggle = (responder) => {
    setSelectedResponders(prev => {
      const isSelected = prev.find(r => r.id === responder.id);
      if (isSelected) {
        return prev.filter(r => r.id !== responder.id);
      } else {
        return [...prev, responder];
      }
    });
  };

  const validateForm = () => {
    if (!eventData.title.trim()) {
      Alert.alert('Validation Error', 'Event title is required');
      return false;
    }
    if (!eventData.description.trim()) {
      Alert.alert('Validation Error', 'Event description is required');
      return false;
    }
    if (!eventData.location.trim()) {
      Alert.alert('Validation Error', 'Event location is required');
      return false;
    }
    if (selectedResponders.length === 0) {
      Alert.alert('Validation Error', 'Please select at least one responder');
      return false;
    }
    return true;
  };

  const createEvent = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      // Create the event
      const newEvent = await apiService.createEvent({
        ...eventData,
        created_by: user.id,
        company_id: user.company_id || 1,
      });

      // Assign responders to the event
      const tasks = selectedResponders.map(responder => ({
        event_id: newEvent.id,
        assigned_to: responder.id,
        title: `Respond to: ${eventData.title}`,
        description: `You have been assigned to respond to this ${eventData.priority} priority event.`,
        status: 'pending',
        priority: eventData.priority,
        due_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
      }));

      // Create tasks for each responder
      for (const task of tasks) {
        await apiService.createTask(task);
      }

      // Send notifications to responders
      for (const responder of selectedResponders) {
        try {
          await apiService.createNotification({
            responder_id: responder.id,
            event_id: newEvent.id,
            title: `🚨 New Emergency Assignment`,
            message: `You've been assigned to: ${eventData.title}`,
            type: 'event_assignment',
            priority: eventData.priority,
            is_read: false,
          });
        } catch (notifError) {
          console.error('Error creating notification for responder:', responder.id, notifError);
        }
      }

      // Show success notification
      showLocalNotification(
        'Event Created Successfully',
        `${eventData.title} has been created and responders have been notified.`
      );

      Alert.alert(
        'Success',
        `Event "${eventData.title}" has been created successfully!\n\n${selectedResponders.length} responders have been notified.`,
        [
          {
            text: 'View Event',
            onPress: () => {
              navigation.replace('EventDetail', { eventId: newEvent.id });
            }
          },
          {
            text: 'Create Another',
            onPress: () => {
              // Reset form
              setEventData({
                title: '',
                description: '',
                priority: 'medium',
                urgency: 'medium',
                location: '',
                latitude: '',
                longitude: '',
                status: 'active',
                info: '',
              });
              setSelectedResponders([]);
            }
          }
        ]
      );

    } catch (error) {
      console.error('Error creating event:', error);
      Alert.alert('Error', 'Failed to create event. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderResponderItem = (responder) => {
    const isSelected = selectedResponders.find(r => r.id === responder.id);
    
    return (
      <TouchableOpacity
        key={responder.id}
        style={[styles.responderItem, isSelected && styles.responderItemSelected]}
        onPress={() => handleResponderToggle(responder)}
      >
        <View style={styles.responderInfo}>
          <Text style={styles.responderName}>
            {responder.first_name} {responder.last_name}
          </Text>
          <Text style={styles.responderRole}>
            {responder.job_role} • {responder.main_location}
          </Text>
        </View>
        <View style={[styles.checkbox, isSelected && styles.checkboxSelected]}>
          {isSelected && <Ionicons name="checkmark" size={16} color="#fff" />}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.scrollContent}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={config.COLORS.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create New Event</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Event Details Form */}
      <View style={styles.formCard}>
        <Text style={styles.sectionTitle}>Event Details</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Event Title *</Text>
          <TextInput
            style={styles.input}
            value={eventData.title}
            onChangeText={(text) => setEventData(prev => ({ ...prev, title: text }))}
            placeholder="Enter event title"
            placeholderTextColor={config.COLORS.textSecondary}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Description *</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={eventData.description}
            onChangeText={(text) => setEventData(prev => ({ ...prev, description: text }))}
            placeholder="Describe the emergency situation"
            placeholderTextColor={config.COLORS.textSecondary}
            multiline
            numberOfLines={3}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Location *</Text>
          <TextInput
            style={styles.input}
            value={eventData.location}
            onChangeText={(text) => setEventData(prev => ({ ...prev, location: text }))}
            placeholder="Event location or address"
            placeholderTextColor={config.COLORS.textSecondary}
          />
        </View>

        <View style={styles.row}>
          <View style={[styles.inputGroup, styles.halfWidth]}>
            <Text style={styles.label}>Priority</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={eventData.priority}
                onValueChange={(value) => setEventData(prev => ({ ...prev, priority: value }))}
                style={styles.picker}
              >
                <Picker.Item label="Low" value="low" />
                <Picker.Item label="Medium" value="medium" />
                <Picker.Item label="High" value="high" />
                <Picker.Item label="Immediate" value="immediate" />
              </Picker>
            </View>
          </View>

          <View style={[styles.inputGroup, styles.halfWidth]}>
            <Text style={styles.label}>Urgency</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={eventData.urgency}
                onValueChange={(value) => setEventData(prev => ({ ...prev, urgency: value }))}
                style={styles.picker}
              >
                <Picker.Item label="Low" value="low" />
                <Picker.Item label="Medium" value="medium" />
                <Picker.Item label="High" value="high" />
                <Picker.Item label="Immediate" value="immediate" />
              </Picker>
            </View>
          </View>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Additional Information</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={eventData.info}
            onChangeText={(text) => setEventData(prev => ({ ...prev, info: text }))}
            placeholder="Any additional details or instructions"
            placeholderTextColor={config.COLORS.textSecondary}
            multiline
            numberOfLines={2}
          />
        </View>
      </View>

      {/* Responder Selection */}
      <View style={styles.formCard}>
        <Text style={styles.sectionTitle}>
          Assign Responders ({selectedResponders.length} selected)
        </Text>
        
        {availableResponders.length === 0 ? (
          <Text style={styles.noRespondersText}>No available responders found</Text>
        ) : (
          availableResponders.map(renderResponderItem)
        )}
      </View>

      {/* Create Button */}
      <TouchableOpacity
        style={[styles.createButton, loading && styles.createButtonDisabled]}
        onPress={createEvent}
        disabled={loading}
      >
        <Ionicons name="add-circle" size={24} color="#fff" />
        <Text style={styles.createButtonText}>
          {loading ? 'Creating Event...' : 'Create Event & Notify Responders'}
        </Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: config.COLORS.background,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
    backgroundColor: config.COLORS.surface,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: config.COLORS.text,
  },
  placeholder: {
    width: 32,
  },
  formCard: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: config.COLORS.text,
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: config.COLORS.text,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: config.COLORS.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: config.COLORS.text,
    backgroundColor: config.COLORS.background,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfWidth: {
    width: '48%',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: config.COLORS.border,
    borderRadius: 8,
    backgroundColor: config.COLORS.background,
  },
  picker: {
    height: 50,
    color: config.COLORS.text,
  },
  responderItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: config.COLORS.background,
    borderWidth: 1,
    borderColor: config.COLORS.border,
  },
  responderItemSelected: {
    backgroundColor: config.COLORS.primary + '20',
    borderColor: config.COLORS.primary,
  },
  responderInfo: {
    flex: 1,
  },
  responderName: {
    fontSize: 16,
    fontWeight: '500',
    color: config.COLORS.text,
    marginBottom: 2,
  },
  responderRole: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: config.COLORS.border,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    backgroundColor: config.COLORS.primary,
    borderColor: config.COLORS.primary,
  },
  noRespondersText: {
    fontSize: 16,
    color: config.COLORS.textSecondary,
    textAlign: 'center',
    padding: 20,
    fontStyle: 'italic',
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: config.COLORS.primary,
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  createButtonDisabled: {
    backgroundColor: config.COLORS.disabled,
  },
  createButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default CreateEventScreen;
