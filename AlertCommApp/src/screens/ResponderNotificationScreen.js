import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import { useNotifications } from '../context/NotificationContext';
import apiService from '../services/api';
import config from '../config/config';

const ResponderNotificationScreen = ({ navigation }) => {
  const { user } = useAuth();
  const { notifications, markAsRead } = useNotifications();
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [eventNotifications, setEventNotifications] = useState([]);

  useEffect(() => {
    loadNotifications();
  }, []);

  const loadNotifications = async () => {
    setLoading(true);
    try {
      const notificationsData = await apiService.getNotifications();
      // Filter for event assignment notifications
      const eventAssignments = notificationsData.filter(n => n.type === 'event_assignment');
      setEventNotifications(eventAssignments);
    } catch (error) {
      console.error('Error loading notifications:', error);
      Alert.alert('Error', 'Failed to load notifications');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadNotifications();
    setRefreshing(false);
  };

  const handleNotificationResponse = async (notification, response) => {
    try {
      // Mark notification as read
      await apiService.markNotificationRead(notification.id);
      markAsRead(notification.id);

      // Update task status based on response
      let newStatus = 'pending';
      let message = '';

      switch (response) {
        case 'accept':
          newStatus = 'acknowledged';
          message = 'Assignment accepted. You can now update your status.';
          break;
        case 'decline':
          newStatus = 'declined';
          message = 'Assignment declined. Command has been notified.';
          break;
        case 'enroute':
          newStatus = 'enroute';
          message = 'Status updated to En Route.';
          break;
        case 'on_scene':
          newStatus = 'on_scene';
          message = 'Status updated to On Scene.';
          break;
      }

      // Update task status if there's an associated task
      if (notification.event_id) {
        // Find the user's task for this event
        const eventTasks = await apiService.getEventTasks(notification.event_id);
        const userTask = eventTasks.find(task => task.assigned_to === user.id);
        
        if (userTask) {
          await apiService.updateTaskStatus(userTask.id, newStatus);
        }
      }

      Alert.alert('Response Recorded', message, [
        {
          text: 'View Event',
          onPress: () => {
            if (notification.event_id) {
              navigation.navigate('EventDetail', { eventId: notification.event_id });
            }
          }
        },
        { text: 'OK' }
      ]);

      // Refresh notifications
      loadNotifications();

    } catch (error) {
      console.error('Error responding to notification:', error);
      Alert.alert('Error', 'Failed to record response. Please try again.');
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority?.toLowerCase()) {
      case 'immediate':
        return '#d32f2f';
      case 'high':
        return '#f44336';
      case 'medium':
        return '#ff9800';
      case 'low':
        return '#4caf50';
      default:
        return config.COLORS.primary;
    }
  };

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now - time) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const renderNotification = (notification) => {
    const isRead = notification.is_read;
    const priorityColor = getPriorityColor(notification.priority);

    return (
      <View key={notification.id} style={[styles.notificationCard, !isRead && styles.unreadCard]}>
        {/* Priority Indicator */}
        <View style={[styles.priorityIndicator, { backgroundColor: priorityColor }]} />
        
        {/* Header */}
        <View style={styles.notificationHeader}>
          <View style={styles.headerLeft}>
            <Ionicons name="alert-circle" size={24} color={priorityColor} />
            <View style={styles.headerText}>
              <Text style={[styles.notificationTitle, !isRead && styles.unreadText]}>
                {notification.title}
              </Text>
              <Text style={styles.notificationTime}>
                {formatTimeAgo(notification.created_at)}
              </Text>
            </View>
          </View>
          {!isRead && <View style={styles.unreadDot} />}
        </View>

        {/* Message */}
        <Text style={styles.notificationMessage}>
          {notification.message}
        </Text>

        {/* Event Info */}
        {notification.event_title && (
          <View style={styles.eventInfo}>
            <Ionicons name="calendar" size={16} color={config.COLORS.textSecondary} />
            <Text style={styles.eventTitle}>{notification.event_title}</Text>
          </View>
        )}

        {/* Action Buttons */}
        {!isRead && (
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.actionButton, styles.acceptButton]}
              onPress={() => handleNotificationResponse(notification, 'accept')}
            >
              <Ionicons name="checkmark" size={20} color="#fff" />
              <Text style={styles.actionButtonText}>Accept</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, styles.declineButton]}
              onPress={() => handleNotificationResponse(notification, 'decline')}
            >
              <Ionicons name="close" size={20} color="#fff" />
              <Text style={styles.actionButtonText}>Decline</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, styles.enrouteButton]}
              onPress={() => handleNotificationResponse(notification, 'enroute')}
            >
              <Ionicons name="car" size={20} color="#fff" />
              <Text style={styles.actionButtonText}>En Route</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* View Event Button */}
        {notification.event_id && (
          <TouchableOpacity
            style={styles.viewEventButton}
            onPress={() => {
              navigation.navigate('EventDetail', { eventId: notification.event_id });
            }}
          >
            <Text style={styles.viewEventText}>View Event Details</Text>
            <Ionicons name="chevron-forward" size={16} color={config.COLORS.primary} />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="notifications-outline" size={64} color={config.COLORS.disabled} />
      <Text style={styles.emptyTitle}>No Event Assignments</Text>
      <Text style={styles.emptyText}>
        You don't have any pending event assignments. New assignments will appear here.
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={config.COLORS.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Event Assignments</Text>
        <TouchableOpacity onPress={handleRefresh} style={styles.refreshButton}>
          <Ionicons name="refresh" size={24} color={config.COLORS.text} />
        </TouchableOpacity>
      </View>

      {/* User Info */}
      <View style={styles.userInfo}>
        <View style={styles.userAvatar}>
          <Ionicons name="person" size={24} color="#fff" />
        </View>
        <View style={styles.userDetails}>
          <Text style={styles.userName}>
            {user?.first_name} {user?.last_name}
          </Text>
          <Text style={styles.userRole}>
            {user?.job_role} • {user?.main_location}
          </Text>
        </View>
        <View style={[styles.statusIndicator, { backgroundColor: '#4caf50' }]} />
      </View>

      {/* Notifications List */}
      <ScrollView
        style={styles.notificationsList}
        contentContainerStyle={eventNotifications.length === 0 ? styles.emptyListContainer : null}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[config.COLORS.primary]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {eventNotifications.length === 0 ? (
          renderEmptyState()
        ) : (
          eventNotifications.map(renderNotification)
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: config.COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
    backgroundColor: config.COLORS.surface,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: config.COLORS.text,
  },
  refreshButton: {
    padding: 4,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: config.COLORS.surface,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
  },
  userAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: config.COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: config.COLORS.text,
    marginBottom: 2,
  },
  userRole: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  notificationsList: {
    flex: 1,
  },
  emptyListContainer: {
    flex: 1,
  },
  notificationCard: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    marginBottom: 8,
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    position: 'relative',
  },
  unreadCard: {
    backgroundColor: config.COLORS.primary + '10',
    borderLeftWidth: 4,
    borderLeftColor: config.COLORS.primary,
  },
  priorityIndicator: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 4,
    height: '100%',
    borderTopRightRadius: 12,
    borderBottomRightRadius: 12,
  },
  notificationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerText: {
    marginLeft: 12,
    flex: 1,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: config.COLORS.text,
    marginBottom: 2,
  },
  unreadText: {
    fontWeight: '600',
  },
  notificationTime: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: config.COLORS.primary,
  },
  notificationMessage: {
    fontSize: 14,
    color: config.COLORS.text,
    lineHeight: 20,
    marginBottom: 12,
  },
  eventInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    padding: 8,
    backgroundColor: config.COLORS.background,
    borderRadius: 6,
  },
  eventTitle: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    marginLeft: 6,
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    padding: 10,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  acceptButton: {
    backgroundColor: '#4caf50',
  },
  declineButton: {
    backgroundColor: '#f44336',
  },
  enrouteButton: {
    backgroundColor: '#ff9800',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  viewEventButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    backgroundColor: config.COLORS.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: config.COLORS.primary,
  },
  viewEventText: {
    color: config.COLORS.primary,
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: config.COLORS.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: config.COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
});

export default ResponderNotificationScreen;
