import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import config from '../config/config';

const PrivacyPolicyScreen = ({ navigation }) => {
  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={config.COLORS.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Privacy Policy</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.scrollContent}>
        <View style={styles.section}>
          <Text style={styles.lastUpdated}>Last updated: January 2024</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>1. Information We Collect</Text>
          <Text style={styles.paragraph}>
            AlertComm collects information necessary to provide emergency response coordination services:
          </Text>
          <Text style={styles.bulletPoint}>• Personal identification information (name, email, phone number)</Text>
          <Text style={styles.bulletPoint}>• Location data when using emergency response features</Text>
          <Text style={styles.bulletPoint}>• Communication logs for coordination purposes</Text>
          <Text style={styles.bulletPoint}>• Device information for app functionality</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>2. How We Use Your Information</Text>
          <Text style={styles.paragraph}>
            Your information is used exclusively for emergency response coordination:
          </Text>
          <Text style={styles.bulletPoint}>• Coordinating emergency response activities</Text>
          <Text style={styles.bulletPoint}>• Sending critical notifications and alerts</Text>
          <Text style={styles.bulletPoint}>• Tracking responder locations during emergencies</Text>
          <Text style={styles.bulletPoint}>• Facilitating communication between team members</Text>
          <Text style={styles.bulletPoint}>• Improving emergency response procedures</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>3. Information Sharing</Text>
          <Text style={styles.paragraph}>
            We do not sell, trade, or rent your personal information. Information may be shared only:
          </Text>
          <Text style={styles.bulletPoint}>• With authorized emergency response team members</Text>
          <Text style={styles.bulletPoint}>• With emergency services when required by law</Text>
          <Text style={styles.bulletPoint}>• To protect safety during emergency situations</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>4. Data Security</Text>
          <Text style={styles.paragraph}>
            We implement industry-standard security measures to protect your information:
          </Text>
          <Text style={styles.bulletPoint}>• Encrypted data transmission and storage</Text>
          <Text style={styles.bulletPoint}>• Secure authentication systems</Text>
          <Text style={styles.bulletPoint}>• Regular security audits and updates</Text>
          <Text style={styles.bulletPoint}>• Limited access to authorized personnel only</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>5. Location Data</Text>
          <Text style={styles.paragraph}>
            Location information is critical for emergency response:
          </Text>
          <Text style={styles.bulletPoint}>• Used only during active emergency responses</Text>
          <Text style={styles.bulletPoint}>• Shared with team members for coordination</Text>
          <Text style={styles.bulletPoint}>• Automatically deleted after incident resolution</Text>
          <Text style={styles.bulletPoint}>• Can be disabled when not on active duty</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>6. Data Retention</Text>
          <Text style={styles.paragraph}>
            We retain information only as long as necessary:
          </Text>
          <Text style={styles.bulletPoint}>• Active user data while employed/volunteering</Text>
          <Text style={styles.bulletPoint}>• Emergency logs for regulatory compliance</Text>
          <Text style={styles.bulletPoint}>• Communication records for training purposes</Text>
          <Text style={styles.bulletPoint}>• Data deletion upon request when legally permissible</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>7. Your Rights</Text>
          <Text style={styles.paragraph}>
            You have the right to:
          </Text>
          <Text style={styles.bulletPoint}>• Access your personal information</Text>
          <Text style={styles.bulletPoint}>• Request corrections to your data</Text>
          <Text style={styles.bulletPoint}>• Opt-out of non-essential communications</Text>
          <Text style={styles.bulletPoint}>• Request data deletion (subject to legal requirements)</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>8. Contact Information</Text>
          <Text style={styles.paragraph}>
            For privacy-related questions or concerns, contact:
          </Text>
          <Text style={styles.contactInfo}>
            Email: <EMAIL>{'\n'}
            Phone: 1-800-ALERT-01{'\n'}
            Address: 123 Emergency Way, Safety City, SC 12345
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>9. Changes to This Policy</Text>
          <Text style={styles.paragraph}>
            We may update this privacy policy to reflect changes in our practices or legal requirements. 
            Users will be notified of significant changes through the app or email.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>10. Emergency Situations</Text>
          <Text style={styles.paragraph}>
            During emergency situations, normal privacy restrictions may be temporarily modified to:
          </Text>
          <Text style={styles.bulletPoint}>• Ensure responder and public safety</Text>
          <Text style={styles.bulletPoint}>• Coordinate with emergency services</Text>
          <Text style={styles.bulletPoint}>• Comply with emergency protocols</Text>
          <Text style={styles.bulletPoint}>• Share critical information with authorized personnel</Text>
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            By using AlertComm, you acknowledge that you have read and understood this Privacy Policy 
            and agree to the collection and use of your information as described herein.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: config.COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
    backgroundColor: config.COLORS.surface,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: config.COLORS.text,
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  section: {
    marginBottom: 24,
  },
  lastUpdated: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    fontStyle: 'italic',
    textAlign: 'center',
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: config.COLORS.text,
    marginBottom: 12,
  },
  paragraph: {
    fontSize: 16,
    color: config.COLORS.text,
    lineHeight: 24,
    marginBottom: 8,
  },
  bulletPoint: {
    fontSize: 16,
    color: config.COLORS.text,
    lineHeight: 24,
    marginBottom: 4,
    marginLeft: 8,
  },
  contactInfo: {
    fontSize: 16,
    color: config.COLORS.text,
    lineHeight: 24,
    backgroundColor: config.COLORS.surface,
    padding: 12,
    borderRadius: 8,
    fontFamily: 'monospace',
  },
  footer: {
    backgroundColor: config.COLORS.surface,
    padding: 16,
    borderRadius: 8,
    marginTop: 16,
  },
  footerText: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    lineHeight: 20,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default PrivacyPolicyScreen;
