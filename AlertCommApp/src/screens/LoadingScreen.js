import React from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import config from '../config/config';

const LoadingScreen = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>AlertComm</Text>
      <Text style={styles.subtitle}>Emergency Response System</Text>
      <ActivityIndicator 
        size="large" 
        color={config.COLORS.primary} 
        style={styles.loader}
      />
      <Text style={styles.loadingText}>Loading...</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: config.COLORS.background,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: config.COLORS.primary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: config.COLORS.textSecondary,
    marginBottom: 40,
  },
  loader: {
    marginBottom: 20,
  },
  loadingText: {
    fontSize: 16,
    color: config.COLORS.textSecondary,
  },
});

export default LoadingScreen;
