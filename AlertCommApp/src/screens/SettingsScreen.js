import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Switch,
  Alert,
  TextInput,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import { usePermission } from '../components/PermissionGuard';
import { PERMISSIONS } from '../utils/permissions';
import apiService from '../services/api';
import config from '../config/config';

const SettingsScreen = ({ navigation }) => {
  const { user } = useAuth();
  const canManageSystem = usePermission(PERMISSIONS.SYSTEM_SETTINGS);
  const canManageUsers = usePermission(PERMISSIONS.MANAGE_USERS);
  
  const [settings, setSettings] = useState({
    // System Settings
    autoAssignments: true,
    realTimeTracking: true,
    emergencyAlerts: true,
    dataRetention: 30, // days
    
    // Notification Settings
    pushNotifications: true,
    emailNotifications: true,
    smsNotifications: false,
    
    // Security Settings
    sessionTimeout: 60, // minutes
    requireStrongPasswords: true,
    twoFactorAuth: false,
    
    // Communication Settings
    allowGlobalChat: true,
    moderateChat: false,
    fileUploads: true,
    maxFileSize: 10, // MB
  });

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      // Load from AsyncStorage
      const savedSettings = await AsyncStorage.getItem('alertcomm_settings');
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings(parsedSettings);
        console.log('Settings loaded from storage:', parsedSettings);
      } else {
        console.log('No saved settings found, using defaults');
      }

      // In a real app, this would also load from API
      // const apiSettings = await apiService.getSystemSettings();
      // setSettings(apiSettings);
    } catch (error) {
      console.error('Error loading settings:', error);
      Alert.alert('Error', 'Failed to load settings: ' + error.message);
    }
  };

  const updateSetting = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const saveSettings = async () => {
    setLoading(true);
    try {
      // Save to AsyncStorage for persistence
      await AsyncStorage.setItem('alertcomm_settings', JSON.stringify(settings));

      // In a real app, this would also save to API
      // await apiService.updateSystemSettings(settings);

      Alert.alert('Success', 'Settings saved successfully', [
        {
          text: 'OK',
          onPress: () => {
            // Show what was actually saved
            console.log('Settings saved:', settings);
          }
        }
      ]);
    } catch (error) {
      console.error('Error saving settings:', error);
      Alert.alert('Error', 'Failed to save settings: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const resetToDefaults = () => {
    Alert.alert(
      'Reset Settings',
      'Are you sure you want to reset all settings to default values?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: () => {
            setSettings({
              autoAssignments: true,
              realTimeTracking: true,
              emergencyAlerts: true,
              dataRetention: 30,
              pushNotifications: true,
              emailNotifications: true,
              smsNotifications: false,
              sessionTimeout: 60,
              requireStrongPasswords: true,
              twoFactorAuth: false,
              allowGlobalChat: true,
              moderateChat: false,
              fileUploads: true,
              maxFileSize: 10,
            });
          }
        }
      ]
    );
  };

  const showUserList = async () => {
    try {
      const users = await apiService.getUsers();
      const userList = users.map(u => `${u.first_name} ${u.last_name} (${u.role})`).join('\n');
      Alert.alert(
        'Current Users',
        userList || 'No users found',
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to load users: ' + error.message);
    }
  };

  const exportData = (type) => {
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `alertcomm_${type}_${timestamp}`;

    Alert.alert(
      'Export Started',
      `Exporting ${type} data...\n\nFile: ${filename}.csv\nThis will be sent to your email when ready.`,
      [
        {
          text: 'OK',
          onPress: () => {
            // Simulate export process
            setTimeout(() => {
              Alert.alert('Export Complete', `${type} data has been exported successfully!`);
            }, 2000);
          }
        }
      ]
    );
  };

  const renderSettingItem = (title, description, value, onValueChange, type = 'switch') => (
    <View style={styles.settingItem}>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        <Text style={styles.settingDescription}>{description}</Text>
      </View>
      {type === 'switch' ? (
        <Switch
          value={value}
          onValueChange={onValueChange}
          trackColor={{ false: config.COLORS.disabled, true: config.COLORS.primary }}
          thumbColor={value ? '#fff' : '#f4f3f4'}
        />
      ) : (
        <TextInput
          style={styles.numberInput}
          value={value.toString()}
          onChangeText={(text) => onValueChange(parseInt(text) || 0)}
          keyboardType="numeric"
          placeholder="0"
        />
      )}
    </View>
  );

  const renderSection = (title, children) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.sectionContent}>
        {children}
      </View>
    </View>
  );

  if (!canManageSystem) {
    return (
      <View style={styles.noAccessContainer}>
        <Ionicons name="lock-closed" size={64} color={config.COLORS.disabled} />
        <Text style={styles.noAccessTitle}>Access Restricted</Text>
        <Text style={styles.noAccessText}>
          You don't have permission to access system settings. Contact your administrator for access.
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={config.COLORS.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>AlertComm Settings</Text>
        <TouchableOpacity onPress={saveSettings} style={styles.saveButton} disabled={loading}>
          <Text style={[styles.saveButtonText, loading && styles.saveButtonDisabled]}>
            {loading ? 'Saving...' : 'Save'}
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.scrollContent}>
        {/* System Settings */}
        {renderSection('System Settings', [
          renderSettingItem(
            'Auto Assignments',
            'Automatically assign responders based on availability and location',
            settings.autoAssignments,
            (value) => updateSetting('autoAssignments', value)
          ),
          renderSettingItem(
            'Real-time Tracking',
            'Enable GPS tracking for responders during active events',
            settings.realTimeTracking,
            (value) => updateSetting('realTimeTracking', value)
          ),
          renderSettingItem(
            'Emergency Alerts',
            'Send immediate alerts for high-priority emergencies',
            settings.emergencyAlerts,
            (value) => updateSetting('emergencyAlerts', value)
          ),
          renderSettingItem(
            'Data Retention (days)',
            'How long to keep event and communication data',
            settings.dataRetention,
            (value) => updateSetting('dataRetention', value),
            'number'
          ),
        ])}

        {/* Notification Settings */}
        {renderSection('Notification Settings', [
          renderSettingItem(
            'Push Notifications',
            'Send push notifications to mobile devices',
            settings.pushNotifications,
            (value) => updateSetting('pushNotifications', value)
          ),
          renderSettingItem(
            'Email Notifications',
            'Send notifications via email',
            settings.emailNotifications,
            (value) => updateSetting('emailNotifications', value)
          ),
          renderSettingItem(
            'SMS Notifications',
            'Send critical alerts via SMS (additional charges may apply)',
            settings.smsNotifications,
            (value) => updateSetting('smsNotifications', value)
          ),
        ])}

        {/* Security Settings */}
        {renderSection('Security Settings', [
          renderSettingItem(
            'Session Timeout (minutes)',
            'Automatically log out users after inactivity',
            settings.sessionTimeout,
            (value) => updateSetting('sessionTimeout', value),
            'number'
          ),
          renderSettingItem(
            'Require Strong Passwords',
            'Enforce strong password requirements for all users',
            settings.requireStrongPasswords,
            (value) => updateSetting('requireStrongPasswords', value)
          ),
          renderSettingItem(
            'Two-Factor Authentication',
            'Require 2FA for all user accounts',
            settings.twoFactorAuth,
            (value) => updateSetting('twoFactorAuth', value)
          ),
        ])}

        {/* Communication Settings */}
        {renderSection('Communication Settings', [
          renderSettingItem(
            'Allow Global Chat',
            'Enable global team chat for all users',
            settings.allowGlobalChat,
            (value) => updateSetting('allowGlobalChat', value)
          ),
          renderSettingItem(
            'Moderate Chat',
            'Require approval for chat messages',
            settings.moderateChat,
            (value) => updateSetting('moderateChat', value)
          ),
          renderSettingItem(
            'File Uploads',
            'Allow users to upload files and documents',
            settings.fileUploads,
            (value) => updateSetting('fileUploads', value)
          ),
          renderSettingItem(
            'Max File Size (MB)',
            'Maximum file size for uploads',
            settings.maxFileSize,
            (value) => updateSetting('maxFileSize', value),
            'number'
          ),
        ])}

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => {
              console.log('Create Event pressed');
              navigation.navigate('CreateEvent');
            }}
          >
            <Ionicons name="add-circle" size={24} color={config.COLORS.primary} />
            <Text style={styles.actionButtonText}>Create New Event</Text>
            <Ionicons name="chevron-forward" size={20} color={config.COLORS.textSecondary} />
          </TouchableOpacity>

          {canManageUsers && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => {
                console.log('Manage Users pressed');
                navigation.navigate('UserManagement');
              }}
            >
              <Ionicons name="people" size={24} color={config.COLORS.primary} />
              <Text style={styles.actionButtonText}>Manage Users</Text>
              <Ionicons name="chevron-forward" size={20} color={config.COLORS.textSecondary} />
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => {
              console.log('View Reports pressed');
              // Navigate to Reports tab
              navigation.navigate('Reports');
            }}
          >
            <Ionicons name="bar-chart" size={24} color={config.COLORS.primary} />
            <Text style={styles.actionButtonText}>View Reports</Text>
            <Ionicons name="chevron-forward" size={20} color={config.COLORS.textSecondary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => {
              Alert.alert(
                'Export Data',
                'Choose data to export:',
                [
                  { text: 'Events Data', onPress: () => exportData('events') },
                  { text: 'User Data', onPress: () => exportData('users') },
                  { text: 'System Logs', onPress: () => exportData('logs') },
                  { text: 'All Data', onPress: () => exportData('all') },
                  { text: 'Cancel', style: 'cancel' }
                ]
              );
            }}
          >
            <Ionicons name="download" size={24} color={config.COLORS.primary} />
            <Text style={styles.actionButtonText}>Export Data</Text>
            <Ionicons name="chevron-forward" size={20} color={config.COLORS.textSecondary} />
          </TouchableOpacity>
        </View>

        {/* Reset Button */}
        <View style={styles.section}>
          <TouchableOpacity style={styles.resetButton} onPress={resetToDefaults}>
            <Ionicons name="refresh" size={20} color={config.COLORS.error} />
            <Text style={styles.resetButtonText}>Reset to Defaults</Text>
          </TouchableOpacity>
        </View>

        {/* System Info */}
        <View style={styles.systemInfo}>
          <Text style={styles.systemInfoTitle}>System Information</Text>
          <Text style={styles.systemInfoText}>AlertComm v1.0.0</Text>
          <Text style={styles.systemInfoText}>Database: Connected</Text>
          <Text style={styles.systemInfoText}>Last Backup: Today, 3:00 AM</Text>
          <Text style={styles.systemInfoText}>Active Users: 24</Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: config.COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
    backgroundColor: config.COLORS.surface,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: config.COLORS.text,
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: config.COLORS.primary,
    borderRadius: 6,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 32,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: config.COLORS.text,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: config.COLORS.surface,
  },
  sectionContent: {
    backgroundColor: config.COLORS.surface,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
  },
  settingContent: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: config.COLORS.text,
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    lineHeight: 18,
  },
  numberInput: {
    borderWidth: 1,
    borderColor: config.COLORS.border,
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    color: config.COLORS.text,
    backgroundColor: config.COLORS.background,
    minWidth: 80,
    textAlign: 'center',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: config.COLORS.surface,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
  },
  actionButtonText: {
    fontSize: 16,
    color: config.COLORS.text,
    marginLeft: 12,
    flex: 1,
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: config.COLORS.surface,
    marginHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: config.COLORS.error,
  },
  resetButtonText: {
    fontSize: 16,
    color: config.COLORS.error,
    fontWeight: '500',
    marginLeft: 8,
  },
  systemInfo: {
    backgroundColor: config.COLORS.surface,
    marginHorizontal: 16,
    padding: 16,
    borderRadius: 8,
    marginTop: 16,
  },
  systemInfoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: config.COLORS.text,
    marginBottom: 8,
  },
  systemInfoText: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    marginBottom: 2,
  },
  noAccessContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    backgroundColor: config.COLORS.background,
  },
  noAccessTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: config.COLORS.text,
    marginTop: 16,
    marginBottom: 8,
  },
  noAccessText: {
    fontSize: 16,
    color: config.COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
});

export default SettingsScreen;
