import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import { usePermission } from '../components/PermissionGuard';
import { PERMISSIONS } from '../utils/permissions';
import apiService from '../services/api';
import config from '../config/config';

const { width } = Dimensions.get('window');

const ReportsScreen = ({ navigation }) => {
  const { user } = useAuth();
  const canViewAnalytics = usePermission(PERMISSIONS.VIEW_ANALYTICS);
  const canExportData = usePermission(PERMISSIONS.EXPORT_DATA);
  
  const [reportData, setReportData] = useState({
    totalEvents: 0,
    activeEvents: 0,
    completedEvents: 0,
    totalResponders: 0,
    activeResponders: 0,
    avgResponseTime: 0,
    recentEvents: [],
    responseTimeData: [],
    eventsByPriority: {
      immediate: 0,
      high: 0,
      medium: 0,
      low: 0,
    },
    responderPerformance: [],
  });

  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('7d'); // 7d, 30d, 90d, 1y

  useEffect(() => {
    loadReportData();
  }, [selectedPeriod]);

  const loadReportData = async () => {
    setLoading(true);
    try {
      // Simulate API calls for report data
      const events = await apiService.getEvents();
      const users = await apiService.getUsers();
      
      // Calculate metrics
      const totalEvents = events.length;
      const activeEvents = events.filter(e => e.status === 'active').length;
      const completedEvents = events.filter(e => e.status === 'completed').length;
      const totalResponders = users.filter(u => ['staff', 'supervisor'].includes(u.role)).length;
      const activeResponders = users.filter(u => u.status === 1 && ['staff', 'supervisor'].includes(u.role)).length;
      
      // Simulate additional metrics
      const avgResponseTime = Math.floor(Math.random() * 15) + 5; // 5-20 minutes
      
      // Event priority distribution
      const eventsByPriority = {
        immediate: events.filter(e => e.priority === 'immediate').length,
        high: events.filter(e => e.priority === 'high').length,
        medium: events.filter(e => e.priority === 'medium').length,
        low: events.filter(e => e.priority === 'low').length,
      };

      // Recent events (last 5)
      const recentEvents = events.slice(0, 5);

      // Simulate response time data for chart
      const responseTimeData = Array.from({ length: 7 }, (_, i) => ({
        day: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'][i],
        time: Math.floor(Math.random() * 10) + 5,
      }));

      // Simulate responder performance
      const responderPerformance = users
        .filter(u => ['staff', 'supervisor'].includes(u.role))
        .slice(0, 5)
        .map(u => ({
          name: `${u.first_name} ${u.last_name}`,
          eventsCompleted: Math.floor(Math.random() * 20) + 5,
          avgResponseTime: Math.floor(Math.random() * 10) + 5,
          rating: (Math.random() * 2 + 3).toFixed(1), // 3.0-5.0
        }));

      setReportData({
        totalEvents,
        activeEvents,
        completedEvents,
        totalResponders,
        activeResponders,
        avgResponseTime,
        recentEvents,
        responseTimeData,
        eventsByPriority,
        responderPerformance,
      });
    } catch (error) {
      console.error('Error loading report data:', error);
      Alert.alert('Error', 'Failed to load report data');
    } finally {
      setLoading(false);
    }
  };

  const exportReport = () => {
    Alert.alert(
      'Export Report',
      'Choose export format:',
      [
        { text: 'PDF', onPress: () => handleExport('pdf') },
        { text: 'Excel', onPress: () => handleExport('excel') },
        { text: 'CSV', onPress: () => handleExport('csv') },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  };

  const handleExport = (format) => {
    Alert.alert('Export Started', `Report will be exported as ${format.toUpperCase()} and sent to your email.`);
  };

  const renderMetricCard = (title, value, subtitle, icon, color = config.COLORS.primary) => (
    <View style={[styles.metricCard, { borderLeftColor: color }]}>
      <View style={styles.metricHeader}>
        <Ionicons name={icon} size={24} color={color} />
        <Text style={styles.metricTitle}>{title}</Text>
      </View>
      <Text style={styles.metricValue}>{value}</Text>
      {subtitle && <Text style={styles.metricSubtitle}>{subtitle}</Text>}
    </View>
  );

  const renderPeriodSelector = () => (
    <View style={styles.periodSelector}>
      {[
        { key: '7d', label: '7 Days' },
        { key: '30d', label: '30 Days' },
        { key: '90d', label: '90 Days' },
        { key: '1y', label: '1 Year' },
      ].map((period) => (
        <TouchableOpacity
          key={period.key}
          style={[
            styles.periodButton,
            selectedPeriod === period.key && styles.periodButtonActive
          ]}
          onPress={() => setSelectedPeriod(period.key)}
        >
          <Text style={[
            styles.periodButtonText,
            selectedPeriod === period.key && styles.periodButtonTextActive
          ]}>
            {period.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderResponseTimeChart = () => (
    <View style={styles.chartCard}>
      <Text style={styles.chartTitle}>Average Response Time (Minutes)</Text>
      <View style={styles.chartContainer}>
        {reportData.responseTimeData.map((item, index) => (
          <View key={index} style={styles.chartBar}>
            <View
              style={[
                styles.chartBarFill,
                { height: `${(item.time / 20) * 100}%` }
              ]}
            />
            <Text style={styles.chartBarLabel}>{item.day}</Text>
            <Text style={styles.chartBarValue}>{item.time}m</Text>
          </View>
        ))}
      </View>
    </View>
  );

  const renderPriorityDistribution = () => (
    <View style={styles.chartCard}>
      <Text style={styles.chartTitle}>Events by Priority</Text>
      <View style={styles.priorityContainer}>
        {Object.entries(reportData.eventsByPriority).map(([priority, count]) => {
          const colors = {
            immediate: '#d32f2f',
            high: '#f44336',
            medium: '#ff9800',
            low: '#4caf50',
          };
          return (
            <View key={priority} style={styles.priorityItem}>
              <View style={[styles.priorityIndicator, { backgroundColor: colors[priority] }]} />
              <Text style={styles.priorityLabel}>{priority.charAt(0).toUpperCase() + priority.slice(1)}</Text>
              <Text style={styles.priorityValue}>{count}</Text>
            </View>
          );
        })}
      </View>
    </View>
  );

  const renderTopPerformers = () => (
    <View style={styles.chartCard}>
      <Text style={styles.chartTitle}>Top Performers</Text>
      {reportData.responderPerformance.map((performer, index) => (
        <View key={index} style={styles.performerItem}>
          <View style={styles.performerRank}>
            <Text style={styles.performerRankText}>{index + 1}</Text>
          </View>
          <View style={styles.performerInfo}>
            <Text style={styles.performerName}>{performer.name}</Text>
            <Text style={styles.performerStats}>
              {performer.eventsCompleted} events • {performer.avgResponseTime}m avg • ⭐ {performer.rating}
            </Text>
          </View>
        </View>
      ))}
    </View>
  );

  if (!canViewAnalytics) {
    return (
      <View style={styles.noAccessContainer}>
        <Ionicons name="bar-chart" size={64} color={config.COLORS.disabled} />
        <Text style={styles.noAccessTitle}>Access Restricted</Text>
        <Text style={styles.noAccessText}>
          You don't have permission to view analytics and reports. Contact your administrator for access.
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Analytics & Reports</Text>
        {canExportData && (
          <TouchableOpacity onPress={exportReport} style={styles.exportButton}>
            <Ionicons name="download" size={20} color="#fff" />
            <Text style={styles.exportButtonText}>Export</Text>
          </TouchableOpacity>
        )}
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.scrollContent}>
        {/* Period Selector */}
        {renderPeriodSelector()}

        {/* Key Metrics */}
        <View style={styles.metricsGrid}>
          {renderMetricCard('Total Events', reportData.totalEvents, 'All time', 'calendar', '#2196f3')}
          {renderMetricCard('Active Events', reportData.activeEvents, 'Currently active', 'alert-circle', '#f44336')}
          {renderMetricCard('Completed Events', reportData.completedEvents, 'Successfully resolved', 'checkmark-circle', '#4caf50')}
          {renderMetricCard('Active Responders', `${reportData.activeResponders}/${reportData.totalResponders}`, 'Available now', 'people', '#ff9800')}
          {renderMetricCard('Avg Response Time', `${reportData.avgResponseTime}m`, 'Last 30 days', 'time', '#9c27b0')}
          {renderMetricCard('Response Rate', '94%', 'Assignment acceptance', 'trending-up', '#00bcd4')}
        </View>

        {/* Charts */}
        {renderResponseTimeChart()}
        {renderPriorityDistribution()}
        {renderTopPerformers()}

        {/* Recent Events Summary */}
        <View style={styles.chartCard}>
          <Text style={styles.chartTitle}>Recent Events</Text>
          {reportData.recentEvents.map((event, index) => (
            <TouchableOpacity
              key={event.id}
              style={styles.eventItem}
              onPress={() => navigation.navigate('EventDetail', { eventId: event.id })}
            >
              <View style={styles.eventInfo}>
                <Text style={styles.eventTitle}>{event.title}</Text>
                <Text style={styles.eventMeta}>
                  {event.priority} priority • {event.status} • {new Date(event.created_at).toLocaleDateString()}
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={config.COLORS.textSecondary} />
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: config.COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
    backgroundColor: config.COLORS.surface,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: config.COLORS.text,
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: config.COLORS.primary,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
  },
  exportButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 32,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: config.COLORS.surface,
    margin: 16,
    borderRadius: 8,
    padding: 4,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 6,
  },
  periodButtonActive: {
    backgroundColor: config.COLORS.primary,
  },
  periodButtonText: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    fontWeight: '500',
  },
  periodButtonTextActive: {
    color: '#fff',
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 8,
  },
  metricCard: {
    width: (width - 48) / 2,
    backgroundColor: config.COLORS.surface,
    margin: 8,
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  metricTitle: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    marginLeft: 8,
    fontWeight: '500',
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: config.COLORS.text,
    marginBottom: 4,
  },
  metricSubtitle: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
  },
  chartCard: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    padding: 16,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: config.COLORS.text,
    marginBottom: 16,
  },
  chartContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'flex-end',
    height: 120,
  },
  chartBar: {
    alignItems: 'center',
    flex: 1,
  },
  chartBarFill: {
    width: 20,
    backgroundColor: config.COLORS.primary,
    borderRadius: 2,
    marginBottom: 8,
  },
  chartBarLabel: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
    marginBottom: 2,
  },
  chartBarValue: {
    fontSize: 10,
    color: config.COLORS.textSecondary,
    fontWeight: '500',
  },
  priorityContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  priorityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '50%',
    marginBottom: 12,
  },
  priorityIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  priorityLabel: {
    fontSize: 14,
    color: config.COLORS.text,
    flex: 1,
  },
  priorityValue: {
    fontSize: 16,
    fontWeight: '600',
    color: config.COLORS.text,
  },
  performerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
  },
  performerRank: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: config.COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  performerRankText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  performerInfo: {
    flex: 1,
  },
  performerName: {
    fontSize: 16,
    fontWeight: '500',
    color: config.COLORS.text,
    marginBottom: 2,
  },
  performerStats: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
  },
  eventItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
  },
  eventInfo: {
    flex: 1,
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: config.COLORS.text,
    marginBottom: 2,
  },
  eventMeta: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
  },
  noAccessContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    backgroundColor: config.COLORS.background,
  },
  noAccessTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: config.COLORS.text,
    marginTop: 16,
    marginBottom: 8,
  },
  noAccessText: {
    fontSize: 16,
    color: config.COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
});

export default ReportsScreen;
