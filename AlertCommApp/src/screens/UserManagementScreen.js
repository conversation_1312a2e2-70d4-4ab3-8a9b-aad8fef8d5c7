import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  RefreshControl,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import { usePermission } from '../components/PermissionGuard';
import { PERMISSIONS, getRoleDisplayName } from '../utils/permissions';
import apiService from '../services/api';
import config from '../config/config';

const UserManagementScreen = ({ navigation }) => {
  const { user } = useAuth();
  const canManageUsers = usePermission(PERMISSIONS.MANAGE_USERS);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (canManageUsers) {
      loadUsers();
    }
  }, [canManageUsers]);

  const loadUsers = async () => {
    try {
      const usersData = await apiService.getUsers();
      setUsers(usersData);
    } catch (error) {
      console.error('Error loading users:', error);
      Alert.alert('Error', 'Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadUsers();
    setRefreshing(false);
  };

  const toggleUserStatus = async (userId, currentStatus) => {
    try {
      const newStatus = currentStatus === 1 ? 0 : 1;
      const statusText = newStatus === 1 ? 'activated' : 'deactivated';
      
      Alert.alert(
        'Confirm Action',
        `Are you sure you want to ${statusText === 'activated' ? 'activate' : 'deactivate'} this user?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Confirm',
            onPress: async () => {
              // In a real app, this would call API
              // await apiService.updateUserStatus(userId, newStatus);
              
              // Update local state
              setUsers(prev => prev.map(u => 
                u.id === userId ? { ...u, status: newStatus } : u
              ));
              
              Alert.alert('Success', `User has been ${statusText}`);
            }
          }
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to update user status');
    }
  };

  const resetUserPassword = (userId, userName) => {
    Alert.alert(
      'Reset Password',
      `Reset password for ${userName}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          onPress: () => {
            // In a real app, this would call API
            Alert.alert(
              'Password Reset',
              'A temporary password has been sent to the user\'s email address.'
            );
          }
        }
      ]
    );
  };

  const editUserRole = (userId, userName, currentRole) => {
    const roles = ['volunteer', 'staff', 'supervisor', 'lead', 'commander'];
    
    Alert.alert(
      'Change Role',
      `Select new role for ${userName}:`,
      [
        ...roles.map(role => ({
          text: getRoleDisplayName(role),
          onPress: () => {
            if (role !== currentRole) {
              updateUserRole(userId, role);
            }
          }
        })),
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  };

  const updateUserRole = async (userId, newRole) => {
    try {
      // In a real app, this would call API
      // await apiService.updateUserRole(userId, newRole);
      
      // Update local state
      setUsers(prev => prev.map(u => 
        u.id === userId ? { ...u, role: newRole } : u
      ));
      
      Alert.alert('Success', `User role updated to ${getRoleDisplayName(newRole)}`);
    } catch (error) {
      Alert.alert('Error', 'Failed to update user role');
    }
  };

  const renderUserItem = (userItem) => {
    const isActive = userItem.status === 1;
    
    return (
      <View key={userItem.id} style={styles.userCard}>
        <View style={styles.userHeader}>
          <View style={styles.userInfo}>
            <Text style={styles.userName}>
              {userItem.first_name} {userItem.last_name}
            </Text>
            <Text style={styles.userEmail}>{userItem.email}</Text>
            <Text style={styles.userRole}>
              {getRoleDisplayName(userItem.role)} • {userItem.job_role}
            </Text>
          </View>
          <View style={styles.userStatus}>
            <View style={[
              styles.statusIndicator,
              { backgroundColor: isActive ? '#4caf50' : '#f44336' }
            ]} />
            <Text style={[
              styles.statusText,
              { color: isActive ? '#4caf50' : '#f44336' }
            ]}>
              {isActive ? 'Active' : 'Inactive'}
            </Text>
          </View>
        </View>

        <View style={styles.userActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => toggleUserStatus(userItem.id, userItem.status)}
          >
            <Ionicons 
              name={isActive ? 'pause' : 'play'} 
              size={16} 
              color={config.COLORS.primary} 
            />
            <Text style={styles.actionButtonText}>
              {isActive ? 'Deactivate' : 'Activate'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => editUserRole(
              userItem.id, 
              `${userItem.first_name} ${userItem.last_name}`, 
              userItem.role
            )}
          >
            <Ionicons name="person-circle" size={16} color={config.COLORS.primary} />
            <Text style={styles.actionButtonText}>Change Role</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => resetUserPassword(
              userItem.id, 
              `${userItem.first_name} ${userItem.last_name}`
            )}
          >
            <Ionicons name="key" size={16} color={config.COLORS.primary} />
            <Text style={styles.actionButtonText}>Reset Password</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  if (!canManageUsers) {
    return (
      <View style={styles.noAccessContainer}>
        <Ionicons name="people" size={64} color={config.COLORS.disabled} />
        <Text style={styles.noAccessTitle}>Access Restricted</Text>
        <Text style={styles.noAccessText}>
          You don't have permission to manage users. Contact your administrator for access.
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={config.COLORS.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>User Management</Text>
        <TouchableOpacity onPress={handleRefresh} style={styles.refreshButton}>
          <Ionicons name="refresh" size={24} color={config.COLORS.text} />
        </TouchableOpacity>
      </View>

      {/* Stats */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{users.length}</Text>
          <Text style={styles.statLabel}>Total Users</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{users.filter(u => u.status === 1).length}</Text>
          <Text style={styles.statLabel}>Active Users</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{users.filter(u => u.role === 'staff').length}</Text>
          <Text style={styles.statLabel}>Staff Members</Text>
        </View>
      </View>

      {/* Users List */}
      <ScrollView
        style={styles.usersList}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[config.COLORS.primary]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {users.map(renderUserItem)}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: config.COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
    backgroundColor: config.COLORS.surface,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: config.COLORS.text,
  },
  refreshButton: {
    padding: 4,
  },
  statsContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: config.COLORS.surface,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    padding: 12,
    marginHorizontal: 4,
    backgroundColor: config.COLORS.background,
    borderRadius: 8,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: config.COLORS.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
    textAlign: 'center',
  },
  usersList: {
    flex: 1,
  },
  userCard: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    marginBottom: 8,
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  userHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: '600',
    color: config.COLORS.text,
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    marginBottom: 2,
  },
  userRole: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
  },
  userStatus: {
    alignItems: 'center',
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginBottom: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  userActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: config.COLORS.primary + '20',
    borderRadius: 6,
    flex: 1,
    marginHorizontal: 4,
    justifyContent: 'center',
  },
  actionButtonText: {
    fontSize: 12,
    color: config.COLORS.primary,
    fontWeight: '500',
    marginLeft: 4,
  },
  noAccessContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    backgroundColor: config.COLORS.background,
  },
  noAccessTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: config.COLORS.text,
    marginTop: 16,
    marginBottom: 8,
  },
  noAccessText: {
    fontSize: 16,
    color: config.COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
});

export default UserManagementScreen;
