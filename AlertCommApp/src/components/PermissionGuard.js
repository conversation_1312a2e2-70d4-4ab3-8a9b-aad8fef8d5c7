import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import { hasPermission, hasAnyPermission, hasAllPermissions } from '../utils/permissions';
import config from '../config/config';

/**
 * PermissionGuard component that conditionally renders children based on user permissions
 * 
 * @param {Object} props
 * @param {string|string[]} props.permission - Single permission or array of permissions
 * @param {string} props.mode - 'any' (default) or 'all' - how to evaluate multiple permissions
 * @param {React.ReactNode} props.children - Content to render if permission check passes
 * @param {React.ReactNode} props.fallback - Content to render if permission check fails
 * @param {boolean} props.showFallback - Whether to show fallback content or nothing
 * @param {string} props.fallbackMessage - Custom message for fallback
 */
const PermissionGuard = ({
  permission,
  mode = 'any',
  children,
  fallback = null,
  showFallback = false,
  fallbackMessage = 'You do not have permission to access this feature.'
}) => {
  const { user } = useAuth();

  if (!user || !user.role) {
    return showFallback ? (
      fallback || (
        <View style={styles.fallbackContainer}>
          <Ionicons name="lock-closed-outline" size={24} color={config.COLORS.textSecondary} />
          <Text style={styles.fallbackText}>Authentication required</Text>
        </View>
      )
    ) : null;
  }

  let hasAccess = false;

  if (Array.isArray(permission)) {
    if (mode === 'all') {
      hasAccess = hasAllPermissions(user.role, permission);
    } else {
      hasAccess = hasAnyPermission(user.role, permission);
    }
  } else if (typeof permission === 'string') {
    hasAccess = hasPermission(user.role, permission);
  }

  if (hasAccess) {
    return children;
  }

  if (showFallback) {
    return fallback || (
      <View style={styles.fallbackContainer}>
        <Ionicons name="lock-closed-outline" size={24} color={config.COLORS.textSecondary} />
        <Text style={styles.fallbackText}>{fallbackMessage}</Text>
      </View>
    );
  }

  return null;
};

/**
 * Hook to check permissions in components
 * @param {string|string[]} permission - Permission(s) to check
 * @param {string} mode - 'any' or 'all' for multiple permissions
 * @returns {boolean} - Whether user has the permission(s)
 */
export const usePermission = (permission, mode = 'any') => {
  const { user } = useAuth();

  if (!user || !user.role) {
    return false;
  }

  if (Array.isArray(permission)) {
    if (mode === 'all') {
      return hasAllPermissions(user.role, permission);
    } else {
      return hasAnyPermission(user.role, permission);
    }
  } else if (typeof permission === 'string') {
    return hasPermission(user.role, permission);
  }

  return false;
};

/**
 * Higher-order component for permission-based rendering
 * @param {React.Component} Component - Component to wrap
 * @param {string|string[]} permission - Permission(s) required
 * @param {Object} options - Additional options
 * @returns {React.Component} - Wrapped component
 */
export const withPermission = (Component, permission, options = {}) => {
  return (props) => (
    <PermissionGuard permission={permission} {...options}>
      <Component {...props} />
    </PermissionGuard>
  );
};

const styles = StyleSheet.create({
  fallbackContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: config.COLORS.background,
  },
  fallbackText: {
    fontSize: 16,
    color: config.COLORS.textSecondary,
    textAlign: 'center',
    marginTop: 12,
    lineHeight: 22,
  },
});

export default PermissionGuard;
