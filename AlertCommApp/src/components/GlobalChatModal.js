import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  Modal,
  FlatList,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import apiService from '../services/api';
import socketService from '../services/socket';
import config from '../config/config';

const GlobalChatModal = ({ visible, onClose, eventId = null, isGlobalChat = true }) => {
  const { user } = useAuth();
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [activeEventId, setActiveEventId] = useState(eventId);
  const flatListRef = useRef(null);

  useEffect(() => {
    if (visible) {
      if (isGlobalChat && !eventId) {
        // For global chat, find the most recent active event or use general chat
        loadActiveEventForChat();
      } else if (eventId) {
        setActiveEventId(eventId);
        loadMessages();
      }
    }
  }, [visible, eventId, isGlobalChat]);

  useEffect(() => {
    if (visible && activeEventId) {
      loadMessages();
      setupSocketListeners();
      socketService.joinEventRoom(eventId);
    }

    return () => {
      socketService.off('chatMessage', handleNewMessage);
    };
  }, [visible, activeEventId]);

  const loadActiveEventForChat = async () => {
    try {
      const events = await apiService.getEvents();
      if (events && events.length > 0) {
        // For global chat, use the most recent active event or create a general chat room
        const activeEvent = events.find(event => event.status === 'active') || events[0];
        setActiveEventId(activeEvent.id);
      } else {
        // If no events, use a general chat room ID
        setActiveEventId('general');
      }
    } catch (error) {
      console.error('Error loading active event for chat:', error);
      setActiveEventId('general'); // Fallback to general chat
    }
  };

  const loadMessages = async () => {
    if (!activeEventId) return;
    if (!eventId) return;
    
    setLoading(true);
    try {
      const messagesData = await apiService.getChatMessages(eventId);
      setMessages(messagesData.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp)));
      // Scroll to bottom after loading
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      console.error('Error loading messages:', error);
      Alert.alert('Error', 'Failed to load chat messages');
    } finally {
      setLoading(false);
    }
  };

  const setupSocketListeners = () => {
    socketService.on('chatMessage', handleNewMessage);
  };

  const handleNewMessage = (message) => {
    setMessages(prev => [...prev, message]);
    // Scroll to bottom when new message arrives
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || !eventId || sending) return;

    setSending(true);
    try {
      const messageData = await apiService.sendChatMessage(eventId, newMessage.trim());
      setNewMessage('');
      // Message will be added via socket listener
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Error', 'Failed to send message');
    } finally {
      setSending(false);
    }
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderMessage = ({ item }) => {
    const isOwnMessage = item.user_id === user.id;
    const senderName = item.first_name && item.last_name 
      ? `${item.first_name} ${item.last_name}`
      : item.username;

    return (
      <View style={[
        styles.messageContainer,
        isOwnMessage ? styles.ownMessage : styles.otherMessage
      ]}>
        {!isOwnMessage && (
          <Text style={styles.senderName}>{senderName}</Text>
        )}
        <Text style={[
          styles.messageText,
          isOwnMessage ? styles.ownMessageText : styles.otherMessageText
        ]}>
          {item.message}
        </Text>
        <Text style={[
          styles.messageTime,
          isOwnMessage ? styles.ownMessageTime : styles.otherMessageTime
        ]}>
          {formatTime(item.timestamp)}
        </Text>
      </View>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <KeyboardAvoidingView 
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={config.COLORS.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>
            {isGlobalChat ? 'Global Team Chat' : `Event Chat`}
          </Text>
          {isGlobalChat && (
            <Text style={styles.headerSubtitle}>
              General coordination chat
            </Text>
          )}
          <View style={styles.placeholder} />
        </View>

        {/* Messages */}
        <FlatList
          ref={flatListRef}
          data={messages}
          keyExtractor={(item) => item.id?.toString() || Math.random().toString()}
          renderItem={renderMessage}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
        />

        {/* Input */}
        <View style={styles.inputContainer}>
          <TextInput
            style={styles.textInput}
            value={newMessage}
            onChangeText={setNewMessage}
            placeholder="Type a message..."
            multiline
            maxLength={500}
            editable={!sending}
          />
          <TouchableOpacity
            style={[
              styles.sendButton,
              (!newMessage.trim() || sending) && styles.sendButtonDisabled
            ]}
            onPress={sendMessage}
            disabled={!newMessage.trim() || sending}
          >
            <Ionicons 
              name={sending ? "hourglass" : "send"} 
              size={20} 
              color={(!newMessage.trim() || sending) ? config.COLORS.textSecondary : config.COLORS.primary} 
            />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: config.COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
    backgroundColor: config.COLORS.surface,
  },
  closeButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: config.COLORS.text,
  },
  headerSubtitle: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
    textAlign: 'center',
    marginTop: 2,
  },
  placeholder: {
    width: 32,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    padding: 16,
  },
  messageContainer: {
    marginBottom: 12,
    maxWidth: '80%',
  },
  ownMessage: {
    alignSelf: 'flex-end',
    backgroundColor: config.COLORS.primary,
    borderRadius: 18,
    borderBottomRightRadius: 4,
    padding: 12,
  },
  otherMessage: {
    alignSelf: 'flex-start',
    backgroundColor: config.COLORS.surface,
    borderRadius: 18,
    borderBottomLeftRadius: 4,
    padding: 12,
  },
  senderName: {
    fontSize: 12,
    fontWeight: '600',
    color: config.COLORS.primary,
    marginBottom: 4,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  ownMessageText: {
    color: '#fff',
  },
  otherMessageText: {
    color: config.COLORS.text,
  },
  messageTime: {
    fontSize: 11,
    marginTop: 4,
  },
  ownMessageTime: {
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'right',
  },
  otherMessageTime: {
    color: config.COLORS.textSecondary,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: config.COLORS.border,
    backgroundColor: config.COLORS.surface,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: config.COLORS.border,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginRight: 12,
    maxHeight: 100,
    fontSize: 16,
    backgroundColor: config.COLORS.background,
    color: config.COLORS.text,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: config.COLORS.primary + '20',
  },
  sendButtonDisabled: {
    backgroundColor: config.COLORS.border,
  },
});

export default GlobalChatModal;
