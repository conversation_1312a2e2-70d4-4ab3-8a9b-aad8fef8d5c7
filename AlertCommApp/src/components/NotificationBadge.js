import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNotifications } from '../context/NotificationContext';
import config from '../config/config';

const NotificationBadge = ({ onPress, style }) => {
  const { unreadCount } = useNotifications();

  if (unreadCount === 0) {
    return (
      <TouchableOpacity style={[styles.container, style]} onPress={onPress}>
        <Ionicons name="notifications-outline" size={24} color={config.COLORS.text} />
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity style={[styles.container, style]} onPress={onPress}>
      <Ionicons name="notifications" size={24} color={config.COLORS.primary} />
      <View style={styles.badge}>
        <Text style={styles.badgeText}>
          {unreadCount > 99 ? '99+' : unreadCount}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    padding: 8,
  },
  badge: {
    position: 'absolute',
    top: 2,
    right: 2,
    backgroundColor: config.COLORS.error || '#f44336',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: config.COLORS.surface || '#fff',
  },
  badgeText: {
    color: '#fff',
    fontSize: 11,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default NotificationBadge;
