import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  TouchableOpacity,
  Modal,
  Dimensions,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import config from '../config/config';

// Platform-specific imports
let MapView, Marker, Circle, Callout;
if (Platform.OS !== 'web') {
  const Maps = require('react-native-maps');
  MapView = Maps.default;
  Marker = Maps.Marker;
  Circle = Maps.Circle;
  Callout = Maps.Callout;
}

const { width, height } = Dimensions.get('window');

const EventMapView = ({ event, responders = [], style, onResponderPress }) => {
  const [region, setRegion] = useState(null);
  const [userLocation, setUserLocation] = useState(null);
  const [mapModalVisible, setMapModalVisible] = useState(false);

  useEffect(() => {
    initializeMap();
    getCurrentLocation();
  }, [event]);

  const initializeMap = () => {
    if (event?.latitude && event?.longitude) {
      setRegion({
        latitude: parseFloat(event.latitude),
        longitude: parseFloat(event.longitude),
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
    } else {
      // Default to a general area if no event location
      setRegion({
        latitude: 37.7749,
        longitude: -122.4194,
        latitudeDelta: 0.1,
        longitudeDelta: 0.1,
      });
    }
  };

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.log('Location permission denied');
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      setUserLocation({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  const getResponderStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'enroute':
        return '#ff9800';
      case 'on_scene':
        return '#4caf50';
      case 'completed':
        return '#2196f3';
      case 'unavailable':
        return '#f44336';
      default:
        return '#9e9e9e';
    }
  };

  const getEventPriorityColor = (priority) => {
    switch (priority?.toLowerCase()) {
      case 'immediate':
        return '#d32f2f';
      case 'high':
        return '#f44336';
      case 'medium':
        return '#ff9800';
      case 'low':
        return '#4caf50';
      default:
        return config.COLORS.primary;
    }
  };

  const handleResponderMarkerPress = (responder) => {
    if (onResponderPress) {
      onResponderPress(responder);
    }
  };

  const openFullScreenMap = () => {
    setMapModalVisible(true);
  };

  const renderMiniMap = () => {
    // Web fallback
    if (Platform.OS === 'web' || !MapView) {
      return (
        <TouchableOpacity style={[styles.miniMapContainer, style]} onPress={openFullScreenMap}>
          <View style={styles.webMapPlaceholder}>
            <Ionicons name="map" size={48} color={config.COLORS.primary} />
            <Text style={styles.webMapText}>Event Location Map</Text>
            {event?.latitude && event?.longitude && (
              <Text style={styles.coordinatesText}>
                {parseFloat(event.latitude).toFixed(4)}, {parseFloat(event.longitude).toFixed(4)}
              </Text>
            )}
            <View style={styles.mapOverlay}>
              <Ionicons name="expand" size={20} color="#fff" />
              <Text style={styles.expandText}>Tap to view details</Text>
            </View>
          </View>
        </TouchableOpacity>
      );
    }

    return (
      <TouchableOpacity style={[styles.miniMapContainer, style]} onPress={openFullScreenMap}>
        <MapView
          style={styles.miniMap}
          region={region}
          scrollEnabled={false}
          zoomEnabled={false}
          rotateEnabled={false}
          pitchEnabled={false}
        >
        {/* Event Location Marker */}
        {event?.latitude && event?.longitude && (
          <Marker
            coordinate={{
              latitude: parseFloat(event.latitude),
              longitude: parseFloat(event.longitude),
            }}
            title={event.title}
            description={event.description}
          >
            <View style={[styles.eventMarker, { backgroundColor: getEventPriorityColor(event.priority) }]}>
              <Ionicons name="alert-circle" size={20} color="#fff" />
            </View>
          </Marker>
        )}

        {/* Event Radius Circle */}
        {event?.latitude && event?.longitude && (
          <Circle
            center={{
              latitude: parseFloat(event.latitude),
              longitude: parseFloat(event.longitude),
            }}
            radius={1000} // 1km radius
            fillColor={getEventPriorityColor(event.priority) + '20'}
            strokeColor={getEventPriorityColor(event.priority)}
            strokeWidth={2}
          />
        )}

        {/* Responder Markers */}
        {responders.map((responder) => (
          responder.latitude && responder.longitude && (
            <Marker
              key={responder.id}
              coordinate={{
                latitude: parseFloat(responder.latitude),
                longitude: parseFloat(responder.longitude),
              }}
              onPress={() => handleResponderMarkerPress(responder)}
            >
              <View style={[styles.responderMarker, { backgroundColor: getResponderStatusColor(responder.status) }]}>
                <Ionicons name="person" size={16} color="#fff" />
              </View>
            </Marker>
          )
        ))}

        {/* User Location */}
        {userLocation && (
          <Marker
            coordinate={userLocation}
            title="Your Location"
          >
            <View style={styles.userMarker}>
              <Ionicons name="radio-button-on" size={16} color={config.COLORS.primary} />
            </View>
          </Marker>
        )}
      </MapView>
      
      <View style={styles.mapOverlay}>
        <Ionicons name="expand" size={20} color="#fff" />
        <Text style={styles.expandText}>Tap to expand</Text>
      </View>
    </TouchableOpacity>
  );

  const renderFullScreenMap = () => (
    <Modal
      visible={mapModalVisible}
      animationType="slide"
      presentationStyle="fullScreen"
    >
      <View style={styles.fullScreenContainer}>
        <View style={styles.mapHeader}>
          <TouchableOpacity onPress={() => setMapModalVisible(false)} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={config.COLORS.text} />
          </TouchableOpacity>
          <Text style={styles.mapTitle}>{event?.title || 'Event Map'}</Text>
          <View style={styles.placeholder} />
        </View>

        {Platform.OS === 'web' || !MapView ? (
          <View style={styles.webMapContainer}>
            <Text style={styles.webMapNote}>
              Interactive maps are not available on web. For full map functionality, please use the mobile app.
            </Text>
            {event?.latitude && event?.longitude && (
              <TouchableOpacity
                style={styles.openMapsButton}
                onPress={() => {
                  const url = `https://www.google.com/maps?q=${event.latitude},${event.longitude}`;
                  if (Platform.OS === 'web') {
                    window.open(url, '_blank');
                  }
                }}
              >
                <Ionicons name="map" size={20} color="#fff" />
                <Text style={styles.openMapsText}>Open in Google Maps</Text>
              </TouchableOpacity>
            )}
          </View>
        ) : (
          <MapView
            style={styles.fullScreenMap}
            region={region}
            showsUserLocation={true}
            showsMyLocationButton={true}
          >
          {/* Event Location Marker */}
          {event?.latitude && event?.longitude && (
            <Marker
              coordinate={{
                latitude: parseFloat(event.latitude),
                longitude: parseFloat(event.longitude),
              }}
            >
              <View style={[styles.eventMarker, { backgroundColor: getEventPriorityColor(event.priority) }]}>
                <Ionicons name="alert-circle" size={24} color="#fff" />
              </View>
              <Callout>
                <View style={styles.calloutContainer}>
                  <Text style={styles.calloutTitle}>{event.title}</Text>
                  <Text style={styles.calloutDescription}>{event.description}</Text>
                  <Text style={styles.calloutPriority}>Priority: {event.priority}</Text>
                </View>
              </Callout>
            </Marker>
          )}

          {/* Event Radius Circle */}
          {event?.latitude && event?.longitude && (
            <Circle
              center={{
                latitude: parseFloat(event.latitude),
                longitude: parseFloat(event.longitude),
              }}
              radius={1000}
              fillColor={getEventPriorityColor(event.priority) + '20'}
              strokeColor={getEventPriorityColor(event.priority)}
              strokeWidth={2}
            />
          )}

          {/* Responder Markers */}
          {responders.map((responder) => (
            responder.latitude && responder.longitude && (
              <Marker
                key={responder.id}
                coordinate={{
                  latitude: parseFloat(responder.latitude),
                  longitude: parseFloat(responder.longitude),
                }}
                onPress={() => handleResponderMarkerPress(responder)}
              >
                <View style={[styles.responderMarker, { backgroundColor: getResponderStatusColor(responder.status) }]}>
                  <Ionicons name="person" size={20} color="#fff" />
                </View>
                <Callout>
                  <View style={styles.calloutContainer}>
                    <Text style={styles.calloutTitle}>
                      {responder.first_name} {responder.last_name}
                    </Text>
                    <Text style={styles.calloutDescription}>
                      Role: {responder.job_role || responder.role}
                    </Text>
                    <Text style={styles.calloutStatus}>
                      Status: {responder.status}
                    </Text>
                  </View>
                </Callout>
              </Marker>
            )
          ))}
          </MapView>
        )}

        {/* Map Legend */}
        <View style={styles.legend}>
          <Text style={styles.legendTitle}>Legend</Text>
          <View style={styles.legendItem}>
            <View style={[styles.legendMarker, { backgroundColor: getEventPriorityColor(event?.priority) }]} />
            <Text style={styles.legendText}>Event Location</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendMarker, { backgroundColor: '#4caf50' }]} />
            <Text style={styles.legendText}>On Scene</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendMarker, { backgroundColor: '#ff9800' }]} />
            <Text style={styles.legendText}>En Route</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendMarker, { backgroundColor: '#9e9e9e' }]} />
            <Text style={styles.legendText}>Pending</Text>
          </View>
        </View>
      </View>
    </Modal>
  );

  if (!region) {
    return (
      <View style={[styles.loadingContainer, style]}>
        <Text style={styles.loadingText}>Loading map...</Text>
      </View>
    );
  }

  return (
    <>
      {renderMiniMap()}
      {renderFullScreenMap()}
    </>
  );
};

const styles = StyleSheet.create({
  miniMapContainer: {
    height: 200,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  miniMap: {
    flex: 1,
  },
  mapOverlay: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 8,
    padding: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  expandText: {
    color: '#fff',
    fontSize: 12,
    marginLeft: 4,
  },
  fullScreenContainer: {
    flex: 1,
    backgroundColor: config.COLORS.background,
  },
  mapHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
    backgroundColor: config.COLORS.surface,
  },
  closeButton: {
    padding: 4,
  },
  mapTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: config.COLORS.text,
  },
  placeholder: {
    width: 32,
  },
  fullScreenMap: {
    flex: 1,
  },
  eventMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#fff',
  },
  responderMarker: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  userMarker: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  calloutContainer: {
    padding: 8,
    minWidth: 150,
  },
  calloutTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  calloutDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  calloutPriority: {
    fontSize: 14,
    fontWeight: '500',
  },
  calloutStatus: {
    fontSize: 14,
    fontWeight: '500',
  },
  legend: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 8,
    padding: 12,
  },
  legendTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  legendMarker: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendText: {
    fontSize: 12,
    color: '#333',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: config.COLORS.surface,
    borderRadius: 12,
  },
  loadingText: {
    fontSize: 16,
    color: config.COLORS.textSecondary,
  },
  webMapPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: config.COLORS.background,
    position: 'relative',
  },
  webMapText: {
    fontSize: 16,
    fontWeight: '600',
    color: config.COLORS.text,
    marginTop: 8,
  },
  coordinatesText: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
    marginTop: 4,
  },
  webMapContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: config.COLORS.background,
  },
  webMapNote: {
    fontSize: 16,
    color: config.COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 22,
  },
  openMapsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: config.COLORS.primary,
    borderRadius: 8,
    padding: 12,
    paddingHorizontal: 20,
  },
  openMapsText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default EventMapView;
