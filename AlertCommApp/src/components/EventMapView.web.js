import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import config from '../config/config';

const { width, height } = Dimensions.get('window');

const EventMapView = ({ event, responders = [], style, onResponderPress }) => {
  const [mapModalVisible, setMapModalVisible] = useState(false);

  const openFullScreenMap = () => {
    setMapModalVisible(true);
  };

  const getResponderStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'enroute':
        return '#ff9800';
      case 'on_scene':
        return '#4caf50';
      case 'completed':
        return '#2196f3';
      case 'unavailable':
        return '#f44336';
      default:
        return '#9e9e9e';
    }
  };

  const getEventPriorityColor = (priority) => {
    switch (priority?.toLowerCase()) {
      case 'immediate':
        return '#d32f2f';
      case 'high':
        return '#f44336';
      case 'medium':
        return '#ff9800';
      case 'low':
        return '#4caf50';
      default:
        return config.COLORS.primary;
    }
  };

  const renderWebMap = () => {
    const mapUrl = event?.latitude && event?.longitude 
      ? `https://www.google.com/maps/embed/v1/place?key=YOUR_GOOGLE_MAPS_API_KEY&q=${event.latitude},${event.longitude}&zoom=15`
      : `https://www.google.com/maps/embed/v1/view?key=YOUR_GOOGLE_MAPS_API_KEY&center=37.7749,-122.4194&zoom=10`;

    return (
      <iframe
        width="100%"
        height="200"
        style={{ border: 0, borderRadius: 12 }}
        src={mapUrl}
        allowFullScreen
      />
    );
  };

  const renderMiniMap = () => (
    <TouchableOpacity style={[styles.miniMapContainer, style]} onPress={openFullScreenMap}>
      <View style={styles.webMapContainer}>
        <View style={styles.mapPlaceholder}>
          <Ionicons name="map" size={48} color={config.COLORS.primary} />
          <Text style={styles.mapPlaceholderText}>Event Location Map</Text>
          {event?.latitude && event?.longitude && (
            <Text style={styles.coordinatesText}>
              {parseFloat(event.latitude).toFixed(4)}, {parseFloat(event.longitude).toFixed(4)}
            </Text>
          )}
        </View>
        
        {/* Event Info Overlay */}
        <View style={styles.eventInfoOverlay}>
          <View style={[styles.eventMarker, { backgroundColor: getEventPriorityColor(event?.priority) }]}>
            <Ionicons name="alert-circle" size={16} color="#fff" />
          </View>
          <Text style={styles.eventTitle}>{event?.title || 'Event'}</Text>
        </View>

        {/* Responders Info */}
        {responders.length > 0 && (
          <View style={styles.respondersOverlay}>
            <Text style={styles.respondersCount}>
              {responders.length} Responder{responders.length !== 1 ? 's' : ''}
            </Text>
            <View style={styles.responderStatusDots}>
              {responders.slice(0, 5).map((responder, index) => (
                <View
                  key={index}
                  style={[
                    styles.responderDot,
                    { backgroundColor: getResponderStatusColor(responder.status) }
                  ]}
                />
              ))}
              {responders.length > 5 && (
                <Text style={styles.moreRespondersText}>+{responders.length - 5}</Text>
              )}
            </View>
          </View>
        )}
      </View>
      
      <View style={styles.mapOverlay}>
        <Ionicons name="expand" size={20} color="#fff" />
        <Text style={styles.expandText}>Tap to view details</Text>
      </View>
    </TouchableOpacity>
  );

  const renderFullScreenMap = () => (
    <Modal
      visible={mapModalVisible}
      animationType="slide"
      presentationStyle="fullScreen"
    >
      <View style={styles.fullScreenContainer}>
        <View style={styles.mapHeader}>
          <TouchableOpacity onPress={() => setMapModalVisible(false)} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={config.COLORS.text} />
          </TouchableOpacity>
          <Text style={styles.mapTitle}>{event?.title || 'Event Map'}</Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.fullScreenMapContent}>
          {/* Event Details */}
          <View style={styles.eventDetailsCard}>
            <View style={styles.eventHeader}>
              <View style={[styles.eventMarker, { backgroundColor: getEventPriorityColor(event?.priority) }]}>
                <Ionicons name="alert-circle" size={24} color="#fff" />
              </View>
              <View style={styles.eventInfo}>
                <Text style={styles.eventTitle}>{event?.title}</Text>
                <Text style={styles.eventPriority}>Priority: {event?.priority || 'Unknown'}</Text>
                {event?.latitude && event?.longitude && (
                  <Text style={styles.coordinatesText}>
                    Location: {parseFloat(event.latitude).toFixed(4)}, {parseFloat(event.longitude).toFixed(4)}
                  </Text>
                )}
              </View>
            </View>
            {event?.description && (
              <Text style={styles.eventDescription}>{event.description}</Text>
            )}
          </View>

          {/* Responders List */}
          {responders.length > 0 && (
            <View style={styles.respondersCard}>
              <Text style={styles.cardTitle}>Responders ({responders.length})</Text>
              {responders.map((responder, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.responderItem}
                  onPress={() => onResponderPress && onResponderPress(responder)}
                >
                  <View style={[styles.responderStatusIndicator, { backgroundColor: getResponderStatusColor(responder.status) }]} />
                  <View style={styles.responderInfo}>
                    <Text style={styles.responderName}>
                      {responder.first_name} {responder.last_name}
                    </Text>
                    <Text style={styles.responderRole}>{responder.job_role || responder.role}</Text>
                  </View>
                  <Text style={styles.responderStatus}>{responder.status}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}

          {/* Web Map Integration */}
          <View style={styles.webMapCard}>
            <Text style={styles.cardTitle}>Interactive Map</Text>
            <Text style={styles.webMapNote}>
              For full interactive map features, please use the mobile app or open in Google Maps.
            </Text>
            {event?.latitude && event?.longitude && (
              <TouchableOpacity
                style={styles.openMapsButton}
                onPress={() => {
                  const url = `https://www.google.com/maps?q=${event.latitude},${event.longitude}`;
                  window.open(url, '_blank');
                }}
              >
                <Ionicons name="map" size={20} color="#fff" />
                <Text style={styles.openMapsText}>Open in Google Maps</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Legend */}
        <View style={styles.legend}>
          <Text style={styles.legendTitle}>Status Legend</Text>
          <View style={styles.legendItem}>
            <View style={[styles.legendMarker, { backgroundColor: '#4caf50' }]} />
            <Text style={styles.legendText}>On Scene</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendMarker, { backgroundColor: '#ff9800' }]} />
            <Text style={styles.legendText}>En Route</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendMarker, { backgroundColor: '#9e9e9e' }]} />
            <Text style={styles.legendText}>Pending</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendMarker, { backgroundColor: '#f44336' }]} />
            <Text style={styles.legendText}>Unavailable</Text>
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <>
      {renderMiniMap()}
      {renderFullScreenMap()}
    </>
  );
};

const styles = StyleSheet.create({
  miniMapContainer: {
    height: 200,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: config.COLORS.surface,
  },
  webMapContainer: {
    flex: 1,
    position: 'relative',
  },
  mapPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: config.COLORS.background,
  },
  mapPlaceholderText: {
    fontSize: 16,
    fontWeight: '600',
    color: config.COLORS.text,
    marginTop: 8,
  },
  coordinatesText: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
    marginTop: 4,
  },
  eventInfoOverlay: {
    position: 'absolute',
    top: 12,
    left: 12,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 8,
    padding: 8,
  },
  eventMarker: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  eventTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: config.COLORS.text,
  },
  respondersOverlay: {
    position: 'absolute',
    bottom: 12,
    left: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 8,
    padding: 8,
  },
  respondersCount: {
    fontSize: 12,
    fontWeight: '600',
    color: config.COLORS.text,
    marginBottom: 4,
  },
  responderStatusDots: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  responderDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  moreRespondersText: {
    fontSize: 10,
    color: config.COLORS.textSecondary,
    marginLeft: 4,
  },
  mapOverlay: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 8,
    padding: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  expandText: {
    color: '#fff',
    fontSize: 12,
    marginLeft: 4,
  },
  fullScreenContainer: {
    flex: 1,
    backgroundColor: config.COLORS.background,
  },
  mapHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
    backgroundColor: config.COLORS.surface,
  },
  closeButton: {
    padding: 4,
  },
  mapTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: config.COLORS.text,
  },
  placeholder: {
    width: 32,
  },
  fullScreenMapContent: {
    flex: 1,
    padding: 16,
  },
  eventDetailsCard: {
    backgroundColor: config.COLORS.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  eventHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  eventInfo: {
    flex: 1,
    marginLeft: 12,
  },
  eventPriority: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    marginTop: 2,
  },
  eventDescription: {
    fontSize: 14,
    color: config.COLORS.text,
    lineHeight: 20,
  },
  respondersCard: {
    backgroundColor: config.COLORS.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: config.COLORS.text,
    marginBottom: 12,
  },
  responderItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
  },
  responderStatusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  responderInfo: {
    flex: 1,
  },
  responderName: {
    fontSize: 14,
    fontWeight: '500',
    color: config.COLORS.text,
  },
  responderRole: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
  },
  responderStatus: {
    fontSize: 12,
    fontWeight: '500',
    color: config.COLORS.textSecondary,
    textTransform: 'capitalize',
  },
  webMapCard: {
    backgroundColor: config.COLORS.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  webMapNote: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    marginBottom: 12,
    lineHeight: 20,
  },
  openMapsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: config.COLORS.primary,
    borderRadius: 8,
    padding: 12,
  },
  openMapsText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  legend: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    borderRadius: 8,
    padding: 12,
  },
  legendTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    color: config.COLORS.text,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  legendMarker: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendText: {
    fontSize: 12,
    color: config.COLORS.text,
  },
});

export default EventMapView;
