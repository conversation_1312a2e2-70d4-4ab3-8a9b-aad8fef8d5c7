# AlertComm Mobile App

A React Native emergency response mobile application for AlertComm Emergency Response System.

## 🚀 Features

### Core Functionality
- **Real-time Emergency Notifications** - Instant push notifications for new events
- **Event Management** - View, respond to, and track emergency events
- **Team Communication** - Real-time chat for event coordination
- **Status Updates** - Update and track responder status in real-time
- **Location Services** - Share location with team members
- **Cross-Platform** - Works on iOS, Android, and Web

### User Experience
- **Modern Design** - Clean, professional interface optimized for emergency use
- **Simple Navigation** - Easy-to-use bottom tab navigation
- **Offline Support** - Basic functionality works without internet
- **Fast Performance** - Optimized for quick response times
- **Accessibility** - Designed for use in high-stress situations

## 📱 Screens

1. **Login Screen** - Secure authentication for emergency personnel
2. **Dashboard** - Overview of active events and quick actions
3. **Events List** - Filter and view all emergency events
4. **Event Details** - Detailed view with team status and actions
5. **Chat** - Real-time communication for event coordination
6. **Profile** - User settings and app configuration

## 🛠️ Technology Stack

- **React Native** with Expo
- **Socket.IO** for real-time communication
- **AsyncStorage** for local data persistence
- **Expo Notifications** for push notifications
- **React Navigation** for navigation
- **Vector Icons** for UI elements

## 🔧 Setup Instructions

### Prerequisites
- Node.js (v14 or higher)
- Expo CLI (`npm install -g @expo/cli`)
- Backend server running on `http://localhost:3000`

### Installation

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start the development server:**
   ```bash
   # For web development
   npm run web
   
   # For mobile development
   npm start
   ```

3. **Run on specific platforms:**
   ```bash
   # iOS Simulator
   npm run ios
   
   # Android Emulator
   npm run android
   
   # Web Browser
   npm run web
   ```

## 🔐 Login Credentials

Use the same credentials as the web application:

- **Admin**: `admin` / `password123`
- **Commander**: `commander1` / `password123`
- **Medic**: `medic1` / `password123`
- **Staff**: `medic2` / `password123`

## 📡 API Integration

The app connects to the AlertComm backend server:

- **Base URL**: `http://localhost:3000` (development)
- **Socket URL**: `http://localhost:3000` (development)
- **Authentication**: JWT tokens stored in AsyncStorage
- **Real-time**: Socket.IO for live updates

## 🔔 Notifications

### Push Notifications
- **Emergency Alerts** - High priority notifications for urgent events
- **Event Assignments** - Notifications when assigned to events
- **Chat Messages** - Real-time message notifications
- **Status Updates** - Team member status changes

### Notification Channels
- **Emergency** - Critical alerts with sound and vibration
- **General** - Standard notifications for updates
- **Chat** - Message notifications

## 🗂️ Project Structure

```
src/
├── components/          # Reusable UI components
├── screens/            # Main app screens
│   ├── LoginScreen.js
│   ├── DashboardScreen.js
│   ├── EventsScreen.js
│   ├── EventDetailScreen.js
│   ├── ChatScreen.js
│   └── ProfileScreen.js
├── navigation/         # Navigation configuration
├── context/           # React Context providers
│   ├── AuthContext.js
│   └── NotificationContext.js
├── services/          # API and Socket services
│   ├── api.js
│   └── socket.js
├── config/           # App configuration
└── utils/           # Utility functions
```

## 🎨 Design System

### Colors
- **Primary**: `#1976d2` (Blue)
- **Secondary**: `#f44336` (Red)
- **Success**: `#4caf50` (Green)
- **Warning**: `#ff9800` (Orange)
- **Background**: `#f5f5f5` (Light Gray)
- **Surface**: `#ffffff` (White)

### Typography
- **Headers**: Bold, 18-24px
- **Body**: Regular, 14-16px
- **Captions**: 12px
- **Font**: System default (San Francisco/Roboto)

## 🚀 Deployment

### Web Deployment
```bash
npm run build:web
npm run serve
```

### Mobile App Store Deployment
```bash
# Build for iOS
expo build:ios

# Build for Android
expo build:android
```

## 🧪 Testing

### Manual Testing Checklist
- [ ] Login with valid credentials
- [ ] Receive push notifications for new events
- [ ] Update responder status
- [ ] View event details and team status
- [ ] Send and receive chat messages
- [ ] Navigate between all screens
- [ ] Test offline functionality
- [ ] Test on different screen sizes

### Test Scenarios
1. **Emergency Response Flow**
   - Login as responder
   - Receive emergency notification
   - Acknowledge event
   - Update status to "En Route"
   - Communicate via chat
   - Update status to "On Scene"

2. **Team Coordination**
   - Multiple users in same event
   - Real-time status updates
   - Chat communication
   - Event completion

## 📞 Support

For technical issues or questions:
1. Check the console logs for errors
2. Verify backend server is running
3. Ensure proper network connectivity
4. Check notification permissions

## 🔄 Updates

The app automatically checks for updates and can be updated through:
- **Web**: Refresh the browser
- **Mobile**: App store updates or OTA updates via Expo

---

**🚨 Emergency Response Ready! 🚨**

Your AlertComm mobile app is ready for emergency response operations!
