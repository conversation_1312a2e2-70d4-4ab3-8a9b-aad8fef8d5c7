# 🚀 AlertComm Emergency Response - Complete Deployment Guide

## 📋 Table of Contents
1. [Prerequisites](#prerequisites)
2. [Project Overview](#project-overview)
3. [Development Setup](#development-setup)
4. [Build Commands](#build-commands)
5. [iOS Deployment](#ios-deployment)
6. [Android Deployment](#android-deployment)
7. [Testing](#testing)
8. [Troubleshooting](#troubleshooting)

## 🔧 Prerequisites

### System Requirements
- **macOS**: Required for iOS builds
- **Node.js**: v18.0.0 or higher
- **npm**: v8.0.0 or higher
- **Git**: Latest version
- **Android Studio**: Latest version (for Android builds)
- **Xcode**: 15.0 or higher (for iOS builds)

### Required Accounts
- **Expo Account**: Free account at expo.dev
- **Apple Developer Account**: $99/year (for iOS App Store)
- **Google Play Console**: $25 one-time fee (for Android Play Store)

### Development Tools
```bash
# Install Expo CLI
npm install -g @expo/cli

# Install EAS CLI (for cloud builds)
npm install -g eas-cli

# Verify installations
expo --version
eas --version
```

## 📱 Project Overview

**AlertComm Emergency Response** is a React Native + Expo application featuring:
- **Framework**: Expo SDK 53 + React Native 0.79.5
- **Architecture**: Modern React Native with Context API
- **Features**: Emergency coordination, real-time chat, GPS tracking, push notifications
- **Platforms**: iOS, Android, Web
- **Size**: ~20MB production build

### Key Dependencies
- React Navigation 7.x
- Socket.IO Client
- Expo Location & Notifications
- React Native Maps
- JWT Authentication

## 🛠 Development Setup

### 1. Clone and Install
```bash
# Clone the repository
git clone <repository-url>
cd AlertCommApp

# Install dependencies
npm install

# Start development server
npm start
```

### 2. Environment Configuration
The app uses different configurations for development and production:
- **Development**: `http://localhost:3000`
- **Production**: `https://app.alertcomm1.com`

Configuration is handled automatically in `src/config/config.js`.

### 3. Test the App
```bash
# Run tests
npm test

# Start development server
npm start

# Test on different platforms
npm run web      # Web browser
npm run android  # Android emulator/device
npm run ios      # iOS simulator/device
```

## 🔨 Build Commands

### Quick Reference
```bash
# Development builds
npm start                    # Development server
npm run android             # Android development
npm run ios                 # iOS development
npm run web                 # Web development

# Production builds
npm run build:android       # Android production APK
npm run build:ios          # iOS production IPA
npm run build:web          # Web production build

# Preview builds (for testing)
npm run build:android-preview  # Android preview APK
npm run build:ios-preview     # iOS preview IPA

# Maintenance
npm run clean              # Clean node_modules
npm run clean:android      # Clean Android build cache
npm run prebuild          # Regenerate native code
```

### Build Profiles (eas.json)
The app uses three build profiles:
- **development**: For testing with development features
- **preview**: For internal testing and distribution
- **production**: For app store submission

## 📱 iOS Deployment

### Prerequisites
1. **Apple Developer Account** ($99/year)
2. **Xcode** 15.0 or higher
3. **iOS Simulator** or physical device

### Development Build
```bash
# Build for iOS simulator
eas build --platform ios --profile development

# Build for physical device (requires Apple Developer account)
eas build --platform ios --profile development --device
```

### App Store Submission
```bash
# 1. Build production version
eas build --platform ios --profile production

# 2. Submit to App Store Connect
eas submit --platform ios

# 3. Configure app in App Store Connect
# - App information
# - Pricing and availability
# - App Review information
# - Version information
```

### iOS Configuration Checklist
- [ ] Bundle identifier configured: `com.alertcomm.app`
- [ ] App icons (1024x1024 for App Store)
- [ ] Launch screen configured
- [ ] Privacy permissions (Location, Notifications)
- [ ] App Store metadata
- [ ] Screenshots for all device sizes

## 🤖 Android Deployment

### Prerequisites
1. **Android Studio** with SDK
2. **Google Play Console** account ($25 one-time)
3. **Android device** or emulator

### Development Build
```bash
# Build APK for testing
eas build --platform android --profile preview

# Local build (requires Android Studio setup)
npm run android
```

### Google Play Store Submission
```bash
# 1. Build production AAB (Android App Bundle)
eas build --platform android --profile production

# 2. Submit to Google Play Console
eas submit --platform android

# 3. Configure app in Google Play Console
# - App information
# - Content rating
# - Pricing and distribution
# - Release management
```

### Android Configuration Checklist
- [ ] Package name configured: `com.alertcomm.app`
- [ ] App icons (adaptive icon)
- [ ] Splash screen configured
- [ ] Permissions (Location, Notifications, Camera)
- [ ] Google Play metadata
- [ ] Screenshots for phones and tablets
- [ ] App signing key configured

## 🧪 Testing

### Automated Testing
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run linting
npm run lint
```

### Manual Testing Checklist
- [ ] Login/logout functionality
- [ ] Navigation between screens
- [ ] Real-time chat
- [ ] Push notifications
- [ ] Location services
- [ ] Emergency event creation
- [ ] Status updates
- [ ] Offline functionality

### Device Testing
1. **iOS**: Test on iPhone and iPad
2. **Android**: Test on different screen sizes
3. **Performance**: Test on older devices
4. **Network**: Test with poor connectivity

## 🔧 Troubleshooting

### Common Build Issues

**Metro bundler issues:**
```bash
npm start -- --reset-cache
```

**Android build failures:**
```bash
npm run clean:android
npm run prebuild
```

**iOS build failures:**
```bash
# Clean Xcode build folder
# Product → Clean Build Folder in Xcode
```

**Dependency conflicts:**
```bash
npm run clean
npm install
```

### Platform-Specific Issues

**iOS:**
- Ensure Xcode is updated
- Check iOS deployment target (iOS 13.0+)
- Verify Apple Developer account status

**Android:**
- Check Android SDK installation
- Verify NDK version (27.1.********)
- Ensure Java 17+ is installed

### Getting Help
1. Check Expo documentation: https://docs.expo.dev
2. React Native documentation: https://reactnative.dev
3. GitHub issues and discussions
4. Expo Discord community

---

## 🎯 Quick Start Summary

**For immediate testing:**
```bash
npm install
npm start
# Scan QR code with Expo Go app
```

**For production APK:**
```bash
eas login
eas build --platform android --profile preview
# Download APK from provided link
```

**For App Store submission:**
```bash
eas build --platform ios --profile production
eas submit --platform ios
```

---

**Built with ❤️ for Emergency Response Teams**
