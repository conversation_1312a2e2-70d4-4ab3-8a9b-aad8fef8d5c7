-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:1:1-37:12
MERGED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:1:1-37:12
INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml:1:1-7:12
MERGED from [:react-native-gesture-handler] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-maps] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-edge-to-edge] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-async-storage_async-storage] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-picker_picker] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-push-notification] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-push-notification/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-vector-icons] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-18:12
MERGED from [host.exp.exponent:expo.modules.font:13.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/c41c0a1db40479f33d891af06550e7a7/transformed/expo.modules.font-13.3.2/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:2:1-24:12
MERGED from [:expo-constants] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-constants/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.application:6.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/8f2d9bfa121af425c1d3ccaa486d65ff/transformed/expo.modules.application-6.1.5/AndroidManifest.xml:2:1-7:12
MERGED from [expo.modules.asset:expo.modules.asset:11.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/bdeef16b1312dc3dd41940111e1a6fb3/transformed/expo.modules.asset-11.1.7/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:2:1-33:12
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/a28908b56ec11751b59243b1f242702a/transformed/expo.modules.keepawake-14.1.4/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/c32eb368d89b8af8c6a684bc0b543217/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:2:1-17:12
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:2:1-43:12
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f41a0a0abe9082c2adadbc91827429e5/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:2:1-18:12
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:17:1-145:12
MERGED from [androidx.room:room-ktx:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/9aea9ef70537bb27590c482118101d15/transformed/room-ktx-2.5.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7e6af7a886ab03e93fbcefb0f4519c4/transformed/room-runtime-2.5.0/AndroidManifest.xml:17:1-31:12
MERGED from [com.google.android.gms:play-services-location:21.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/dc06cdfadb266d2a650b03f61a88d1c2/transformed/play-services-location-21.3.0/AndroidManifest.xml:2:1-8:12
MERGED from [com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/837a13fdb801afa1460e9b1aae21e199/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:16:1-35:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/067eb4768d0977cc253e099ab589c211/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/162c641a6ded6de9dcbbe71bb887fa9c/transformed/constraintlayout-2.0.1/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5fde982e3dd990d3004e359630a81b79/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/bac1e5d2115107526f6218fa7a1c9008/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ec838b99bd125d126bb54ff021f69a3/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/526bdb49f2fae114500606f865ffb519/transformed/firebase-installations-interop-17.1.1/AndroidManifest.xml:15:1-19:12
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce37a28011a1b60b4e3d132dbd8b1975/transformed/activity-1.8.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/0b3cd90608d731515ee5b0c8986ce7d6/transformed/activity-ktx-1.8.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/6d8784d24425b9166c30f1a4f5571e15/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/2ecacc9a77cadee2dd15a52b568a98f1/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/94f09ae075da36273dd5b0f48955e034/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/c3179e0f1b67cb33410fa674edb5496c/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/50fb524026069718687c4ab652c29053/transformed/autofill-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:animated-gif:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/0fd0e4fa611fd75b8ba4eda3c0af56e5/transformed/animated-gif-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/76328da2fdf1aca21629387770b9d62c/transformed/webpsupport-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/4585a14f1ae9a66565345740533a24e2/transformed/fresco-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/11cf39d0535535ef53e14603004c5e44/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f3cd27b890a8689bdecbf95693f6abf/transformed/animated-base-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/e843b317ee776dce67bc96b95fa8519e/transformed/animated-drawable-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/43cd96b77f258442464a756005d7068b/transformed/vito-options-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/90554224eff49d6253bc7f46090a200f/transformed/drawee-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/27d6ba0419c23ccfd28f0d65dba268b6/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/33b3f812d0a9954143308667fe9e3f2b/transformed/memory-type-native-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/a7b8bc68d236b8b6e7876128119c56df/transformed/memory-type-java-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/433663fad8d0a369f9eebadfaf8d9c89/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3c5f6b0c155786f68981012d876a0286/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/22dca7957a69d0d46a2b301d1f3405bd/transformed/imagepipeline-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/d87c01d0ffee9e478b5ed3aedb0a67be/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7e8ff34aa003f1e3d34ab42e7a02199e/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/42f63906eff2f4cf34b7774f8da390db/transformed/urimod-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/c02947a63af0bbe4d1d82d147698eaf8/transformed/vito-source-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/cfd2bc4667cb4cb0287aae0978df24ac/transformed/middleware-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3356ac95f0115ef5184f7de4471e60d4/transformed/ui-common-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/ad8c48db4554b56c12f5e68dfae98b63/transformed/soloader-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/971b53c391360c5dc5f7c648986b09b6/transformed/fbcore-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/c6517c74281c1909d506f689018aed8b/transformed/core-ktx-1.15.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/34bb67911f8414f1ff69475eb4175853/transformed/drawerlayout-1.1.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/925e579c481c06349ca2c1bdadbdec35/transformed/transition-1.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/74502918765ad8f81a97aa9e7ecb2fe7/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/8b13948c497b3cb25b633e3d026cff9f/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/69082f68bb02655e2ffff4dadfa40175/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/65ba2f9fa3c9fe83e5152e0f7195116c/transformed/viewpager2-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ec1853d0b0976e32a4f952e316c0c604/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/6183b82bc0c6a1834671e2c74c065f3b/transformed/media-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.13/transforms/8b7e65eaf17b3c451e235705be1d93a4/transformed/play-services-stats-17.0.2/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/fcba58dc1b88ff592a0c160f99cb9936/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/34564e277d995fd6c7ae4ce24f36e074/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce7fe74a7e1d15baec12eb1258cf58fd/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/2db4240268c509e9b190ca1a393ca7d5/transformed/recyclerview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7776c4e06fd334733f4fe348ed5c13e9/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/625e222979d00247a55c8d8eb69453bf/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b4c4dee6b60fa7565c562d06ae78c19e/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/98eec38dab2893f9dde1642e7a91ed70/transformed/lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/44911df0b7c9625b5575bd1446144add/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/042c063257c458b0d7190d8859d3193e/transformed/lifecycle-service-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/2af3bcc447ac110862396e7d0b0ec9ae/transformed/lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/0e45932910c15ea66169daadac963681/transformed/lifecycle-livedata-core-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/8fbfdc943d04e82c10add9c26833a5c6/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/fa08d204398219a1a89b242b31fa3041/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/5379c5771c0cc88a961c78af9bb09339/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/c4b770dc8989251f37e56402d8aa1685/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/19f8795450958262f34e41d8151462d1/transformed/savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/4de6ef82de9a5711ba602057b3db35de/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:2:1-5:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/2a59a1e417058628b047efcc6425fb8d/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/22d97c11c7e69f31bac4e14a1df1d05a/transformed/fragment-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/506e255793c8bac2342ad449777733c1/transformed/fragment-ktx-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/5e0f9e6bf46b1bd6aeea3c4a7ba42299/transformed/tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/05c532ea6eeed5ba96fff64378302997/transformed/tracing-ktx-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/6d955f79743302bcd3796d7d9def5561/transformed/sqlite-framework-2.3.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/e69ea34ae74c1f0868245de2a10f55c0/transformed/ui-core-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/e2997f661b1f9bbd1c1e0b3ba95ac331/transformed/sqlite-2.3.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/9f527975834136bd1e4e42d80a2ffd4f/transformed/vito-renderer-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/35675df85bd02bb3d2682c8049acc4ba/transformed/hermes-android-0.79.5-debug/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/16ea918983671f6172c82665df93c2bc/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5745f858b74ff0a2f8e17d4b26e19812/transformed/cardview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/0e6b28d36dd1dcea484117e191bbf338/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/0415a429693b075dc371ab9df50f3f05/transformed/firebase-components-18.0.0/AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/78a3c009a8dccc885176388c2c4753ad/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/29cada63d1249206df28cc4b9d1ad1e7/transformed/firebase-encoders-json-18.0.0/AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/b151e5f1ab3adac6b03830d50e953790/transformed/transport-api-3.1.0/AndroidManifest.xml:15:1-20:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/63bd8e0afbc574e96db736c0dfd2bf6a/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/839cc9c259c516d7f6f4a5b066ac62ea/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/497868229bdb3c0c11614fe7fa96dcc3/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/c6aadd448bec4e14e12d110512d17b3c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3b4cadc5d581274a383a17f34b96bacf/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.13/transforms/9478ca0554caeea9ae571964e0cb2ca6/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.multidex:multidex:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/468c9ad524409b76be9ce5bb433cf8e2/transformed/multidex-2.0.1/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:appcompat-v7:27.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/c0638c59f83f1f13f534c7e410769f22/transformed/appcompat-v7-27.1.1/AndroidManifest.xml:17:1-22:12
MERGED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:2:1-52:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:2:1-17:12
MERGED from [com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/e75f83e5e2bd4ad05880a4e7cced326b/transformed/installreferrer-2.2/AndroidManifest.xml:2:1-13:12
MERGED from [com.android.support:animated-vector-drawable:27.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/fa13d3c2760a45d9cf9171144125dd48/transformed/animated-vector-drawable-27.1.1/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-vector-drawable:27.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/77650deea5355580eadebf61bb22b2b7/transformed/support-vector-drawable-27.1.1/AndroidManifest.xml:17:1-22:12
	package
		INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:2:3-78
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/c32eb368d89b8af8c6a684bc0b543217/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:7:5-81
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/c32eb368d89b8af8c6a684bc0b543217/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:7:5-81
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:2:20-76
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:3:3-76
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/c32eb368d89b8af8c6a684bc0b543217/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:8:5-79
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/c32eb368d89b8af8c6a684bc0b543217/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:8:5-79
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:3:20-74
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:4:3-64
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:8:5-67
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:21:5-66
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:21:5-66
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ec838b99bd125d126bb54ff021f69a3/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ec838b99bd125d126bb54ff021f69a3/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:25:5-67
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:4:20-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:5:3-77
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:10:5-80
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:10:5-80
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:5:20-75
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:6:3-75
MERGED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:6:3-75
MERGED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:6:3-75
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:16:5-78
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:6:20-73
uses-permission#android.permission.VIBRATE
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:7:3-63
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:7:20-61
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:8:3-78
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:9:5-81
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:9:5-81
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:8:20-76
queries
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:9:3-15:13
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:12:5-18:15
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:12:5-18:15
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:27:5-30:15
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:27:5-30:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:10:5-14:14
action#android.intent.action.VIEW
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:11:7-58
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:11:15-56
category#android.intent.category.BROWSABLE
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:12:7-67
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:12:17-65
data
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:13:7-37
	android:scheme
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:13:13-35
application
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:3-36:17
MERGED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:3-36:17
MERGED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:3-36:17
INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml:6:5-162
MERGED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-16:19
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:18:5-22:19
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:20:5-31:19
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:20:5-31:19
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/c32eb368d89b8af8c6a684bc0b543217/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:10:5-15:19
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/c32eb368d89b8af8c6a684bc0b543217/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:10:5-15:19
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:10:5-41:19
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:10:5-41:19
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f41a0a0abe9082c2adadbc91827429e5/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f41a0a0abe9082c2adadbc91827429e5/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:8:5-16:19
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:28:5-143:19
MERGED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7e6af7a886ab03e93fbcefb0f4519c4/transformed/room-runtime-2.5.0/AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7e6af7a886ab03e93fbcefb0f4519c4/transformed/room-runtime-2.5.0/AndroidManifest.xml:23:5-29:19
MERGED from [com.google.android.gms:play-services-location:21.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/dc06cdfadb266d2a650b03f61a88d1c2/transformed/play-services-location-21.3.0/AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-location:21.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/dc06cdfadb266d2a650b03f61a88d1c2/transformed/play-services-location-21.3.0/AndroidManifest.xml:6:5-20
MERGED from [com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/837a13fdb801afa1460e9b1aae21e199/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:22:5-26:19
MERGED from [com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/837a13fdb801afa1460e9b1aae21e199/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:31:5-34:19
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:31:5-34:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/AndroidManifest.xml:4:5-6:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/162c641a6ded6de9dcbbe71bb887fa9c/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/162c641a6ded6de9dcbbe71bb887fa9c/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/bac1e5d2115107526f6218fa7a1c9008/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/bac1e5d2115107526f6218fa7a1c9008/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:7:5-8:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.13/transforms/8b7e65eaf17b3c451e235705be1d93a4/transformed/play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.13/transforms/8b7e65eaf17b3c451e235705be1d93a4/transformed/play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/4de6ef82de9a5711ba602057b3db35de/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/4de6ef82de9a5711ba602057b3db35de/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/2a59a1e417058628b047efcc6425fb8d/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/2a59a1e417058628b047efcc6425fb8d/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/AndroidManifest.xml:5:5-7:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/78a3c009a8dccc885176388c2c4753ad/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/78a3c009a8dccc885176388c2c4753ad/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:25:5-39:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/c6aadd448bec4e14e12d110512d17b3c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/c6aadd448bec4e14e12d110512d17b3c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:11:5-15:19
MERGED from [com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/e75f83e5e2bd4ad05880a4e7cced326b/transformed/installreferrer-2.2/AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/e75f83e5e2bd4ad05880a4e7cced326b/transformed/installreferrer-2.2/AndroidManifest.xml:11:5-20
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:221-247
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:221-247
	android:label
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:48-80
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:48-80
	tools:ignore
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml:6:75-114
	android:roundIcon
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:116-161
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:116-161
	tools:targetApi
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml:6:54-74
	android:icon
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:81-115
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:81-115
	android:allowBackup
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:162-188
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:162-188
	android:theme
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:189-220
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:189-220
	tools:replace
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml:6:115-159
	android:usesCleartextTraffic
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml:6:18-53
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:16-47
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:16-47
meta-data#com.google.firebase.messaging.default_notification_color
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:17:5-139
	android:resource
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:17:88-137
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:17:16-87
meta-data#com.google.firebase.messaging.default_notification_icon
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:18:5-135
	android:resource
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:18:87-133
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:18:16-86
meta-data#expo.modules.notifications.default_notification_color
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:19:5-136
	android:resource
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:19:85-134
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:19:16-84
meta-data#expo.modules.notifications.default_notification_icon
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:20:5-132
	android:resource
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:20:84-130
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:20:16-83
meta-data#expo.modules.updates.ENABLED
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:21:5-83
	android:value
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:21:60-81
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:21:16-59
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:22:5-105
	android:value
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:22:81-103
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:22:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:23:5-99
	android:value
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:23:80-97
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:23:16-79
activity#com.alertcomm.app.MainActivity
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:5-35:16
	android:screenOrientation
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:280-316
	android:launchMode
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:135-166
	android:windowSoftInputMode
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:167-209
	android:exported
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:256-279
	android:configChanges
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:44-134
	android:theme
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:210-255
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:25:7-28:23
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:26:9-60
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:26:17-58
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:27:9-68
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:27:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:exp+alertcomm-app
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:29:7-34:23
category#android.intent.category.DEFAULT
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:31:9-67
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:31:19-65
uses-sdk
INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml
MERGED from [:react-native-gesture-handler] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-picker_picker] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-picker_picker] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-push-notification] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-push-notification/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-push-notification] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-push-notification/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/c41c0a1db40479f33d891af06550e7a7/transformed/expo.modules.font-13.3.2/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/c41c0a1db40479f33d891af06550e7a7/transformed/expo.modules.font-13.3.2/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:10:5-44
MERGED from [:expo-constants] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-constants/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-constants/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.application:6.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/8f2d9bfa121af425c1d3ccaa486d65ff/transformed/expo.modules.application-6.1.5/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.application:6.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/8f2d9bfa121af425c1d3ccaa486d65ff/transformed/expo.modules.application-6.1.5/AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/bdeef16b1312dc3dd41940111e1a6fb3/transformed/expo.modules.asset-11.1.7/AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/bdeef16b1312dc3dd41940111e1a6fb3/transformed/expo.modules.asset-11.1.7/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/a28908b56ec11751b59243b1f242702a/transformed/expo.modules.keepawake-14.1.4/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/a28908b56ec11751b59243b1f242702a/transformed/expo.modules.keepawake-14.1.4/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/c32eb368d89b8af8c6a684bc0b543217/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/c32eb368d89b8af8c6a684bc0b543217/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f41a0a0abe9082c2adadbc91827429e5/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f41a0a0abe9082c2adadbc91827429e5/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/9aea9ef70537bb27590c482118101d15/transformed/room-ktx-2.5.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/9aea9ef70537bb27590c482118101d15/transformed/room-ktx-2.5.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7e6af7a886ab03e93fbcefb0f4519c4/transformed/room-runtime-2.5.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7e6af7a886ab03e93fbcefb0f4519c4/transformed/room-runtime-2.5.0/AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/dc06cdfadb266d2a650b03f61a88d1c2/transformed/play-services-location-21.3.0/AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/dc06cdfadb266d2a650b03f61a88d1c2/transformed/play-services-location-21.3.0/AndroidManifest.xml:4:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/837a13fdb801afa1460e9b1aae21e199/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/837a13fdb801afa1460e9b1aae21e199/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:26:5-43
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:26:5-43
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/AndroidManifest.xml:3:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/067eb4768d0977cc253e099ab589c211/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/067eb4768d0977cc253e099ab589c211/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/162c641a6ded6de9dcbbe71bb887fa9c/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/162c641a6ded6de9dcbbe71bb887fa9c/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5fde982e3dd990d3004e359630a81b79/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5fde982e3dd990d3004e359630a81b79/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/bac1e5d2115107526f6218fa7a1c9008/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/bac1e5d2115107526f6218fa7a1c9008/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ec838b99bd125d126bb54ff021f69a3/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ec838b99bd125d126bb54ff021f69a3/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/526bdb49f2fae114500606f865ffb519/transformed/firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/526bdb49f2fae114500606f865ffb519/transformed/firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce37a28011a1b60b4e3d132dbd8b1975/transformed/activity-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce37a28011a1b60b4e3d132dbd8b1975/transformed/activity-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/0b3cd90608d731515ee5b0c8986ce7d6/transformed/activity-ktx-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/0b3cd90608d731515ee5b0c8986ce7d6/transformed/activity-ktx-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/6d8784d24425b9166c30f1a4f5571e15/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/6d8784d24425b9166c30f1a4f5571e15/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/2ecacc9a77cadee2dd15a52b568a98f1/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/2ecacc9a77cadee2dd15a52b568a98f1/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/94f09ae075da36273dd5b0f48955e034/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/94f09ae075da36273dd5b0f48955e034/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/c3179e0f1b67cb33410fa674edb5496c/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/c3179e0f1b67cb33410fa674edb5496c/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/50fb524026069718687c4ab652c29053/transformed/autofill-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/50fb524026069718687c4ab652c29053/transformed/autofill-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:animated-gif:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/0fd0e4fa611fd75b8ba4eda3c0af56e5/transformed/animated-gif-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/0fd0e4fa611fd75b8ba4eda3c0af56e5/transformed/animated-gif-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/76328da2fdf1aca21629387770b9d62c/transformed/webpsupport-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/76328da2fdf1aca21629387770b9d62c/transformed/webpsupport-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/4585a14f1ae9a66565345740533a24e2/transformed/fresco-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/4585a14f1ae9a66565345740533a24e2/transformed/fresco-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/11cf39d0535535ef53e14603004c5e44/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/11cf39d0535535ef53e14603004c5e44/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f3cd27b890a8689bdecbf95693f6abf/transformed/animated-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f3cd27b890a8689bdecbf95693f6abf/transformed/animated-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/e843b317ee776dce67bc96b95fa8519e/transformed/animated-drawable-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/e843b317ee776dce67bc96b95fa8519e/transformed/animated-drawable-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/43cd96b77f258442464a756005d7068b/transformed/vito-options-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/43cd96b77f258442464a756005d7068b/transformed/vito-options-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/90554224eff49d6253bc7f46090a200f/transformed/drawee-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/90554224eff49d6253bc7f46090a200f/transformed/drawee-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/27d6ba0419c23ccfd28f0d65dba268b6/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/27d6ba0419c23ccfd28f0d65dba268b6/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/33b3f812d0a9954143308667fe9e3f2b/transformed/memory-type-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/33b3f812d0a9954143308667fe9e3f2b/transformed/memory-type-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/a7b8bc68d236b8b6e7876128119c56df/transformed/memory-type-java-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/a7b8bc68d236b8b6e7876128119c56df/transformed/memory-type-java-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/433663fad8d0a369f9eebadfaf8d9c89/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/433663fad8d0a369f9eebadfaf8d9c89/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3c5f6b0c155786f68981012d876a0286/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3c5f6b0c155786f68981012d876a0286/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/22dca7957a69d0d46a2b301d1f3405bd/transformed/imagepipeline-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/22dca7957a69d0d46a2b301d1f3405bd/transformed/imagepipeline-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/d87c01d0ffee9e478b5ed3aedb0a67be/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/d87c01d0ffee9e478b5ed3aedb0a67be/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7e8ff34aa003f1e3d34ab42e7a02199e/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7e8ff34aa003f1e3d34ab42e7a02199e/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/42f63906eff2f4cf34b7774f8da390db/transformed/urimod-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/42f63906eff2f4cf34b7774f8da390db/transformed/urimod-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/c02947a63af0bbe4d1d82d147698eaf8/transformed/vito-source-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/c02947a63af0bbe4d1d82d147698eaf8/transformed/vito-source-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/cfd2bc4667cb4cb0287aae0978df24ac/transformed/middleware-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/cfd2bc4667cb4cb0287aae0978df24ac/transformed/middleware-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3356ac95f0115ef5184f7de4471e60d4/transformed/ui-common-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3356ac95f0115ef5184f7de4471e60d4/transformed/ui-common-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/ad8c48db4554b56c12f5e68dfae98b63/transformed/soloader-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/ad8c48db4554b56c12f5e68dfae98b63/transformed/soloader-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/971b53c391360c5dc5f7c648986b09b6/transformed/fbcore-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/971b53c391360c5dc5f7c648986b09b6/transformed/fbcore-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/c6517c74281c1909d506f689018aed8b/transformed/core-ktx-1.15.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/c6517c74281c1909d506f689018aed8b/transformed/core-ktx-1.15.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/34bb67911f8414f1ff69475eb4175853/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/34bb67911f8414f1ff69475eb4175853/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/925e579c481c06349ca2c1bdadbdec35/transformed/transition-1.5.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/925e579c481c06349ca2c1bdadbdec35/transformed/transition-1.5.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/74502918765ad8f81a97aa9e7ecb2fe7/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/74502918765ad8f81a97aa9e7ecb2fe7/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/8b13948c497b3cb25b633e3d026cff9f/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/8b13948c497b3cb25b633e3d026cff9f/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/69082f68bb02655e2ffff4dadfa40175/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/69082f68bb02655e2ffff4dadfa40175/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/65ba2f9fa3c9fe83e5152e0f7195116c/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/65ba2f9fa3c9fe83e5152e0f7195116c/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ec1853d0b0976e32a4f952e316c0c604/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/ec1853d0b0976e32a4f952e316c0c604/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/6183b82bc0c6a1834671e2c74c065f3b/transformed/media-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/6183b82bc0c6a1834671e2c74c065f3b/transformed/media-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.13/transforms/8b7e65eaf17b3c451e235705be1d93a4/transformed/play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.13/transforms/8b7e65eaf17b3c451e235705be1d93a4/transformed/play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/fcba58dc1b88ff592a0c160f99cb9936/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/fcba58dc1b88ff592a0c160f99cb9936/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/34564e277d995fd6c7ae4ce24f36e074/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/34564e277d995fd6c7ae4ce24f36e074/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce7fe74a7e1d15baec12eb1258cf58fd/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce7fe74a7e1d15baec12eb1258cf58fd/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/2db4240268c509e9b190ca1a393ca7d5/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/2db4240268c509e9b190ca1a393ca7d5/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7776c4e06fd334733f4fe348ed5c13e9/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7776c4e06fd334733f4fe348ed5c13e9/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/625e222979d00247a55c8d8eb69453bf/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/625e222979d00247a55c8d8eb69453bf/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b4c4dee6b60fa7565c562d06ae78c19e/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b4c4dee6b60fa7565c562d06ae78c19e/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/98eec38dab2893f9dde1642e7a91ed70/transformed/lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/98eec38dab2893f9dde1642e7a91ed70/transformed/lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/44911df0b7c9625b5575bd1446144add/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/44911df0b7c9625b5575bd1446144add/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/042c063257c458b0d7190d8859d3193e/transformed/lifecycle-service-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/042c063257c458b0d7190d8859d3193e/transformed/lifecycle-service-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/2af3bcc447ac110862396e7d0b0ec9ae/transformed/lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/2af3bcc447ac110862396e7d0b0ec9ae/transformed/lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/0e45932910c15ea66169daadac963681/transformed/lifecycle-livedata-core-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/0e45932910c15ea66169daadac963681/transformed/lifecycle-livedata-core-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/8fbfdc943d04e82c10add9c26833a5c6/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/8fbfdc943d04e82c10add9c26833a5c6/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/fa08d204398219a1a89b242b31fa3041/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/fa08d204398219a1a89b242b31fa3041/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/5379c5771c0cc88a961c78af9bb09339/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/5379c5771c0cc88a961c78af9bb09339/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/c4b770dc8989251f37e56402d8aa1685/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/c4b770dc8989251f37e56402d8aa1685/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/19f8795450958262f34e41d8151462d1/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/19f8795450958262f34e41d8151462d1/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/4de6ef82de9a5711ba602057b3db35de/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/4de6ef82de9a5711ba602057b3db35de/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/2a59a1e417058628b047efcc6425fb8d/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/2a59a1e417058628b047efcc6425fb8d/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/22d97c11c7e69f31bac4e14a1df1d05a/transformed/fragment-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/22d97c11c7e69f31bac4e14a1df1d05a/transformed/fragment-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/506e255793c8bac2342ad449777733c1/transformed/fragment-ktx-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/506e255793c8bac2342ad449777733c1/transformed/fragment-ktx-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/5e0f9e6bf46b1bd6aeea3c4a7ba42299/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/5e0f9e6bf46b1bd6aeea3c4a7ba42299/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/05c532ea6eeed5ba96fff64378302997/transformed/tracing-ktx-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/05c532ea6eeed5ba96fff64378302997/transformed/tracing-ktx-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/6d955f79743302bcd3796d7d9def5561/transformed/sqlite-framework-2.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/6d955f79743302bcd3796d7d9def5561/transformed/sqlite-framework-2.3.0/AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/e69ea34ae74c1f0868245de2a10f55c0/transformed/ui-core-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/e69ea34ae74c1f0868245de2a10f55c0/transformed/ui-core-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/e2997f661b1f9bbd1c1e0b3ba95ac331/transformed/sqlite-2.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/e2997f661b1f9bbd1c1e0b3ba95ac331/transformed/sqlite-2.3.0/AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/9f527975834136bd1e4e42d80a2ffd4f/transformed/vito-renderer-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/9f527975834136bd1e4e42d80a2ffd4f/transformed/vito-renderer-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/35675df85bd02bb3d2682c8049acc4ba/transformed/hermes-android-0.79.5-debug/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/35675df85bd02bb3d2682c8049acc4ba/transformed/hermes-android-0.79.5-debug/AndroidManifest.xml:5:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/16ea918983671f6172c82665df93c2bc/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/16ea918983671f6172c82665df93c2bc/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5745f858b74ff0a2f8e17d4b26e19812/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5745f858b74ff0a2f8e17d4b26e19812/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/0e6b28d36dd1dcea484117e191bbf338/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/0e6b28d36dd1dcea484117e191bbf338/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/0415a429693b075dc371ab9df50f3f05/transformed/firebase-components-18.0.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/0415a429693b075dc371ab9df50f3f05/transformed/firebase-components-18.0.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/78a3c009a8dccc885176388c2c4753ad/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/78a3c009a8dccc885176388c2c4753ad/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/29cada63d1249206df28cc4b9d1ad1e7/transformed/firebase-encoders-json-18.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/29cada63d1249206df28cc4b9d1ad1e7/transformed/firebase-encoders-json-18.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/b151e5f1ab3adac6b03830d50e953790/transformed/transport-api-3.1.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/b151e5f1ab3adac6b03830d50e953790/transformed/transport-api-3.1.0/AndroidManifest.xml:18:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/63bd8e0afbc574e96db736c0dfd2bf6a/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/63bd8e0afbc574e96db736c0dfd2bf6a/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/839cc9c259c516d7f6f4a5b066ac62ea/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/839cc9c259c516d7f6f4a5b066ac62ea/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/497868229bdb3c0c11614fe7fa96dcc3/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/497868229bdb3c0c11614fe7fa96dcc3/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/c6aadd448bec4e14e12d110512d17b3c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/c6aadd448bec4e14e12d110512d17b3c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3b4cadc5d581274a383a17f34b96bacf/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3b4cadc5d581274a383a17f34b96bacf/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.13/transforms/9478ca0554caeea9ae571964e0cb2ca6/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.13/transforms/9478ca0554caeea9ae571964e0cb2ca6/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.multidex:multidex:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/468c9ad524409b76be9ce5bb433cf8e2/transformed/multidex-2.0.1/AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/468c9ad524409b76be9ce5bb433cf8e2/transformed/multidex-2.0.1/AndroidManifest.xml:20:5-43
MERGED from [com.android.support:appcompat-v7:27.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/c0638c59f83f1f13f534c7e410769f22/transformed/appcompat-v7-27.1.1/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:appcompat-v7:27.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/c0638c59f83f1f13f534c7e410769f22/transformed/appcompat-v7-27.1.1/AndroidManifest.xml:20:5-44
MERGED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:7:5-9:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:7:5-9:41
MERGED from [com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/e75f83e5e2bd4ad05880a4e7cced326b/transformed/installreferrer-2.2/AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/e75f83e5e2bd4ad05880a4e7cced326b/transformed/installreferrer-2.2/AndroidManifest.xml:5:5-7:41
MERGED from [com.android.support:animated-vector-drawable:27.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/fa13d3c2760a45d9cf9171144125dd48/transformed/animated-vector-drawable-27.1.1/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:animated-vector-drawable:27.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/fa13d3c2760a45d9cf9171144125dd48/transformed/animated-vector-drawable-27.1.1/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-vector-drawable:27.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/77650deea5355580eadebf61bb22b2b7/transformed/support-vector-drawable-27.1.1/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-vector-drawable:27.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/77650deea5355580eadebf61bb22b2b7/transformed/support-vector-drawable-27.1.1/AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-57
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:20:13-77
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:16:13-79
	android:name
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:26:13-48
	android:name
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:22:13-74
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:27:13-29:70
	android:resource
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:29:17-67
	android:name
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:28:17-67
service#expo.modules.location.services.LocationTaskService
ADDED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/c32eb368d89b8af8c6a684bc0b543217/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:11:9-14:56
	android:exported
		ADDED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/c32eb368d89b8af8c6a684bc0b543217/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:13:13-37
	android:foregroundServiceType
		ADDED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/c32eb368d89b8af8c6a684bc0b543217/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:14:13-53
	android:name
		ADDED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/c32eb368d89b8af8c6a684bc0b543217/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:12:13-78
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:7:5-81
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:25:5-81
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:7:22-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:8:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:23:5-77
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:8:22-74
service#expo.modules.notifications.service.ExpoFirebaseMessagingService
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:11:9-17:19
	android:exported
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:13:13-37
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:12:13-91
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:14:13-16:29
	android:priority
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:14:28-49
action#com.google.firebase.MESSAGING_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:15:17-78
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:15:25-75
receiver#expo.modules.notifications.service.NotificationsService
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:19:9-31:20
	android:enabled
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:21:13-35
	android:exported
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:22:13-37
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:20:13-83
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.QUICKBOOT_POWERON+action:name:android.intent.action.REBOOT+action:name:com.htc.intent.action.QUICKBOOT_POWERON+action:name:expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:23:13-30:29
	android:priority
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:23:28-49
action#expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:24:17-88
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:24:25-85
action#android.intent.action.BOOT_COMPLETED
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:25:17-79
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:25:25-76
action#android.intent.action.REBOOT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:26:17-71
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:26:25-68
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:27:17-82
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:27:25-79
action#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:28:17-82
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:28:25-79
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:29:17-84
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:29:25-81
activity#expo.modules.notifications.service.NotificationForwarderActivity
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:33:9-40:75
	android:excludeFromRecents
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:35:13-46
	android:launchMode
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:37:13-42
	android:noHistory
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:38:13-37
	android:exported
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:36:13-37
	android:theme
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:40:13-72
	android:taskAffinity
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:39:13-36
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:34:13-92
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:20:5-78
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:20:5-78
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ec838b99bd125d126bb54ff021f69a3/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ec838b99bd125d126bb54ff021f69a3/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:22:5-79
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:22:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:24:5-68
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:23:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ec838b99bd125d126bb54ff021f69a3/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ec838b99bd125d126bb54ff021f69a3/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:9:5-68
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:24:22-65
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ec838b99bd125d126bb54ff021f69a3/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ec838b99bd125d126bb54ff021f69a3/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:47:13-82
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f41a0a0abe9082c2adadbc91827429e5/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f41a0a0abe9082c2adadbc91827429e5/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/78a3c009a8dccc885176388c2c4753ad/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/78a3c009a8dccc885176388c2c4753ad/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f41a0a0abe9082c2adadbc91827429e5/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f41a0a0abe9082c2adadbc91827429e5/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f41a0a0abe9082c2adadbc91827429e5/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:26:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:26:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:140:25-85
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7e6af7a886ab03e93fbcefb0f4519c4/transformed/room-runtime-2.5.0/AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7e6af7a886ab03e93fbcefb0f4519c4/transformed/room-runtime-2.5.0/AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7e6af7a886ab03e93fbcefb0f4519c4/transformed/room-runtime-2.5.0/AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7e6af7a886ab03e93fbcefb0f4519c4/transformed/room-runtime-2.5.0/AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7e6af7a886ab03e93fbcefb0f4519c4/transformed/room-runtime-2.5.0/AndroidManifest.xml:25:13-74
meta-data#com.google.android.gms.version
ADDED from [com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/837a13fdb801afa1460e9b1aae21e199/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:23:9-25:69
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:9-122
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/837a13fdb801afa1460e9b1aae21e199/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:25:13-66
	android:name
		ADDED from [com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/837a13fdb801afa1460e9b1aae21e199/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:24:13-58
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:22:5-24:33
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:23:8-40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:24:8-31
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:29:7-61
	android:name
		ADDED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:29:16-59
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:33:7-86
	android:required
		ADDED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:33:59-83
	android:name
		ADDED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:33:21-58
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:19-85
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:36:17-109
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:23:9-81
permission#com.alertcomm.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:26:22-94
uses-permission#com.alertcomm.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/78a3c009a8dccc885176388c2c4753ad/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/78a3c009a8dccc885176388c2c4753ad/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/78a3c009a8dccc885176388c2c4753ad/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:33:13-132
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:19:5-86
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:19:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:20:5-87
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:20:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:23:5-81
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:23:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:24:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:24:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:27:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:27:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:28:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:28:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:31:5-84
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:31:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:34:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:34:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:37:5-91
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:37:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:38:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:38:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:39:5-93
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:39:22-90
uses-permission#android.permission.READ_APP_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:42:5-73
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:42:22-70
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:45:5-82
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:45:22-79
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:46:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:46:22-80
uses-permission#me.everything.badger.permission.BADGE_COUNT_READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:49:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:49:22-85
uses-permission#me.everything.badger.permission.BADGE_COUNT_WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:50:5-89
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:50:22-86
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/e75f83e5e2bd4ad05880a4e7cced326b/transformed/installreferrer-2.2/AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/e75f83e5e2bd4ad05880a4e7cced326b/transformed/installreferrer-2.2/AndroidManifest.xml:9:22-107
