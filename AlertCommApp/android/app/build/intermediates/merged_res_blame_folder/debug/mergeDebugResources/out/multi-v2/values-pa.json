{"logs": [{"outputFile": "com.alertcomm.app-mergeDebugResources-56:/values-pa/values-pa.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/res/values-pa/values-pa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,340,419,500,599,688,796,908,991,1047,1111,1203,1272,1331,1416,1479,1541,1599,1663,1724,1778,1892,1950,2010,2064,2134,2261,2342,2432,2531,2628,2707,2842,2918,2995,3124,3208,3290,3345,3400,3466,3535,3612,3683,3762,3830,3906,3976,4041,4143,4238,4311,4405,4498,4572,4641,4735,4791,4874,4941,5025,5113,5175,5239,5302,5369,5466,5572,5663,5765,5824,5883,5960,6045,6121", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,78,80,98,88,107,111,82,55,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,81,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76,84,75,72", "endOffsets": "258,335,414,495,594,683,791,903,986,1042,1106,1198,1267,1326,1411,1474,1536,1594,1658,1719,1773,1887,1945,2005,2059,2129,2256,2337,2427,2526,2623,2702,2837,2913,2990,3119,3203,3285,3340,3395,3461,3530,3607,3678,3757,3825,3901,3971,4036,4138,4233,4306,4400,4493,4567,4636,4730,4786,4869,4936,5020,5108,5170,5234,5297,5364,5461,5567,5658,5760,5819,5878,5955,6040,6116,6189"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,68,69,70,72,75,77,78,79,80,81,82,83,84,85,86,87,88,89,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,144,145,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3046,3123,3202,3283,3382,4201,4309,4421,6846,6902,6966,7129,7347,7473,7558,7621,7683,7741,7805,7866,7920,8034,8092,8152,8206,8276,8620,8701,8791,8890,8987,9066,9201,9277,9354,9483,9567,9649,9704,9759,9825,9894,9971,10042,10121,10189,10265,10335,10400,10502,10597,10670,10764,10857,10931,11000,11094,11150,11233,11300,11384,11472,11534,11598,11661,11728,11825,11931,12022,12124,12183,12242,12707,12792,12868", "endLines": "5,34,35,36,37,38,46,47,48,68,69,70,72,75,77,78,79,80,81,82,83,84,85,86,87,88,89,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,144,145,146", "endColumns": "12,76,78,80,98,88,107,111,82,55,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,81,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76,84,75,72", "endOffsets": "308,3118,3197,3278,3377,3466,4304,4416,4499,6897,6961,7053,7193,7401,7553,7616,7678,7736,7800,7861,7915,8029,8087,8147,8201,8271,8398,8696,8786,8885,8982,9061,9196,9272,9349,9478,9562,9644,9699,9754,9820,9889,9966,10037,10116,10184,10260,10330,10395,10497,10592,10665,10759,10852,10926,10995,11089,11145,11228,11295,11379,11467,11529,11593,11656,11723,11820,11926,12017,12119,12178,12237,12314,12787,12863,12936"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/res/values-pa/values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,416,513,618,704,804,917,995,1072,1163,1256,1350,1444,1544,1637,1732,1826,1917,2008,2087,2197,2300,2396,2507,2609,2719,2878,12627", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "411,508,613,699,799,912,990,1067,1158,1251,1345,1439,1539,1632,1727,1821,1912,2003,2082,2192,2295,2391,2502,2604,2714,2873,2970,12702"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/res/values-pa/values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,203,274,343,423,490,557,631,707,790,869,937,1015,1098,1172,1256,1344,1419,1490,1561,1647,1716,1790,1859", "endColumns": "70,76,70,68,79,66,66,73,75,82,78,67,77,82,73,83,87,74,70,70,85,68,73,68,72", "endOffsets": "121,198,269,338,418,485,552,626,702,785,864,932,1010,1093,1167,1251,1339,1414,1485,1556,1642,1711,1785,1854,1927"}, "to": {"startLines": "33,49,71,73,74,76,90,91,92,139,140,141,142,147,148,149,150,151,152,153,154,156,157,158,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2975,4504,7058,7198,7267,7406,8403,8470,8544,12319,12402,12481,12549,12941,13024,13098,13182,13270,13345,13416,13487,13674,13743,13817,13886", "endColumns": "70,76,70,68,79,66,66,73,75,82,78,67,77,82,73,83,87,74,70,70,85,68,73,68,72", "endOffsets": "3041,4576,7124,7262,7342,7468,8465,8539,8615,12397,12476,12544,12622,13019,13093,13177,13265,13340,13411,13482,13568,13738,13812,13881,13954"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/res/values-pa/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5592", "endColumns": "150", "endOffsets": "5738"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/res/values-pa/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,591,696,839,964,1073,1172,1330,1435,1604,1732,1881,2038,2099,2161", "endColumns": "102,168,125,104,142,124,108,98,157,104,168,127,148,156,60,61,77", "endOffsets": "295,464,590,695,838,963,1072,1171,1329,1434,1603,1731,1880,2037,2098,2160,2238"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4581,4688,4861,4991,5100,5247,5376,5489,5743,5905,6014,6187,6319,6472,6633,6698,6764", "endColumns": "106,172,129,108,146,128,112,102,161,108,172,131,152,160,64,65,81", "endOffsets": "4683,4856,4986,5095,5242,5371,5484,5587,5900,6009,6182,6314,6467,6628,6693,6759,6841"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/res/values-pa/values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,558,656,785", "endColumns": "97,101,99,100,101,97,128,100", "endOffsets": "148,250,350,451,553,651,780,881"}, "to": {"startLines": "39,40,41,42,43,44,45,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3471,3569,3671,3771,3872,3974,4072,13573", "endColumns": "97,101,99,100,101,97,128,100", "endOffsets": "3564,3666,3766,3867,3969,4067,4196,13669"}}]}]}