{"logs": [{"outputFile": "com.alertcomm.app-mergeDebugResources-56:/values-km/values-km.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/res/values-km/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "144", "endOffsets": "339"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5567", "endColumns": "148", "endOffsets": "5711"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/res/values-km/values-km.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,347,423,503,582,661,761,873,953,1019,1084,1178,1248,1310,1397,1460,1525,1584,1649,1710,1767,1886,1944,2005,2062,2133,2263,2349,2425,2510,2592,2670,2808,2883,2954,3104,3201,3279,3334,3390,3456,3536,3626,3697,3782,3861,3938,4008,4083,4195,4283,4356,4456,4555,4629,4705,4812,4866,4956,5029,5120,5216,5278,5342,5405,5476,5575,5673,5765,5861,5919,5979,6062,6144,6222", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,79,78,78,99,111,79,65,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,75,84,81,77,137,74,70,149,96,77,54,55,65,79,89,70,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,97,91,95,57,59,82,81,77,75", "endOffsets": "264,342,418,498,577,656,756,868,948,1014,1079,1173,1243,1305,1392,1455,1520,1579,1644,1705,1762,1881,1939,2000,2057,2128,2258,2344,2420,2505,2587,2665,2803,2878,2949,3099,3196,3274,3329,3385,3451,3531,3621,3692,3777,3856,3933,4003,4078,4190,4278,4351,4451,4550,4624,4700,4807,4861,4951,5024,5115,5211,5273,5337,5400,5471,5570,5668,5760,5856,5914,5974,6057,6139,6217,6293"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,68,69,70,72,75,77,78,79,80,81,82,83,84,85,86,87,88,89,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,144,145,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3080,3158,3234,3314,3393,4193,4293,4405,6789,6855,6920,7086,7310,7438,7525,7588,7653,7712,7777,7838,7895,8014,8072,8133,8190,8261,8613,8699,8775,8860,8942,9020,9158,9233,9304,9454,9551,9629,9684,9740,9806,9886,9976,10047,10132,10211,10288,10358,10433,10545,10633,10706,10806,10905,10979,11055,11162,11216,11306,11379,11470,11566,11628,11692,11755,11826,11925,12023,12115,12211,12269,12329,12807,12889,12967", "endLines": "5,34,35,36,37,38,46,47,48,68,69,70,72,75,77,78,79,80,81,82,83,84,85,86,87,88,89,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,144,145,146", "endColumns": "12,77,75,79,78,78,99,111,79,65,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,75,84,81,77,137,74,70,149,96,77,54,55,65,79,89,70,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,97,91,95,57,59,82,81,77,75", "endOffsets": "314,3153,3229,3309,3388,3467,4288,4400,4480,6850,6915,7009,7151,7367,7520,7583,7648,7707,7772,7833,7890,8009,8067,8128,8185,8256,8386,8694,8770,8855,8937,9015,9153,9228,9299,9449,9546,9624,9679,9735,9801,9881,9971,10042,10127,10206,10283,10353,10428,10540,10628,10701,10801,10900,10974,11050,11157,11211,11301,11374,11465,11561,11623,11687,11750,11821,11920,12018,12110,12206,12264,12324,12407,12884,12962,13038"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/res/values-km/values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,421,520,630,717,820,941,1019,1095,1186,1279,1371,1465,1565,1658,1753,1847,1938,2029,2112,2216,2320,2420,2529,2638,2747,2909,12723", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "416,515,625,712,815,936,1014,1090,1181,1274,1366,1460,1560,1653,1748,1842,1933,2024,2107,2211,2315,2415,2524,2633,2742,2904,3002,12802"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/res/values-km/values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,211,283,353,437,503,572,648,725,808,888,959,1036,1118,1195,1278,1360,1436,1507,1577,1670,1749,1823,1902", "endColumns": "72,82,71,69,83,65,68,75,76,82,79,70,76,81,76,82,81,75,70,69,92,78,73,78,76", "endOffsets": "123,206,278,348,432,498,567,643,720,803,883,954,1031,1113,1190,1273,1355,1431,1502,1572,1665,1744,1818,1897,1974"}, "to": {"startLines": "33,49,71,73,74,76,90,91,92,139,140,141,142,147,148,149,150,151,152,153,154,156,157,158,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3007,4485,7014,7156,7226,7372,8391,8460,8536,12412,12495,12575,12646,13043,13125,13202,13285,13367,13443,13514,13584,13778,13857,13931,14010", "endColumns": "72,82,71,69,83,65,68,75,76,82,79,70,76,81,76,82,81,75,70,69,92,78,73,78,76", "endOffsets": "3075,4563,7081,7221,7305,7433,8455,8531,8608,12490,12570,12641,12718,13120,13197,13280,13362,13438,13509,13579,13672,13852,13926,14005,14082"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/res/values-km/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,442,559,660,818,938,1055,1160,1314,1427,1594,1715,1856,2010,2070,2124", "endColumns": "97,150,116,100,157,119,116,104,153,112,166,120,140,153,59,53,72", "endOffsets": "290,441,558,659,817,937,1054,1159,1313,1426,1593,1714,1855,2009,2069,2123,2196"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4568,4670,4825,4946,5051,5213,5337,5458,5716,5874,5991,6162,6287,6432,6590,6654,6712", "endColumns": "101,154,120,104,161,123,120,108,157,116,170,124,144,157,63,57,76", "endOffsets": "4665,4820,4941,5046,5208,5332,5453,5562,5869,5986,6157,6282,6427,6585,6649,6707,6784"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/res/values-km/values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "39,40,41,42,43,44,45,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3472,3567,3670,3768,3868,3969,4081,13677", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "3562,3665,3763,3863,3964,4076,4188,13773"}}]}]}