{"logs": [{"outputFile": "com.alertcomm.app-mergeDebugResources-56:/values-fi/values-fi.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/res/values-fi/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,449,576,678,817,939,1051,1154,1291,1393,1538,1660,1804,1939,2001,2067", "endColumns": "106,148,126,101,138,121,111,102,136,101,144,121,143,134,61,65,78", "endOffsets": "299,448,575,677,816,938,1050,1153,1290,1392,1537,1659,1803,1938,2000,2066,2145"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4567,4678,4831,4962,5068,5211,5337,5453,5710,5851,5957,6106,6232,6380,6519,6585,6655", "endColumns": "110,152,130,105,142,125,115,106,140,105,148,125,147,138,65,69,82", "endOffsets": "4673,4826,4957,5063,5206,5332,5448,5555,5846,5952,6101,6227,6375,6514,6580,6650,6733"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/res/values-fi/values-fi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,344,418,501,590,672,768,876,960,1022,1087,1180,1255,1320,1408,1473,1539,1597,1668,1734,1788,1898,1958,2022,2076,2149,2265,2349,2425,2516,2597,2678,2811,2896,2981,3114,3204,3278,3330,3381,3447,3524,3606,3677,3751,3825,3904,3981,4053,4160,4249,4325,4416,4511,4585,4658,4752,4806,4880,4952,5038,5124,5186,5250,5313,5384,5485,5588,5683,5783,5839,5894,5973,6059,6138", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,73,82,88,81,95,107,83,61,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,75,90,80,80,132,84,84,132,89,73,51,50,65,76,81,70,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78,85,78,74", "endOffsets": "263,339,413,496,585,667,763,871,955,1017,1082,1175,1250,1315,1403,1468,1534,1592,1663,1729,1783,1893,1953,2017,2071,2144,2260,2344,2420,2511,2592,2673,2806,2891,2976,3109,3199,3273,3325,3376,3442,3519,3601,3672,3746,3820,3899,3976,4048,4155,4244,4320,4411,4506,4580,4653,4747,4801,4875,4947,5033,5119,5181,5245,5308,5379,5480,5583,5678,5778,5834,5889,5968,6054,6133,6208"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,68,69,70,72,75,77,78,79,80,81,82,83,84,85,86,87,88,89,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,144,145,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3059,3135,3209,3292,3381,4197,4293,4401,6738,6800,6865,7030,7256,7390,7478,7543,7609,7667,7738,7804,7858,7968,8028,8092,8146,8219,8563,8647,8723,8814,8895,8976,9109,9194,9279,9412,9502,9576,9628,9679,9745,9822,9904,9975,10049,10123,10202,10279,10351,10458,10547,10623,10714,10809,10883,10956,11050,11104,11178,11250,11336,11422,11484,11548,11611,11682,11783,11886,11981,12081,12137,12192,12679,12765,12844", "endLines": "5,34,35,36,37,38,46,47,48,68,69,70,72,75,77,78,79,80,81,82,83,84,85,86,87,88,89,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,144,145,146", "endColumns": "12,75,73,82,88,81,95,107,83,61,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,75,90,80,80,132,84,84,132,89,73,51,50,65,76,81,70,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78,85,78,74", "endOffsets": "313,3130,3204,3287,3376,3458,4288,4396,4480,6795,6860,6953,7100,7316,7473,7538,7604,7662,7733,7799,7853,7963,8023,8087,8141,8214,8330,8642,8718,8809,8890,8971,9104,9189,9274,9407,9497,9571,9623,9674,9740,9817,9899,9970,10044,10118,10197,10274,10346,10453,10542,10618,10709,10804,10878,10951,11045,11099,11173,11245,11331,11417,11479,11543,11606,11677,11778,11881,11976,12076,12132,12187,12266,12760,12839,12914"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/res/values-fi/values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,426,526,635,721,826,944,1030,1109,1200,1293,1388,1482,1576,1669,1765,1864,1955,2049,2129,2236,2337,2434,2540,2640,2738,2888,12598", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "421,521,630,716,821,939,1025,1104,1195,1288,1383,1477,1571,1664,1760,1859,1950,2044,2124,2231,2332,2429,2535,2635,2733,2883,2983,12674"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/res/values-fi/values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,208,280,348,431,500,570,649,728,813,899,973,1055,1139,1215,1300,1384,1464,1543,1618,1703,1779,1859,1930", "endColumns": "70,81,71,67,82,68,69,78,78,84,85,73,81,83,75,84,83,79,78,74,84,75,79,70,78", "endOffsets": "121,203,275,343,426,495,565,644,723,808,894,968,1050,1134,1210,1295,1379,1459,1538,1613,1698,1774,1854,1925,2004"}, "to": {"startLines": "33,49,71,73,74,76,90,91,92,139,140,141,142,147,148,149,150,151,152,153,154,156,157,158,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2988,4485,6958,7105,7173,7321,8335,8405,8484,12271,12356,12442,12516,12919,13003,13079,13164,13248,13328,13407,13482,13668,13744,13824,13895", "endColumns": "70,81,71,67,82,68,69,78,78,84,85,73,81,83,75,84,83,79,78,74,84,75,79,70,78", "endOffsets": "3054,4562,7025,7168,7251,7385,8400,8479,8558,12351,12437,12511,12593,12998,13074,13159,13243,13323,13402,13477,13562,13739,13819,13890,13969"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/res/values-fi/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5560", "endColumns": "149", "endOffsets": "5705"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/res/values-fi/values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "39,40,41,42,43,44,45,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3463,3559,3661,3759,3864,3969,4081,13567", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "3554,3656,3754,3859,3964,4076,4192,13663"}}]}]}