{"logs": [{"outputFile": "com.alertcomm.app-mergeDebugResources-56:/values-sk/values-sk.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/res/values-sk/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5738", "endColumns": "138", "endOffsets": "5872"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/res/values-sk/values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "41,42,43,44,45,46,47,157", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3618,3714,3816,3917,4015,4125,4233,13885", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "3709,3811,3912,4010,4120,4228,4350,13981"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/res/values-sk/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,571,677,829,953,1062,1160,1325,1432,1598,1724,1883,2043,2107,2170", "endColumns": "101,155,119,105,151,123,108,97,164,106,165,125,158,159,63,62,82", "endOffsets": "294,450,570,676,828,952,1061,1159,1324,1431,1597,1723,1882,2042,2106,2169,2252"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4739,4845,5005,5129,5239,5395,5523,5636,5877,6046,6157,6327,6457,6620,6784,6852,6919", "endColumns": "105,159,123,109,155,127,112,101,168,110,169,129,162,163,67,66,86", "endOffsets": "4840,5000,5124,5234,5390,5518,5631,5733,6041,6152,6322,6452,6615,6779,6847,6914,7001"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/res/values-sk/values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "428,535,636,747,833,941,1059,1138,1215,1306,1399,1497,1591,1691,1784,1879,1977,2068,2159,2243,2348,2456,2555,2661,2773,2876,3042,12933", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "530,631,742,828,936,1054,1133,1210,1301,1394,1492,1586,1686,1779,1874,1972,2063,2154,2238,2343,2451,2550,2656,2768,2871,3037,3135,13011"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/res/values-sk/values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,213,284,355,442,510,579,660,741,828,923,997,1083,1167,1244,1325,1407,1485,1560,1634,1718,1789,1868,1939", "endColumns": "74,82,70,70,86,67,68,80,80,86,94,73,85,83,76,80,81,77,74,73,83,70,78,70,82", "endOffsets": "125,208,279,350,437,505,574,655,736,823,918,992,1078,1162,1239,1320,1402,1480,1555,1629,1713,1784,1863,1934,2017"}, "to": {"startLines": "35,51,73,75,76,78,92,93,94,141,142,143,144,149,150,151,152,153,154,155,156,158,159,160,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3140,4656,7231,7378,7449,7601,8621,8690,8771,12591,12678,12773,12847,13250,13334,13411,13492,13574,13652,13727,13801,13986,14057,14136,14207", "endColumns": "74,82,70,70,86,67,68,80,80,86,94,73,85,83,76,80,81,77,74,73,83,70,78,70,82", "endOffsets": "3210,4734,7297,7444,7531,7664,8685,8766,8847,12673,12768,12842,12928,13329,13406,13487,13569,13647,13722,13796,13880,14052,14131,14202,14285"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/res/values-sk/values-sk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,378,453,528,606,698,781,873,1001,1082,1143,1208,1307,1383,1448,1538,1602,1668,1722,1791,1851,1905,2022,2082,2144,2198,2270,2400,2487,2567,2663,2747,2839,2978,3047,3125,3256,3344,3424,3478,3529,3595,3667,3744,3815,3897,3969,4046,4119,4190,4295,4383,4455,4547,4643,4717,4791,4887,4939,5021,5088,5175,5262,5324,5388,5451,5519,5625,5732,5830,5947,6005,6060,6139,6222,6297", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,74,74,77,91,82,91,127,80,60,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,79,95,83,91,138,68,77,130,87,79,53,50,65,71,76,70,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78,82,74,75", "endOffsets": "373,448,523,601,693,776,868,996,1077,1138,1203,1302,1378,1443,1533,1597,1663,1717,1786,1846,1900,2017,2077,2139,2193,2265,2395,2482,2562,2658,2742,2834,2973,3042,3120,3251,3339,3419,3473,3524,3590,3662,3739,3810,3892,3964,4041,4114,4185,4290,4378,4450,4542,4638,4712,4786,4882,4934,5016,5083,5170,5257,5319,5383,5446,5514,5620,5727,5825,5942,6000,6055,6134,6217,6292,6368"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,70,71,72,74,77,79,80,81,82,83,84,85,86,87,88,89,90,91,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,146,147,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3215,3290,3365,3443,3535,4355,4447,4575,7006,7067,7132,7302,7536,7669,7759,7823,7889,7943,8012,8072,8126,8243,8303,8365,8419,8491,8852,8939,9019,9115,9199,9291,9430,9499,9577,9708,9796,9876,9930,9981,10047,10119,10196,10267,10349,10421,10498,10571,10642,10747,10835,10907,10999,11095,11169,11243,11339,11391,11473,11540,11627,11714,11776,11840,11903,11971,12077,12184,12282,12399,12457,12512,13016,13099,13174", "endLines": "7,36,37,38,39,40,48,49,50,70,71,72,74,77,79,80,81,82,83,84,85,86,87,88,89,90,91,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,146,147,148", "endColumns": "12,74,74,77,91,82,91,127,80,60,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,79,95,83,91,138,68,77,130,87,79,53,50,65,71,76,70,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78,82,74,75", "endOffsets": "423,3285,3360,3438,3530,3613,4442,4570,4651,7062,7127,7226,7373,7596,7754,7818,7884,7938,8007,8067,8121,8238,8298,8360,8414,8486,8616,8934,9014,9110,9194,9286,9425,9494,9572,9703,9791,9871,9925,9976,10042,10114,10191,10262,10344,10416,10493,10566,10637,10742,10830,10902,10994,11090,11164,11238,11334,11386,11468,11535,11622,11709,11771,11835,11898,11966,12072,12179,12277,12394,12452,12507,12586,13094,13169,13245"}}]}]}