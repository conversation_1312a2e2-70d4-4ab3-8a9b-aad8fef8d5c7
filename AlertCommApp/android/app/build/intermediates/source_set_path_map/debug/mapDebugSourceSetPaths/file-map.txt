com.alertcomm.app-lifecycle-service-2.6.2-0 /Users/<USER>/.gradle/caches/8.13/transforms/042c063257c458b0d7190d8859d3193e/transformed/lifecycle-service-2.6.2/res
com.alertcomm.app-tracing-ktx-1.2.0-1 /Users/<USER>/.gradle/caches/8.13/transforms/05c532ea6eeed5ba96fff64378302997/transformed/tracing-ktx-1.2.0/res
com.alertcomm.app-appcompat-resources-1.7.0-2 /Users/<USER>/.gradle/caches/8.13/transforms/067eb4768d0977cc253e099ab589c211/transformed/appcompat-resources-1.7.0/res
com.alertcomm.app-lifecycle-livedata-core-ktx-2.6.2-3 /Users/<USER>/.gradle/caches/8.13/transforms/0e45932910c15ea66169daadac963681/transformed/lifecycle-livedata-core-ktx-2.6.2/res
com.alertcomm.app-startup-runtime-1.1.1-4 /Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/res
com.alertcomm.app-constraintlayout-2.0.1-5 /Users/<USER>/.gradle/caches/8.13/transforms/162c641a6ded6de9dcbbe71bb887fa9c/transformed/constraintlayout-2.0.1/res
com.alertcomm.app-savedstate-1.2.1-6 /Users/<USER>/.gradle/caches/8.13/transforms/19f8795450958262f34e41d8151462d1/transformed/savedstate-1.2.1/res
com.alertcomm.app-fragment-ktx-1.8.5-7 /Users/<USER>/.gradle/caches/8.13/transforms/21399c389b592e9743307d8c0f0007ad/transformed/fragment-ktx-1.8.5/res
com.alertcomm.app-lifecycle-runtime-ktx-2.6.2-8 /Users/<USER>/.gradle/caches/8.13/transforms/2af3bcc447ac110862396e7d0b0ec9ae/transformed/lifecycle-runtime-ktx-2.6.2/res
com.alertcomm.app-recyclerview-1.1.0-9 /Users/<USER>/.gradle/caches/8.13/transforms/2db4240268c509e9b190ca1a393ca7d5/transformed/recyclerview-1.1.0/res
com.alertcomm.app-drawerlayout-1.1.1-10 /Users/<USER>/.gradle/caches/8.13/transforms/34bb67911f8414f1ff69475eb4175853/transformed/drawerlayout-1.1.1/res
com.alertcomm.app-expo.modules.filesystem-18.1.11-11 /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/res
com.alertcomm.app-play-services-maps-19.1.0-12 /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/res
com.alertcomm.app-core-runtime-2.2.0-13 /Users/<USER>/.gradle/caches/8.13/transforms/3b4cadc5d581274a383a17f34b96bacf/transformed/core-runtime-2.2.0/res
com.alertcomm.app-firebase-messaging-24.0.1-14 /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/res
com.alertcomm.app-core-1.15.0-15 /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/res
com.alertcomm.app-lifecycle-viewmodel-2.6.2-16 /Users/<USER>/.gradle/caches/8.13/transforms/44911df0b7c9625b5575bd1446144add/transformed/lifecycle-viewmodel-2.6.2/res
com.alertcomm.app-profileinstaller-1.3.1-17 /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/res
com.alertcomm.app-activity-1.8.1-18 /Users/<USER>/.gradle/caches/8.13/transforms/4fc74322dd0e0dd78e32a282f08beefe/transformed/activity-1.8.1/res
com.alertcomm.app-autofill-1.1.0-19 /Users/<USER>/.gradle/caches/8.13/transforms/50fb524026069718687c4ab652c29053/transformed/autofill-1.1.0/res
com.alertcomm.app-lifecycle-runtime-2.6.2-20 /Users/<USER>/.gradle/caches/8.13/transforms/5379c5771c0cc88a961c78af9bb09339/transformed/lifecycle-runtime-2.6.2/res
com.alertcomm.app-cardview-1.0.0-21 /Users/<USER>/.gradle/caches/8.13/transforms/5745f858b74ff0a2f8e17d4b26e19812/transformed/cardview-1.0.0/res
com.alertcomm.app-emoji2-1.3.0-22 /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/res
com.alertcomm.app-tracing-1.2.0-23 /Users/<USER>/.gradle/caches/8.13/transforms/5e0f9e6bf46b1bd6aeea3c4a7ba42299/transformed/tracing-1.2.0/res
com.alertcomm.app-media-1.0.0-24 /Users/<USER>/.gradle/caches/8.13/transforms/6183b82bc0c6a1834671e2c74c065f3b/transformed/media-1.0.0/res
com.alertcomm.app-viewpager2-1.0.0-25 /Users/<USER>/.gradle/caches/8.13/transforms/65ba2f9fa3c9fe83e5152e0f7195116c/transformed/viewpager2-1.0.0/res
com.alertcomm.app-activity-ktx-1.8.1-26 /Users/<USER>/.gradle/caches/8.13/transforms/6a9ec132b645f46bd08ff5acb22515de/transformed/activity-ktx-1.8.1/res
com.alertcomm.app-savedstate-ktx-1.2.1-27 /Users/<USER>/.gradle/caches/8.13/transforms/6d8784d24425b9166c30f1a4f5571e15/transformed/savedstate-ktx-1.2.1/res
com.alertcomm.app-sqlite-framework-2.3.0-28 /Users/<USER>/.gradle/caches/8.13/transforms/6d955f79743302bcd3796d7d9def5561/transformed/sqlite-framework-2.3.0/res
com.alertcomm.app-work-runtime-2.9.1-29 /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/res
com.alertcomm.app-material-1.12.0-30 /Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/res
com.alertcomm.app-android-maps-utils-3.10.0-31 /Users/<USER>/.gradle/caches/8.13/transforms/837a13fdb801afa1460e9b1aae21e199/transformed/android-maps-utils-3.10.0/res
com.alertcomm.app-play-services-basement-18.4.0-32 /Users/<USER>/.gradle/caches/8.13/transforms/8c47125c9a335e989accf2286b6eb952/transformed/play-services-basement-18.4.0/res
com.alertcomm.app-lifecycle-livedata-core-2.6.2-33 /Users/<USER>/.gradle/caches/8.13/transforms/8fbfdc943d04e82c10add9c26833a5c6/transformed/lifecycle-livedata-core-2.6.2/res
com.alertcomm.app-drawee-3.6.0-34 /Users/<USER>/.gradle/caches/8.13/transforms/90554224eff49d6253bc7f46090a200f/transformed/drawee-3.6.0/res
com.alertcomm.app-transition-1.5.0-35 /Users/<USER>/.gradle/caches/8.13/transforms/925e579c481c06349ca2c1bdadbdec35/transformed/transition-1.5.0/res
com.alertcomm.app-annotation-experimental-1.4.1-36 /Users/<USER>/.gradle/caches/8.13/transforms/9478ca0554caeea9ae571964e0cb2ca6/transformed/annotation-experimental-1.4.1/res
com.alertcomm.app-coordinatorlayout-1.2.0-37 /Users/<USER>/.gradle/caches/8.13/transforms/94f09ae075da36273dd5b0f48955e034/transformed/coordinatorlayout-1.2.0/res
com.alertcomm.app-lifecycle-viewmodel-ktx-2.6.2-38 /Users/<USER>/.gradle/caches/8.13/transforms/98eec38dab2893f9dde1642e7a91ed70/transformed/lifecycle-viewmodel-ktx-2.6.2/res
com.alertcomm.app-room-ktx-2.5.0-39 /Users/<USER>/.gradle/caches/8.13/transforms/9aea9ef70537bb27590c482118101d15/transformed/room-ktx-2.5.0/res
com.alertcomm.app-expo.modules.notifications-0.31.4-40 /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/res
com.alertcomm.app-lifecycle-process-2.6.2-41 /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/res
com.alertcomm.app-appcompat-1.7.0-42 /Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/res
com.alertcomm.app-fragment-1.8.5-43 /Users/<USER>/.gradle/caches/8.13/transforms/b5f0c96b16b42d86750be76b7bb8c375/transformed/fragment-1.8.5/res
com.alertcomm.app-play-services-base-18.5.0-44 /Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/res
com.alertcomm.app-swiperefreshlayout-1.1.0-45 /Users/<USER>/.gradle/caches/8.13/transforms/c3179e0f1b67cb33410fa674edb5496c/transformed/swiperefreshlayout-1.1.0/res
com.alertcomm.app-lifecycle-viewmodel-savedstate-2.6.2-46 /Users/<USER>/.gradle/caches/8.13/transforms/c4b770dc8989251f37e56402d8aa1685/transformed/lifecycle-viewmodel-savedstate-2.6.2/res
com.alertcomm.app-core-ktx-1.15.0-47 /Users/<USER>/.gradle/caches/8.13/transforms/c6517c74281c1909d506f689018aed8b/transformed/core-ktx-1.15.0/res
com.alertcomm.app-firebase-common-21.0.0-48 /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/res
com.alertcomm.app-sqlite-2.3.0-49 /Users/<USER>/.gradle/caches/8.13/transforms/e2997f661b1f9bbd1c1e0b3ba95ac331/transformed/sqlite-2.3.0/res
com.alertcomm.app-emoji2-views-helper-1.3.0-50 /Users/<USER>/.gradle/caches/8.13/transforms/ec1853d0b0976e32a4f952e316c0c604/transformed/emoji2-views-helper-1.3.0/res
com.alertcomm.app-react-android-0.79.5-debug-51 /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/res
com.alertcomm.app-room-runtime-2.5.0-52 /Users/<USER>/.gradle/caches/8.13/transforms/f7e6af7a886ab03e93fbcefb0f4519c4/transformed/room-runtime-2.5.0/res
com.alertcomm.app-lifecycle-livedata-2.6.2-53 /Users/<USER>/.gradle/caches/8.13/transforms/fa08d204398219a1a89b242b31fa3041/transformed/lifecycle-livedata-2.6.2/res
com.alertcomm.app-pngs-54 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/res/pngs/debug
com.alertcomm.app-resValues-55 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/res/resValues/debug
com.alertcomm.app-packageDebugResources-56 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/incremental/debug/packageDebugResources/merged.dir
com.alertcomm.app-packageDebugResources-57 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/incremental/debug/packageDebugResources/stripped.dir
com.alertcomm.app-debug-58 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/merged_res/debug/mergeDebugResources
com.alertcomm.app-debug-59 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/res
com.alertcomm.app-main-60 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/res
com.alertcomm.app-debug-61 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-62 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-63 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-constants/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-64 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-65 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-66 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-67 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-68 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-69 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-push-notification/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-70 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-71 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-72 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/intermediates/packaged_res/debug/packageDebugResources
