/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \
  -H/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup \
  -DCMAKE_SYSTEM_NAME=Android \
  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
  -DCMAKE_SYSTEM_VERSION=24 \
  -DANDROID_PLATFORM=android-24 \
  -DANDROID_ABI=armeabi-v7a \
  -DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a \
  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006 \
  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006 \
  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake \
  -DC<PERSON><PERSON>_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \
  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/armeabi-v7a \
  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/armeabi-v7a \
  -DCMAKE_BUILD_TYPE=Debug \
  -DCMAKE_FIND_ROOT_PATH=/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/prefab/armeabi-v7a/prefab \
  -B/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/armeabi-v7a \
  -GNinja \
  -DPROJECT_BUILD_DIR=/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build \
  -DPROJECT_ROOT_DIR=/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android \
  -DREACT_ANDROID_DIR=/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid \
  -DANDROID_STL=c++_shared \
  -DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON
