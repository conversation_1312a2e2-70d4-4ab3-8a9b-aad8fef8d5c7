1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.alertcomm.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:6:3-75
11-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:6:20-73
12    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
12-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:2:3-78
12-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:2:20-76
13    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
13-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:3:3-76
13-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:3:20-74
14    <uses-permission android:name="android.permission.INTERNET" />
14-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:4:3-64
14-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:4:20-62
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:5:3-77
15-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:5:20-75
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:7:3-63
16-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:7:20-61
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:8:3-78
17-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:8:20-76
18
19    <queries>
19-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:9:3-15:13
20        <intent>
20-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:10:5-14:14
21            <action android:name="android.intent.action.VIEW" />
21-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:11:7-58
21-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:11:15-56
22
23            <category android:name="android.intent.category.BROWSABLE" />
23-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:12:7-67
23-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:12:17-65
24
25            <data android:scheme="https" />
25-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:13:7-37
25-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:13:13-35
26        </intent>
27        <!-- Query open documents -->
28        <intent>
28-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:15:9-17:18
29            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
29-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:16:13-79
29-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:16:21-76
30        </intent> <!-- Needs to be explicitly declared on Android R+ -->
31        <package android:name="com.google.android.apps.maps" />
31-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:29:7-61
31-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:29:16-59
32    </queries>
33
34    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
34-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:7:5-81
34-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:7:22-78
35    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
35-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:8:5-77
35-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:8:22-74
36    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
36-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:22:5-79
36-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:22:22-76
37    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
37-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:24:5-68
37-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:24:22-65
38    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
38-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:26:5-82
38-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:26:22-79
39    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
39-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:26:5-77
39-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:26:22-74
40
41    <uses-feature
41-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:22:5-24:33
42        android:glEsVersion="0x00020000"
42-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:23:8-40
43        android:required="true" />
43-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:24:8-31
44
45    <permission
45-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:22:5-24:47
46        android:name="com.alertcomm.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
46-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:23:9-81
47        android:protectionLevel="signature" />
47-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:24:9-44
48
49    <uses-permission android:name="com.alertcomm.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- for android -->
49-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:26:5-97
49-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:26:22-94
50    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
51    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
52    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
53    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
54    <!-- for Samsung -->
55    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
55-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:19:5-86
55-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:19:22-83
56    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
56-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:20:5-87
56-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:20:22-84
57    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
57-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:23:5-81
57-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:23:22-78
58    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
58-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:24:5-83
58-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:24:22-80
59    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
59-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:27:5-88
59-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:27:22-85
60    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
60-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:28:5-92
60-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:28:22-89
61    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
61-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:31:5-84
61-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:31:22-81
62    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
62-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:34:5-83
62-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:34:22-80
63    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
63-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:37:5-91
63-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:37:22-88
64    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
64-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:38:5-92
64-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:38:22-89
65    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
65-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:39:5-93
65-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:39:22-90
66    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
66-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:42:5-73
66-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:42:22-70
67    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
67-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:45:5-82
67-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:45:22-79
68    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
68-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:46:5-83
68-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:46:22-80
69    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
69-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:49:5-88
69-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:49:22-85
70    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
70-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:50:5-89
70-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:50:22-86
71    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
71-->[com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/e75f83e5e2bd4ad05880a4e7cced326b/transformed/installreferrer-2.2/AndroidManifest.xml:9:5-110
71-->[com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/e75f83e5e2bd4ad05880a4e7cced326b/transformed/installreferrer-2.2/AndroidManifest.xml:9:22-107
72
73    <application
73-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:3-36:17
74        android:name="com.alertcomm.app.MainApplication"
74-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:16-47
75        android:allowBackup="true"
75-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:162-188
76        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
76-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:28:18-86
77        android:debuggable="true"
78        android:extractNativeLibs="false"
79        android:icon="@mipmap/ic_launcher"
79-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:81-115
80        android:label="@string/app_name"
80-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:48-80
81        android:roundIcon="@mipmap/ic_launcher_round"
81-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:116-161
82        android:supportsRtl="true"
82-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:221-247
83        android:theme="@style/AppTheme"
83-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:189-220
84        android:usesCleartextTraffic="true" >
84-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml:6:18-53
85        <meta-data
85-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:17:5-139
86            android:name="com.google.firebase.messaging.default_notification_color"
86-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:17:16-87
87            android:resource="@color/notification_icon_color" />
87-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:17:88-137
88        <meta-data
88-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:18:5-135
89            android:name="com.google.firebase.messaging.default_notification_icon"
89-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:18:16-86
90            android:resource="@drawable/notification_icon" />
90-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:18:87-133
91        <meta-data
91-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:19:5-136
92            android:name="expo.modules.notifications.default_notification_color"
92-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:19:16-84
93            android:resource="@color/notification_icon_color" />
93-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:19:85-134
94        <meta-data
94-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:20:5-132
95            android:name="expo.modules.notifications.default_notification_icon"
95-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:20:16-83
96            android:resource="@drawable/notification_icon" />
96-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:20:84-130
97        <meta-data
97-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:21:5-83
98            android:name="expo.modules.updates.ENABLED"
98-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:21:16-59
99            android:value="false" />
99-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:21:60-81
100        <meta-data
100-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:22:5-105
101            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
101-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:22:16-80
102            android:value="ALWAYS" />
102-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:22:81-103
103        <meta-data
103-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:23:5-99
104            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
104-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:23:16-79
105            android:value="0" />
105-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:23:80-97
106
107        <activity
107-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:5-35:16
108            android:name="com.alertcomm.app.MainActivity"
108-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:15-43
109            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
109-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:44-134
110            android:exported="true"
110-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:256-279
111            android:launchMode="singleTask"
111-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:135-166
112            android:screenOrientation="portrait"
112-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:280-316
113            android:theme="@style/Theme.App.SplashScreen"
113-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:210-255
114            android:windowSoftInputMode="adjustResize" >
114-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:167-209
115            <intent-filter>
115-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:25:7-28:23
116                <action android:name="android.intent.action.MAIN" />
116-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:26:9-60
116-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:26:17-58
117
118                <category android:name="android.intent.category.LAUNCHER" />
118-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:27:9-68
118-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:27:19-66
119            </intent-filter>
120            <intent-filter>
120-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:29:7-34:23
121                <action android:name="android.intent.action.VIEW" />
121-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:11:7-58
121-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:11:15-56
122
123                <category android:name="android.intent.category.DEFAULT" />
123-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:31:9-67
123-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:31:19-65
124                <category android:name="android.intent.category.BROWSABLE" />
124-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:12:7-67
124-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:12:17-65
125
126                <data android:scheme="exp+alertcomm-app" />
126-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:13:7-37
126-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:13:13-35
127            </intent-filter>
128        </activity>
129
130        <meta-data
130-->[:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-11:89
131            android:name="org.unimodules.core.AppLoader#react-native-headless"
131-->[:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-79
132            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
132-->[:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-86
133        <meta-data
133-->[:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:9-15:45
134            android:name="com.facebook.soloader.enabled"
134-->[:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-57
135            android:value="true" />
135-->[:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-33
136
137        <activity
137-->[com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:19:9-21:40
138            android:name="com.facebook.react.devsupport.DevSettingsActivity"
138-->[com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:20:13-77
139            android:exported="false" />
139-->[com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:21:13-37
140
141        <provider
141-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:21:9-30:20
142            android:name="expo.modules.filesystem.FileSystemFileProvider"
142-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:22:13-74
143            android:authorities="com.alertcomm.app.FileSystemFileProvider"
143-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:23:13-74
144            android:exported="false"
144-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:24:13-37
145            android:grantUriPermissions="true" >
145-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:25:13-47
146            <meta-data
146-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:27:13-29:70
147                android:name="android.support.FILE_PROVIDER_PATHS"
147-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:28:17-67
148                android:resource="@xml/file_system_provider_paths" />
148-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:29:17-67
149        </provider>
150
151        <service
151-->[host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/c32eb368d89b8af8c6a684bc0b543217/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:11:9-14:56
152            android:name="expo.modules.location.services.LocationTaskService"
152-->[host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/c32eb368d89b8af8c6a684bc0b543217/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:12:13-78
153            android:exported="false"
153-->[host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/c32eb368d89b8af8c6a684bc0b543217/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:13:13-37
154            android:foregroundServiceType="location" />
154-->[host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/c32eb368d89b8af8c6a684bc0b543217/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:14:13-53
155        <service
155-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:11:9-17:19
156            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
156-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:12:13-91
157            android:exported="false" >
157-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:13:13-37
158            <intent-filter android:priority="-1" >
158-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:14:13-16:29
158-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:14:28-49
159                <action android:name="com.google.firebase.MESSAGING_EVENT" />
159-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:15:17-78
159-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:15:25-75
160            </intent-filter>
161        </service>
162
163        <receiver
163-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:19:9-31:20
164            android:name="expo.modules.notifications.service.NotificationsService"
164-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:20:13-83
165            android:enabled="true"
165-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:21:13-35
166            android:exported="false" >
166-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:22:13-37
167            <intent-filter android:priority="-1" >
167-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:23:13-30:29
167-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:23:28-49
168                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
168-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:24:17-88
168-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:24:25-85
169                <action android:name="android.intent.action.BOOT_COMPLETED" />
169-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:25:17-79
169-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:25:25-76
170                <action android:name="android.intent.action.REBOOT" />
170-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:26:17-71
170-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:26:25-68
171                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
171-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:27:17-82
171-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:27:25-79
172                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
172-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:28:17-82
172-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:28:25-79
173                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
173-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:29:17-84
173-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:29:25-81
174            </intent-filter>
175        </receiver>
176
177        <activity
177-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:33:9-40:75
178            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
178-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:34:13-92
179            android:excludeFromRecents="true"
179-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:35:13-46
180            android:exported="false"
180-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:36:13-37
181            android:launchMode="standard"
181-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:37:13-42
182            android:noHistory="true"
182-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:38:13-37
183            android:taskAffinity=""
183-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:39:13-36
184            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
184-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:40:13-72
185
186        <receiver
186-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:29:9-40:20
187            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
187-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:30:13-78
188            android:exported="true"
188-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:31:13-36
189            android:permission="com.google.android.c2dm.permission.SEND" >
189-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:32:13-73
190            <intent-filter>
190-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:33:13-35:29
191                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
191-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:34:17-81
191-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:34:25-78
192            </intent-filter>
193
194            <meta-data
194-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:37:13-39:40
195                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
195-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:38:17-92
196                android:value="true" />
196-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:39:17-37
197        </receiver>
198        <!--
199             FirebaseMessagingService performs security checks at runtime,
200             but set to not exported to explicitly avoid allowing another app to call it.
201        -->
202        <service
202-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:46:9-53:19
203            android:name="com.google.firebase.messaging.FirebaseMessagingService"
203-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:47:13-82
204            android:directBootAware="true"
204-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:48:13-43
205            android:exported="false" >
205-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:49:13-37
206            <intent-filter android:priority="-500" >
206-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:14:13-16:29
206-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:14:28-49
207                <action android:name="com.google.firebase.MESSAGING_EVENT" />
207-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:15:17-78
207-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:15:25-75
208            </intent-filter>
209        </service>
210        <service
210-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:54:9-63:19
211            android:name="com.google.firebase.components.ComponentDiscoveryService"
211-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:55:13-84
212            android:directBootAware="true"
212-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:32:13-43
213            android:exported="false" >
213-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:56:13-37
214            <meta-data
214-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:57:13-59:85
215                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
215-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:58:17-122
216                android:value="com.google.firebase.components.ComponentRegistrar" />
216-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:59:17-82
217            <meta-data
217-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:60:13-62:85
218                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
218-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:61:17-119
219                android:value="com.google.firebase.components.ComponentRegistrar" />
219-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:62:17-82
220            <meta-data
220-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:15:13-17:85
221                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
221-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:16:17-130
222                android:value="com.google.firebase.components.ComponentRegistrar" />
222-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:17:17-82
223            <meta-data
223-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:18:13-20:85
224                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
224-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:19:17-127
225                android:value="com.google.firebase.components.ComponentRegistrar" />
225-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:20:17-82
226            <meta-data
226-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f41a0a0abe9082c2adadbc91827429e5/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
227                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
227-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f41a0a0abe9082c2adadbc91827429e5/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
228                android:value="com.google.firebase.components.ComponentRegistrar" />
228-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f41a0a0abe9082c2adadbc91827429e5/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
229            <meta-data
229-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
230                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
230-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:36:17-109
231                android:value="com.google.firebase.components.ComponentRegistrar" />
231-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:37:17-82
232            <meta-data
232-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/78a3c009a8dccc885176388c2c4753ad/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
233                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
233-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/78a3c009a8dccc885176388c2c4753ad/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
234                android:value="com.google.firebase.components.ComponentRegistrar" />
234-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/78a3c009a8dccc885176388c2c4753ad/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
235        </service>
236
237        <provider
237-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:29:9-37:20
238            android:name="androidx.startup.InitializationProvider"
238-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:30:13-67
239            android:authorities="com.alertcomm.app.androidx-startup"
239-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:31:13-68
240            android:exported="false" >
240-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:32:13-37
241            <meta-data
241-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:34:13-36:52
242                android:name="androidx.work.WorkManagerInitializer"
242-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:35:17-68
243                android:value="androidx.startup" />
243-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:36:17-49
244            <meta-data
244-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
245                android:name="androidx.emoji2.text.EmojiCompatInitializer"
245-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
246                android:value="androidx.startup" />
246-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
247            <meta-data
247-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
248                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
248-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
249                android:value="androidx.startup" />
249-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
250            <meta-data
250-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
251                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
251-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
252                android:value="androidx.startup" />
252-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
253        </provider>
254
255        <service
255-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:39:9-45:35
256            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
256-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:40:13-88
257            android:directBootAware="false"
257-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:41:13-44
258            android:enabled="@bool/enable_system_alarm_service_default"
258-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:42:13-72
259            android:exported="false" />
259-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:43:13-37
260        <service
260-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:46:9-52:35
261            android:name="androidx.work.impl.background.systemjob.SystemJobService"
261-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:47:13-84
262            android:directBootAware="false"
262-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:48:13-44
263            android:enabled="@bool/enable_system_job_service_default"
263-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:49:13-70
264            android:exported="true"
264-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:50:13-36
265            android:permission="android.permission.BIND_JOB_SERVICE" />
265-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:51:13-69
266        <service
266-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:53:9-59:35
267            android:name="androidx.work.impl.foreground.SystemForegroundService"
267-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:54:13-81
268            android:directBootAware="false"
268-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:55:13-44
269            android:enabled="@bool/enable_system_foreground_service_default"
269-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:56:13-77
270            android:exported="false" />
270-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:57:13-37
271
272        <receiver
272-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:61:9-66:35
273            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
273-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:62:13-88
274            android:directBootAware="false"
274-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:63:13-44
275            android:enabled="true"
275-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:64:13-35
276            android:exported="false" />
276-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:65:13-37
277        <receiver
277-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:67:9-77:20
278            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
278-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:68:13-106
279            android:directBootAware="false"
279-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:69:13-44
280            android:enabled="false"
280-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:70:13-36
281            android:exported="false" >
281-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:71:13-37
282            <intent-filter>
282-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:73:13-76:29
283                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
283-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:74:17-87
283-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:74:25-84
284                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
284-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:75:17-90
284-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:75:25-87
285            </intent-filter>
286        </receiver>
287        <receiver
287-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:78:9-88:20
288            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
288-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:79:13-104
289            android:directBootAware="false"
289-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:80:13-44
290            android:enabled="false"
290-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:81:13-36
291            android:exported="false" >
291-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:82:13-37
292            <intent-filter>
292-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:84:13-87:29
293                <action android:name="android.intent.action.BATTERY_OKAY" />
293-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:85:17-77
293-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:85:25-74
294                <action android:name="android.intent.action.BATTERY_LOW" />
294-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:86:17-76
294-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:86:25-73
295            </intent-filter>
296        </receiver>
297        <receiver
297-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:89:9-99:20
298            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
298-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:90:13-104
299            android:directBootAware="false"
299-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:91:13-44
300            android:enabled="false"
300-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:92:13-36
301            android:exported="false" >
301-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:93:13-37
302            <intent-filter>
302-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:95:13-98:29
303                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
303-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:96:17-83
303-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:96:25-80
304                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
304-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:97:17-82
304-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:97:25-79
305            </intent-filter>
306        </receiver>
307        <receiver
307-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:100:9-109:20
308            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
308-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:101:13-103
309            android:directBootAware="false"
309-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:102:13-44
310            android:enabled="false"
310-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:103:13-36
311            android:exported="false" >
311-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:104:13-37
312            <intent-filter>
312-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:106:13-108:29
313                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
313-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:107:17-79
313-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:107:25-76
314            </intent-filter>
315        </receiver>
316        <receiver
316-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:110:9-121:20
317            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
317-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:111:13-88
318            android:directBootAware="false"
318-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:112:13-44
319            android:enabled="false"
319-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:113:13-36
320            android:exported="false" >
320-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:114:13-37
321            <intent-filter>
321-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:116:13-120:29
322                <action android:name="android.intent.action.BOOT_COMPLETED" />
322-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:25:17-79
322-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:25:25-76
323                <action android:name="android.intent.action.TIME_SET" />
323-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:118:17-73
323-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:118:25-70
324                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
324-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:119:17-81
324-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:119:25-78
325            </intent-filter>
326        </receiver>
327        <receiver
327-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:122:9-131:20
328            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
328-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:123:13-99
329            android:directBootAware="false"
329-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:124:13-44
330            android:enabled="@bool/enable_system_alarm_service_default"
330-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:125:13-72
331            android:exported="false" >
331-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:126:13-37
332            <intent-filter>
332-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:128:13-130:29
333                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
333-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:129:17-98
333-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:129:25-95
334            </intent-filter>
335        </receiver>
336        <receiver
336-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:132:9-142:20
337            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
337-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:133:13-78
338            android:directBootAware="false"
338-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:134:13-44
339            android:enabled="true"
339-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:135:13-35
340            android:exported="true"
340-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:136:13-36
341            android:permission="android.permission.DUMP" >
341-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:137:13-57
342            <intent-filter>
342-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:139:13-141:29
343                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
343-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:140:17-88
343-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:140:25-85
344            </intent-filter>
345        </receiver>
346
347        <service
347-->[androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7e6af7a886ab03e93fbcefb0f4519c4/transformed/room-runtime-2.5.0/AndroidManifest.xml:24:9-28:63
348            android:name="androidx.room.MultiInstanceInvalidationService"
348-->[androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7e6af7a886ab03e93fbcefb0f4519c4/transformed/room-runtime-2.5.0/AndroidManifest.xml:25:13-74
349            android:directBootAware="true"
349-->[androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7e6af7a886ab03e93fbcefb0f4519c4/transformed/room-runtime-2.5.0/AndroidManifest.xml:26:13-43
350            android:exported="false" />
350-->[androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7e6af7a886ab03e93fbcefb0f4519c4/transformed/room-runtime-2.5.0/AndroidManifest.xml:27:13-37
351
352        <meta-data
352-->[com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/837a13fdb801afa1460e9b1aae21e199/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:23:9-25:69
353            android:name="com.google.android.gms.version"
353-->[com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/837a13fdb801afa1460e9b1aae21e199/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:24:13-58
354            android:value="@integer/google_play_services_version" /> <!-- Needs to be explicitly declared on P+ -->
354-->[com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/837a13fdb801afa1460e9b1aae21e199/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:25:13-66
355        <uses-library
355-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:33:7-86
356            android:name="org.apache.http.legacy"
356-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:33:21-58
357            android:required="false" />
357-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:33:59-83
358
359        <activity
359-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:9-173
360            android:name="com.google.android.gms.common.api.GoogleApiActivity"
360-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:19-85
361            android:exported="false"
361-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:146-170
362            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
362-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:86-145
363
364        <provider
364-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
365            android:name="com.google.firebase.provider.FirebaseInitProvider"
365-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:24:13-77
366            android:authorities="com.alertcomm.app.firebaseinitprovider"
366-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:25:13-72
367            android:directBootAware="true"
367-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:26:13-43
368            android:exported="false"
368-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:27:13-37
369            android:initOrder="100" />
369-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:28:13-36
370
371        <receiver
371-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
372            android:name="androidx.profileinstaller.ProfileInstallReceiver"
372-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
373            android:directBootAware="false"
373-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
374            android:enabled="true"
374-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
375            android:exported="true"
375-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
376            android:permission="android.permission.DUMP" >
376-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
377            <intent-filter>
377-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
378                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
378-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
378-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
379            </intent-filter>
380            <intent-filter>
380-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
381                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
381-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
381-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
382            </intent-filter>
383            <intent-filter>
383-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
384                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
384-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
384-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
385            </intent-filter>
386            <intent-filter>
386-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
387                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
387-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
387-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
388            </intent-filter>
389        </receiver>
390
391        <service
391-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
392            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
392-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
393            android:exported="false" >
393-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
394            <meta-data
394-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
395                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
395-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
396                android:value="cct" />
396-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
397        </service>
398        <service
398-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
399            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
399-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
400            android:exported="false"
400-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
401            android:permission="android.permission.BIND_JOB_SERVICE" >
401-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
402        </service>
403
404        <receiver
404-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
405            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
405-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:33:13-132
406            android:exported="false" />
406-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
407    </application>
408
409</manifest>
