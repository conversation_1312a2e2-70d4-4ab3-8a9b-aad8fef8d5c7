1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.alertcomm.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:10:3-75
11-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:10:20-73
12    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
12-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:2:3-82
12-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:2:20-80
13    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
13-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:3:3-78
13-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:3:20-76
14    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
14-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:4:3-76
14-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:4:20-74
15    <uses-permission android:name="android.permission.CAMERA" />
15-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:5:3-62
15-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:5:20-60
16    <uses-permission android:name="android.permission.INTERNET" />
16-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:6:3-64
16-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:6:20-62
17    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
17-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:7:3-77
17-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:7:20-75
18    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
18-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:8:3-78
18-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:8:20-76
19    <uses-permission android:name="android.permission.RECORD_AUDIO" />
19-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:9:3-68
19-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:9:20-66
20    <uses-permission android:name="android.permission.VIBRATE" />
20-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:11:3-63
20-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:11:20-61
21    <uses-permission android:name="android.permission.WAKE_LOCK" />
21-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:12:3-65
21-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:12:20-63
22    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
22-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:13:3-78
22-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:13:20-76
23
24    <queries>
24-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:14:3-20:13
25        <intent>
25-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:15:5-19:14
26            <action android:name="android.intent.action.VIEW" />
26-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:7-58
26-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:15-56
27
28            <category android:name="android.intent.category.BROWSABLE" />
28-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:17:7-67
28-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:17:17-65
29
30            <data android:scheme="https" />
30-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:18:7-37
30-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:18:13-35
31        </intent>
32        <!-- Query open documents -->
33        <intent>
33-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:15:9-17:18
34            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
34-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:16:13-79
34-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:16:21-76
35        </intent> <!-- Needs to be explicitly declared on Android R+ -->
36        <package android:name="com.google.android.apps.maps" />
36-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:29:7-61
36-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:29:16-59
37    </queries>
38
39    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
39-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:8:5-77
39-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:8:22-74
40    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Required by older versions of Google Play services to create IID tokens -->
40-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:22:5-79
40-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:22:22-76
41    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
41-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:26:5-82
41-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:26:22-79
42    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
42-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:26:5-77
42-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:26:22-74
43
44    <uses-feature
44-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:22:5-24:33
45        android:glEsVersion="0x00020000"
45-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:23:8-40
46        android:required="true" />
46-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:24:8-31
47
48    <permission
48-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:22:5-24:47
49        android:name="com.alertcomm.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
49-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:23:9-81
50        android:protectionLevel="signature" />
50-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:24:9-44
51
52    <uses-permission android:name="com.alertcomm.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- for android -->
52-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:26:5-97
52-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:26:22-94
53    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
54    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
55    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
56    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
57    <!-- for Samsung -->
58    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
58-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:19:5-86
58-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:19:22-83
59    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
59-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:20:5-87
59-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:20:22-84
60    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
60-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:23:5-81
60-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:23:22-78
61    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
61-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:24:5-83
61-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:24:22-80
62    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
62-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:27:5-88
62-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:27:22-85
63    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
63-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:28:5-92
63-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:28:22-89
64    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
64-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:31:5-84
64-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:31:22-81
65    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
65-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:34:5-83
65-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:34:22-80
66    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
66-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:37:5-91
66-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:37:22-88
67    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
67-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:38:5-92
67-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:38:22-89
68    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
68-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:39:5-93
68-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:39:22-90
69    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
69-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:42:5-73
69-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:42:22-70
70    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
70-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:45:5-82
70-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:45:22-79
71    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
71-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:46:5-83
71-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:46:22-80
72    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
72-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:49:5-88
72-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:49:22-85
73    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
73-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:50:5-89
73-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7a37c5ffb528e6a5fb2c4356e4fa9bb8/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:50:22-86
74    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
74-->[com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/e75f83e5e2bd4ad05880a4e7cced326b/transformed/installreferrer-2.2/AndroidManifest.xml:9:5-110
74-->[com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/e75f83e5e2bd4ad05880a4e7cced326b/transformed/installreferrer-2.2/AndroidManifest.xml:9:22-107
75
76    <application
76-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:21:3-41:17
77        android:name="com.alertcomm.app.MainApplication"
77-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:21:16-47
78        android:allowBackup="true"
78-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:21:162-188
79        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
79-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/3ff3b48c125af6c74caaf7ee6663189f/transformed/core-1.15.0/AndroidManifest.xml:28:18-86
80        android:debuggable="true"
81        android:extractNativeLibs="false"
82        android:icon="@mipmap/ic_launcher"
82-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:21:81-115
83        android:label="@string/app_name"
83-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:21:48-80
84        android:roundIcon="@mipmap/ic_launcher_round"
84-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:21:116-161
85        android:supportsRtl="true"
85-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:21:221-247
86        android:theme="@style/AppTheme"
86-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:21:189-220
87        android:usesCleartextTraffic="true" >
87-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml:6:18-53
88        <meta-data
88-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:22:5-139
89            android:name="com.google.firebase.messaging.default_notification_color"
89-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:22:16-87
90            android:resource="@color/notification_icon_color" />
90-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:22:88-137
91        <meta-data
91-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:23:5-135
92            android:name="com.google.firebase.messaging.default_notification_icon"
92-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:23:16-86
93            android:resource="@drawable/notification_icon" />
93-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:23:87-133
94        <meta-data
94-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:5-136
95            android:name="expo.modules.notifications.default_notification_color"
95-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:16-84
96            android:resource="@color/notification_icon_color" />
96-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:85-134
97        <meta-data
97-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:25:5-132
98            android:name="expo.modules.notifications.default_notification_icon"
98-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:25:16-83
99            android:resource="@drawable/notification_icon" />
99-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:25:84-130
100        <meta-data
100-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:26:5-83
101            android:name="expo.modules.updates.ENABLED"
101-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:26:16-59
102            android:value="false" />
102-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:26:60-81
103        <meta-data
103-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:27:5-105
104            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
104-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:27:16-80
105            android:value="ALWAYS" />
105-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:27:81-103
106        <meta-data
106-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:28:5-99
107            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
107-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:28:16-79
108            android:value="0" />
108-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:28:80-97
109
110        <activity
110-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:29:5-40:16
111            android:name="com.alertcomm.app.MainActivity"
111-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:29:15-43
112            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
112-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:29:44-134
113            android:exported="true"
113-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:29:256-279
114            android:launchMode="singleTask"
114-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:29:135-166
115            android:screenOrientation="portrait"
115-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:29:280-316
116            android:theme="@style/Theme.App.SplashScreen"
116-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:29:210-255
117            android:windowSoftInputMode="adjustResize" >
117-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:29:167-209
118            <intent-filter>
118-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:30:7-33:23
119                <action android:name="android.intent.action.MAIN" />
119-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:31:9-60
119-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:31:17-58
120
121                <category android:name="android.intent.category.LAUNCHER" />
121-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:32:9-68
121-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:32:19-66
122            </intent-filter>
123            <intent-filter data-generated="true" >
123-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:34:7-39:23
123-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:34:22-43
124                <action android:name="android.intent.action.VIEW" />
124-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:7-58
124-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:15-56
125
126                <data
126-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:18:7-37
127                    android:host="app.alertcomm1.com"
128                    android:scheme="https" />
128-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:18:13-35
129
130                <category android:name="android.intent.category.BROWSABLE" />
130-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:17:7-67
130-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:17:17-65
131                <category android:name="android.intent.category.DEFAULT" />
131-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:38:9-67
131-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:38:19-65
132            </intent-filter>
133        </activity>
134
135        <meta-data
135-->[:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-11:89
136            android:name="org.unimodules.core.AppLoader#react-native-headless"
136-->[:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-79
137            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
137-->[:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-86
138        <meta-data
138-->[:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:9-15:45
139            android:name="com.facebook.soloader.enabled"
139-->[:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-57
140            android:value="true" />
140-->[:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-33
141
142        <activity
142-->[com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:19:9-21:40
143            android:name="com.facebook.react.devsupport.DevSettingsActivity"
143-->[com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:20:13-77
144            android:exported="false" />
144-->[com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/AndroidManifest.xml:21:13-37
145
146        <provider
146-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:21:9-30:20
147            android:name="expo.modules.filesystem.FileSystemFileProvider"
147-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:22:13-74
148            android:authorities="com.alertcomm.app.FileSystemFileProvider"
148-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:23:13-74
149            android:exported="false"
149-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:24:13-37
150            android:grantUriPermissions="true" >
150-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:25:13-47
151            <meta-data
151-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:27:13-29:70
152                android:name="android.support.FILE_PROVIDER_PATHS"
152-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:28:17-67
153                android:resource="@xml/file_system_provider_paths" />
153-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:29:17-67
154        </provider>
155
156        <service
156-->[host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/c32eb368d89b8af8c6a684bc0b543217/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:11:9-14:56
157            android:name="expo.modules.location.services.LocationTaskService"
157-->[host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/c32eb368d89b8af8c6a684bc0b543217/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:12:13-78
158            android:exported="false"
158-->[host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/c32eb368d89b8af8c6a684bc0b543217/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:13:13-37
159            android:foregroundServiceType="location" />
159-->[host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/c32eb368d89b8af8c6a684bc0b543217/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:14:13-53
160        <service
160-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:11:9-17:19
161            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
161-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:12:13-91
162            android:exported="false" >
162-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:13:13-37
163            <intent-filter android:priority="-1" >
163-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:14:13-16:29
163-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:14:28-49
164                <action android:name="com.google.firebase.MESSAGING_EVENT" />
164-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:15:17-78
164-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:15:25-75
165            </intent-filter>
166        </service>
167
168        <receiver
168-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:19:9-31:20
169            android:name="expo.modules.notifications.service.NotificationsService"
169-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:20:13-83
170            android:enabled="true"
170-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:21:13-35
171            android:exported="false" >
171-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:22:13-37
172            <intent-filter android:priority="-1" >
172-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:23:13-30:29
172-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:23:28-49
173                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
173-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:24:17-88
173-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:24:25-85
174                <action android:name="android.intent.action.BOOT_COMPLETED" />
174-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:25:17-79
174-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:25:25-76
175                <action android:name="android.intent.action.REBOOT" />
175-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:26:17-71
175-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:26:25-68
176                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
176-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:27:17-82
176-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:27:25-79
177                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
177-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:28:17-82
177-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:28:25-79
178                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
178-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:29:17-84
178-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:29:25-81
179            </intent-filter>
180        </receiver>
181
182        <activity
182-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:33:9-40:75
183            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
183-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:34:13-92
184            android:excludeFromRecents="true"
184-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:35:13-46
185            android:exported="false"
185-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:36:13-37
186            android:launchMode="standard"
186-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:37:13-42
187            android:noHistory="true"
187-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:38:13-37
188            android:taskAffinity=""
188-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:39:13-36
189            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
189-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:40:13-72
190
191        <receiver
191-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:29:9-40:20
192            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
192-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:30:13-78
193            android:exported="true"
193-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:31:13-36
194            android:permission="com.google.android.c2dm.permission.SEND" >
194-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:32:13-73
195            <intent-filter>
195-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:33:13-35:29
196                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
196-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:34:17-81
196-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:34:25-78
197            </intent-filter>
198
199            <meta-data
199-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:37:13-39:40
200                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
200-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:38:17-92
201                android:value="true" />
201-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:39:17-37
202        </receiver>
203        <!--
204             FirebaseMessagingService performs security checks at runtime,
205             but set to not exported to explicitly avoid allowing another app to call it.
206        -->
207        <service
207-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:46:9-53:19
208            android:name="com.google.firebase.messaging.FirebaseMessagingService"
208-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:47:13-82
209            android:directBootAware="true"
209-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:48:13-43
210            android:exported="false" >
210-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:49:13-37
211            <intent-filter android:priority="-500" >
211-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:14:13-16:29
211-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:14:28-49
212                <action android:name="com.google.firebase.MESSAGING_EVENT" />
212-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:15:17-78
212-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:15:25-75
213            </intent-filter>
214        </service>
215        <service
215-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:54:9-63:19
216            android:name="com.google.firebase.components.ComponentDiscoveryService"
216-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:55:13-84
217            android:directBootAware="true"
217-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:32:13-43
218            android:exported="false" >
218-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:56:13-37
219            <meta-data
219-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:57:13-59:85
220                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
220-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:58:17-122
221                android:value="com.google.firebase.components.ComponentRegistrar" />
221-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:59:17-82
222            <meta-data
222-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:60:13-62:85
223                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
223-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:61:17-119
224                android:value="com.google.firebase.components.ComponentRegistrar" />
224-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/3e2ea98ccde974f562b3bd746689d36e/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:62:17-82
225            <meta-data
225-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:15:13-17:85
226                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
226-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:16:17-130
227                android:value="com.google.firebase.components.ComponentRegistrar" />
227-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:17:17-82
228            <meta-data
228-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:18:13-20:85
229                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
229-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:19:17-127
230                android:value="com.google.firebase.components.ComponentRegistrar" />
230-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d9ac7abbee3fc01e4af1dd52a78ad74f/transformed/firebase-installations-17.2.0/AndroidManifest.xml:20:17-82
231            <meta-data
231-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f41a0a0abe9082c2adadbc91827429e5/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
232                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
232-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f41a0a0abe9082c2adadbc91827429e5/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
233                android:value="com.google.firebase.components.ComponentRegistrar" />
233-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f41a0a0abe9082c2adadbc91827429e5/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
234            <meta-data
234-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
235                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
235-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:36:17-109
236                android:value="com.google.firebase.components.ComponentRegistrar" />
236-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:37:17-82
237            <meta-data
237-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/78a3c009a8dccc885176388c2c4753ad/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
238                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
238-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/78a3c009a8dccc885176388c2c4753ad/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
239                android:value="com.google.firebase.components.ComponentRegistrar" />
239-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/78a3c009a8dccc885176388c2c4753ad/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
240        </service>
241
242        <provider
242-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:29:9-37:20
243            android:name="androidx.startup.InitializationProvider"
243-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:30:13-67
244            android:authorities="com.alertcomm.app.androidx-startup"
244-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:31:13-68
245            android:exported="false" >
245-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:32:13-37
246            <meta-data
246-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:34:13-36:52
247                android:name="androidx.work.WorkManagerInitializer"
247-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:35:17-68
248                android:value="androidx.startup" />
248-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:36:17-49
249            <meta-data
249-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
250                android:name="androidx.emoji2.text.EmojiCompatInitializer"
250-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
251                android:value="androidx.startup" />
251-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
252            <meta-data
252-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
253                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
253-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
254                android:value="androidx.startup" />
254-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
255            <meta-data
255-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
256                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
256-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
257                android:value="androidx.startup" />
257-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
258        </provider>
259
260        <service
260-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:39:9-45:35
261            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
261-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:40:13-88
262            android:directBootAware="false"
262-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:41:13-44
263            android:enabled="@bool/enable_system_alarm_service_default"
263-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:42:13-72
264            android:exported="false" />
264-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:43:13-37
265        <service
265-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:46:9-52:35
266            android:name="androidx.work.impl.background.systemjob.SystemJobService"
266-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:47:13-84
267            android:directBootAware="false"
267-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:48:13-44
268            android:enabled="@bool/enable_system_job_service_default"
268-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:49:13-70
269            android:exported="true"
269-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:50:13-36
270            android:permission="android.permission.BIND_JOB_SERVICE" />
270-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:51:13-69
271        <service
271-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:53:9-59:35
272            android:name="androidx.work.impl.foreground.SystemForegroundService"
272-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:54:13-81
273            android:directBootAware="false"
273-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:55:13-44
274            android:enabled="@bool/enable_system_foreground_service_default"
274-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:56:13-77
275            android:exported="false" />
275-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:57:13-37
276
277        <receiver
277-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:61:9-66:35
278            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
278-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:62:13-88
279            android:directBootAware="false"
279-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:63:13-44
280            android:enabled="true"
280-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:64:13-35
281            android:exported="false" />
281-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:65:13-37
282        <receiver
282-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:67:9-77:20
283            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
283-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:68:13-106
284            android:directBootAware="false"
284-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:69:13-44
285            android:enabled="false"
285-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:70:13-36
286            android:exported="false" >
286-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:71:13-37
287            <intent-filter>
287-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:73:13-76:29
288                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
288-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:74:17-87
288-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:74:25-84
289                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
289-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:75:17-90
289-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:75:25-87
290            </intent-filter>
291        </receiver>
292        <receiver
292-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:78:9-88:20
293            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
293-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:79:13-104
294            android:directBootAware="false"
294-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:80:13-44
295            android:enabled="false"
295-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:81:13-36
296            android:exported="false" >
296-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:82:13-37
297            <intent-filter>
297-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:84:13-87:29
298                <action android:name="android.intent.action.BATTERY_OKAY" />
298-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:85:17-77
298-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:85:25-74
299                <action android:name="android.intent.action.BATTERY_LOW" />
299-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:86:17-76
299-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:86:25-73
300            </intent-filter>
301        </receiver>
302        <receiver
302-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:89:9-99:20
303            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
303-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:90:13-104
304            android:directBootAware="false"
304-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:91:13-44
305            android:enabled="false"
305-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:92:13-36
306            android:exported="false" >
306-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:93:13-37
307            <intent-filter>
307-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:95:13-98:29
308                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
308-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:96:17-83
308-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:96:25-80
309                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
309-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:97:17-82
309-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:97:25-79
310            </intent-filter>
311        </receiver>
312        <receiver
312-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:100:9-109:20
313            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
313-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:101:13-103
314            android:directBootAware="false"
314-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:102:13-44
315            android:enabled="false"
315-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:103:13-36
316            android:exported="false" >
316-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:104:13-37
317            <intent-filter>
317-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:106:13-108:29
318                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
318-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:107:17-79
318-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:107:25-76
319            </intent-filter>
320        </receiver>
321        <receiver
321-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:110:9-121:20
322            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
322-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:111:13-88
323            android:directBootAware="false"
323-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:112:13-44
324            android:enabled="false"
324-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:113:13-36
325            android:exported="false" >
325-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:114:13-37
326            <intent-filter>
326-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:116:13-120:29
327                <action android:name="android.intent.action.BOOT_COMPLETED" />
327-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:25:17-79
327-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/9d367840efb037b167b0f4cbb6c6cca0/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:25:25-76
328                <action android:name="android.intent.action.TIME_SET" />
328-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:118:17-73
328-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:118:25-70
329                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
329-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:119:17-81
329-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:119:25-78
330            </intent-filter>
331        </receiver>
332        <receiver
332-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:122:9-131:20
333            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
333-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:123:13-99
334            android:directBootAware="false"
334-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:124:13-44
335            android:enabled="@bool/enable_system_alarm_service_default"
335-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:125:13-72
336            android:exported="false" >
336-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:126:13-37
337            <intent-filter>
337-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:128:13-130:29
338                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
338-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:129:17-98
338-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:129:25-95
339            </intent-filter>
340        </receiver>
341        <receiver
341-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:132:9-142:20
342            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
342-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:133:13-78
343            android:directBootAware="false"
343-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:134:13-44
344            android:enabled="true"
344-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:135:13-35
345            android:exported="true"
345-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:136:13-36
346            android:permission="android.permission.DUMP" >
346-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:137:13-57
347            <intent-filter>
347-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:139:13-141:29
348                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
348-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:140:17-88
348-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/78cb694708a4d85159028346f880165f/transformed/work-runtime-2.9.1/AndroidManifest.xml:140:25-85
349            </intent-filter>
350        </receiver>
351
352        <service
352-->[androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7e6af7a886ab03e93fbcefb0f4519c4/transformed/room-runtime-2.5.0/AndroidManifest.xml:24:9-28:63
353            android:name="androidx.room.MultiInstanceInvalidationService"
353-->[androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7e6af7a886ab03e93fbcefb0f4519c4/transformed/room-runtime-2.5.0/AndroidManifest.xml:25:13-74
354            android:directBootAware="true"
354-->[androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7e6af7a886ab03e93fbcefb0f4519c4/transformed/room-runtime-2.5.0/AndroidManifest.xml:26:13-43
355            android:exported="false" />
355-->[androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7e6af7a886ab03e93fbcefb0f4519c4/transformed/room-runtime-2.5.0/AndroidManifest.xml:27:13-37
356
357        <meta-data
357-->[com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/837a13fdb801afa1460e9b1aae21e199/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:23:9-25:69
358            android:name="com.google.android.gms.version"
358-->[com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/837a13fdb801afa1460e9b1aae21e199/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:24:13-58
359            android:value="@integer/google_play_services_version" /> <!-- Needs to be explicitly declared on P+ -->
359-->[com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/837a13fdb801afa1460e9b1aae21e199/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:25:13-66
360        <uses-library
360-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:33:7-86
361            android:name="org.apache.http.legacy"
361-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:33:21-58
362            android:required="false" />
362-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/39679bd1e19ba60878362719e5ec6e86/transformed/play-services-maps-19.1.0/AndroidManifest.xml:33:59-83
363
364        <activity
364-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:9-173
365            android:name="com.google.android.gms.common.api.GoogleApiActivity"
365-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:19-85
366            android:exported="false"
366-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:146-170
367            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
367-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/bf4e5700f050532bbd59ead7b0c07184/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:86-145
368
369        <provider
369-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
370            android:name="com.google.firebase.provider.FirebaseInitProvider"
370-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:24:13-77
371            android:authorities="com.alertcomm.app.firebaseinitprovider"
371-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:25:13-72
372            android:directBootAware="true"
372-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:26:13-43
373            android:exported="false"
373-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:27:13-37
374            android:initOrder="100" />
374-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d860c615243dbf55873ae6448fc529e5/transformed/firebase-common-21.0.0/AndroidManifest.xml:28:13-36
375
376        <receiver
376-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
377            android:name="androidx.profileinstaller.ProfileInstallReceiver"
377-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
378            android:directBootAware="false"
378-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
379            android:enabled="true"
379-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
380            android:exported="true"
380-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
381            android:permission="android.permission.DUMP" >
381-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
382            <intent-filter>
382-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
383                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
383-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
383-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
384            </intent-filter>
385            <intent-filter>
385-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
386                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
386-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
386-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
387            </intent-filter>
388            <intent-filter>
388-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
389                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
389-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
389-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
390            </intent-filter>
391            <intent-filter>
391-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
392                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
392-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
392-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/44c936327e19f1139e00844984689fd4/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
393            </intent-filter>
394        </receiver>
395
396        <service
396-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
397            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
397-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
398            android:exported="false" >
398-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
399            <meta-data
399-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
400                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
400-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
401                android:value="cct" />
401-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/3f44a37c7456c771ca1923556c09af4d/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
402        </service>
403        <service
403-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
404            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
404-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
405            android:exported="false"
405-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
406            android:permission="android.permission.BIND_JOB_SERVICE" >
406-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
407        </service>
408
409        <receiver
409-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
410            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
410-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:33:13-132
411            android:exported="false" />
411-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/027d67677131eabd10f58abcc4dd8a4e/transformed/transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
412    </application>
413
414</manifest>
