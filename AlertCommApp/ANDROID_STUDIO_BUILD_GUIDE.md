# 🏗️ AlertComm - Android Studio Build Guide

## 🎯 **Complete Step-by-Step Guide for Native APK Build**

This guide will walk you through building a native Android APK using Android Studio - the most reliable method for React Native apps.

## ✅ **Prerequisites Verified**

Your system is properly configured:
- ✅ Android Studio installed
- ✅ Android SDK configured (`/Users/<USER>/Library/Android/sdk`)
- ✅ ADB available
- ✅ Project prebuild completed successfully

## 🚀 **Method 1: Android Studio GUI (Recommended)**

### **Step 1: Open Project in Android Studio**

1. **Launch Android Studio**
2. **Open Project:**
   - Click "Open an Existing Project"
   - Navigate to: `/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android`
   - Click "Open"

3. **Wait for Gradle Sync:**
   - Android Studio will automatically sync the project
   - Wait for "Gradle sync finished" message
   - This may take 2-5 minutes

### **Step 2: Configure Build Variant**

1. **Open Build Variants Panel:**
   - View → Tool Windows → Build Variants
   - Or click "Build Variants" tab at bottom-left

2. **Select Build Type:**
   - For testing: Select "debug"
   - For production: Select "release"

### **Step 3: Build APK**

1. **Build Menu:**
   - Build → Build Bundle(s) / APK(s) → Build APK(s)
   - Or use shortcut: `Ctrl+Shift+A` (Windows/Linux) or `Cmd+Shift+A` (Mac)

2. **Wait for Build:**
   - Build process will start
   - Progress shown in bottom panel
   - Takes 3-10 minutes depending on your machine

3. **Build Success:**
   - You'll see "BUILD SUCCESSFUL" message
   - Click "locate" link to find the APK file

### **Step 4: Locate Your APK**

APK will be generated at:
```
/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/outputs/apk/debug/app-debug.apk
```

## 🚀 **Method 2: Command Line (Alternative)**

If you prefer command line, here's the complete process:

### **Step 1: Navigate to Android Directory**
```bash
cd /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android
```

### **Step 2: Make Gradlew Executable**
```bash
chmod +x gradlew
```

### **Step 3: Clean Previous Builds**
```bash
./gradlew clean
```

### **Step 4: Build APK**
```bash
# For debug APK (testing)
./gradlew assembleDebug

# For release APK (production)
./gradlew assembleRelease
```

## 🔧 **Troubleshooting Common Issues**

### **Issue 1: Gradle Sync Failed**
**Solution:**
1. File → Invalidate Caches and Restart
2. Wait for re-indexing to complete
3. Try sync again

### **Issue 2: SDK Not Found**
**Solution:**
1. File → Project Structure → SDK Location
2. Set Android SDK Location to: `/Users/<USER>/Library/Android/sdk`
3. Apply and OK

### **Issue 3: Build Failed - Dependency Issues**
**Solution:**
1. In Android Studio: Build → Clean Project
2. Build → Rebuild Project
3. If still fails, run in terminal:
```bash
cd android
./gradlew clean
./gradlew assembleDebug --stacktrace
```

### **Issue 4: Metro Bundler Issues**
**Solution:**
1. Stop any running Metro processes
2. Clear Metro cache:
```bash
npx react-native start --reset-cache
```

## 📱 **Testing Your APK**

### **Install on Connected Device:**
```bash
# Check connected devices
adb devices

# Install APK
adb install app/build/outputs/apk/debug/app-debug.apk
```

### **Install on Emulator:**
1. Start Android Emulator from Android Studio
2. Drag and drop APK file onto emulator
3. Or use adb install command

## 🎯 **Build Variants Explained**

### **Debug Build:**
- **Purpose:** Testing and development
- **Size:** Larger (~30-40 MB)
- **Features:** Includes debugging tools
- **Signing:** Debug keystore (auto-generated)
- **Performance:** Slower, not optimized

### **Release Build:**
- **Purpose:** Production deployment
- **Size:** Smaller (~20-25 MB)
- **Features:** Optimized, no debug tools
- **Signing:** Release keystore (you need to create)
- **Performance:** Optimized for production

## 🔐 **Creating Release Keystore (For Production)**

### **Step 1: Generate Keystore**
```bash
keytool -genkey -v -keystore alertcomm-release-key.keystore -alias alertcomm-key-alias -keyalg RSA -keysize 2048 -validity 10000
```

### **Step 2: Configure in build.gradle**
Add to `android/app/build.gradle`:
```gradle
android {
    signingConfigs {
        release {
            storeFile file('alertcomm-release-key.keystore')
            storePassword 'YOUR_STORE_PASSWORD'
            keyAlias 'alertcomm-key-alias'
            keyPassword 'YOUR_KEY_PASSWORD'
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
            // ... other settings
        }
    }
}
```

## 📊 **Expected Build Times**

- **First Build:** 5-15 minutes (downloads dependencies)
- **Subsequent Builds:** 2-5 minutes
- **Clean Builds:** 3-8 minutes

## ✅ **Success Indicators**

### **In Android Studio:**
- ✅ "BUILD SUCCESSFUL" in Build Output
- ✅ Green checkmark in Build panel
- ✅ APK file created in outputs folder

### **In Terminal:**
- ✅ "BUILD SUCCESSFUL in X minutes"
- ✅ No error messages
- ✅ APK file exists at expected location

## 🎉 **Next Steps After Successful Build**

1. **Test APK on Device:**
   - Install and test all features
   - Verify emergency response functionality
   - Test with different user roles

2. **Distribute to Team:**
   - Share APK file with emergency response team
   - Provide installation instructions
   - Set up user accounts and permissions

3. **Production Deployment:**
   - Create release keystore
   - Build release APK
   - Upload to Google Play Console

## 📞 **Need Help?**

If you encounter any issues:

1. **Check Android Studio Logs:**
   - View → Tool Windows → Build
   - Look for specific error messages

2. **Run with Verbose Output:**
```bash
./gradlew assembleDebug --stacktrace --info
```

3. **Common Solutions:**
   - Restart Android Studio
   - Clean and rebuild project
   - Check internet connection
   - Verify Android SDK installation

---

**🎯 Ready to build your AlertComm APK? Follow the steps above and you'll have a working native Android app!**
