#!/bin/bash

echo "🔧 Fixing AlertComm Build Issues..."

# Fix dependency conflicts
echo "📦 Fixing dependency conflicts..."

# Add to android/app/build.gradle
cat >> android/app/build.gradle << 'EOF'

android {
    configurations.all {
        exclude group: 'com.android.support', module: 'support-compat'
        exclude group: 'com.android.support', module: 'support-core-utils'
        exclude group: 'com.android.support', module: 'support-core-ui'
        exclude group: 'com.android.support', module: 'support-fragment'
        exclude group: 'com.android.support', module: 'support-annotations'
    }
}
EOF

# Clean and rebuild
echo "🧹 Cleaning build cache..."
cd android
./gradlew clean

echo "🔨 Building APK..."
./gradlew assembleDebug

# Check if APK was created
APK_PATH="app/build/outputs/apk/debug/app-debug.apk"
if [ -f "$APK_PATH" ]; then
    echo "✅ APK built successfully!"
    echo "📱 APK location: android/$APK_PATH"
    
    # Copy to root directory
    cp "$APK_PATH" "../AlertComm-Emergency-Response.apk"
    echo "📱 APK copied to: AlertComm-Emergency-Response.apk"
    
    # Show file info
    ls -lh "../AlertComm-Emergency-Response.apk"
    
    echo ""
    echo "🎉 SUCCESS! Your AlertComm APK is ready!"
    echo "📱 Install AlertComm-Emergency-Response.apk on your Android device"
    
else
    echo "❌ APK build failed!"
    echo "Try using EAS Build instead:"
    echo "1. eas login"
    echo "2. eas build --platform android --profile preview"
fi

echo ""
echo "🚀 Build process complete!"
