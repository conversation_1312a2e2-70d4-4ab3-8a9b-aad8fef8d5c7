# 📱 AlertComm - Complete Build Guide

## 🎯 **Project Analysis**

**AlertComm Emergency Response** is a professional React Native + Expo app with:

- **Framework**: Expo SDK 53 + React Native 0.79.5
- **Features**: Emergency coordination, real-time chat, GPS tracking, role-based access
- **Architecture**: Modern React Native with 15 screens, Socket.IO, JWT auth
- **Size**: ~20MB with all dependencies

## 🚀 **Build Commands**

### **📱 Android APK Build**

#### **Method 1: EAS Build (Recommended)**
```bash
# Install EAS CLI
npm install -g eas-cli

# Login to Expo account
eas login

# Build APK for testing
eas build --platform android --profile preview

# Build for production
eas build --platform android --profile production

# Build development version
eas build --platform android --profile development
```

#### **Method 2: Local Build**
```bash
# Set environment variables
export ANDROID_HOME=/Users/<USER>/Library/Android/sdk
export ANDROID_NDK_HOME=/Users/<USER>/Library/Android/sdk/ndk/27.1.********

# Build APK
npx expo run:android --variant debug

# Alternative: Direct Gradle build
cd android
./gradlew assembleDebug
```

#### **Method 3: Fix Build Issues (If local build fails)**
```bash
# Run the fix script
chmod +x fix-build-issues.sh
./fix-build-issues.sh
```

### **📱 iOS Build**

#### **Method 1: EAS Build (Recommended)**
```bash
# Build for iOS Simulator
eas build --platform ios --profile development

# Build for TestFlight
eas build --platform ios --profile preview

# Build for App Store
eas build --platform ios --profile production
```

#### **Method 2: Local Build (Requires Xcode)**
```bash
# Build for iOS
npx expo run:ios

# Build for specific simulator
npx expo run:ios --simulator "iPhone 15 Pro"

# Build for device (requires Apple Developer account)
npx expo run:ios --device
```

### **🌐 Web Build**
```bash
# Development server
npx expo start --web

# Production build
npx expo export:web

# Serve production build locally
npx serve dist
```

## 🔧 **Build Profiles (eas.json)**

```json
{
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal",
      "android": { "buildType": "apk" }
    },
    "preview": {
      "distribution": "internal",
      "android": { "buildType": "apk" }
    },
    "production": {
      "android": { "buildType": "app-bundle" }
    }
  }
}
```

## 📋 **Build Outputs**

### **Android:**
- **Development APK**: `~25MB` (includes dev tools)
- **Preview APK**: `~20MB` (optimized for testing)
- **Production AAB**: `~15MB` (for Google Play Store)

### **iOS:**
- **Development IPA**: `~30MB` (includes dev tools)
- **Preview IPA**: `~25MB` (optimized for TestFlight)
- **Production IPA**: `~20MB` (for App Store)

### **Web:**
- **Bundle Size**: `~5MB` (optimized for web)
- **Assets**: `~2MB` (fonts, images, icons)

## 🎯 **Recommended Build Process**

### **For Development/Testing:**
1. **Quick Test**: `npx expo start` (use Expo Go app)
2. **APK Build**: `eas build --platform android --profile preview`
3. **iOS Build**: `eas build --platform ios --profile development`

### **For Production:**
1. **Android**: `eas build --platform android --profile production`
2. **iOS**: `eas build --platform ios --profile production`
3. **Web**: `npx expo export:web`

## 🔍 **Troubleshooting**

### **Common Issues:**

**"Duplicate class" errors:**
- Run `./fix-build-issues.sh`
- Or use EAS Build (handles automatically)

**NDK version mismatch:**
- Install correct NDK: `sdkmanager "ndk;27.1.********"`
- Or use EAS Build

**Build timeout:**
- Use EAS Build (cloud building)
- Increase local build timeout

**Memory issues:**
- Close other applications
- Use EAS Build for complex builds

### **Build Requirements:**

**For Local Android Build:**
- Android Studio
- Android SDK (API 35)
- NDK 27.1.********
- Java 17+
- 8GB+ RAM

**For Local iOS Build:**
- macOS
- Xcode 15+
- iOS SDK 17+
- Apple Developer account (for device builds)

**For EAS Build:**
- Expo account (free)
- Internet connection
- No local setup required

## 📱 **Installation Guide**

### **Android APK:**
1. Enable "Unknown Sources" in Android settings
2. Transfer APK to device
3. Tap APK file to install
4. Grant permissions (Location, Notifications, Camera)

### **iOS IPA:**
1. Install via TestFlight (preview builds)
2. Or install via Xcode (development builds)
3. Trust developer certificate in Settings

## 🎉 **Success Checklist**

- [ ] APK/IPA builds successfully
- [ ] App installs on device
- [ ] Login screen appears
- [ ] Can connect to server
- [ ] All features work
- [ ] Notifications received
- [ ] GPS location works
- [ ] Chat functionality works

## 📞 **Support**

**Build Issues:**
- Use EAS Build for complex dependency issues
- Check Android Studio and Xcode versions
- Verify SDK and NDK installations

**App Issues:**
- Check server connectivity
- Verify permissions granted
- Test with different devices
- Check logs for errors

---

## 🚀 **Quick Start**

**Fastest way to get APK:**
```bash
eas login
eas build --platform android --profile preview
```

**Download link will be provided when build completes!**

---

**Built with ❤️ for Emergency Response Teams**
